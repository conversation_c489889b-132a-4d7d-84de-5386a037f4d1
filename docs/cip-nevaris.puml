@startuml CIP
!pragma layout smetana

frame Nevaris {
rectangle REST  {
rectangle "/Kostenstellenliste" as FetchCostcentersFromNevarisRoute
rectangle "/debitoren" as EnrichDebtorWithNevarisIdRoute
rectangle "/adressen" as EnrichAddressWithNevarisIdRoute
}
rectangle Filesystem {
file "BBA-CSV-File" as NevarisBBACSVRecord
file "Fibu-CSV-File" as NevarisFiBuCSVRecord
}
}

frame CIP-Nevaris-Adapter {
circle " " as kostenStellenRoute
circle " " as NevarisBBACsvImportFileProducer
circle " " as NevarisFiBuImportCSVFileProducerRoute
queue Queue as messageBroker
database sql [
    kostenstellen
    ----
    kostenstellen.consumer_timestamps
    ----
    debitoren
    ---
    adressen
]


FetchCostcentersFromNevarisRoute --> kostenStellenRoute: NevarisCostCenterList
sql --> kostenStellenRoute : kostenstellen-timestamps
kostenStellenRoute --> messageBroker : Kostenstelle
kostenStellenRoute --> sql
EnrichDebtorWithNevarisIdRoute --> sql: debitoren.nevaris_key
EnrichAddressWithNevarisIdRoute --> sql: adressen.nevaris_key
messageBroker --> NevarisBBACsvImportFileProducer : BBARecord
NevarisBBACsvImportFileProducer --> NevarisBBACSVRecord : List<NevarisBBACSVRecord>
messageBroker --> NevarisFiBuImportCSVFileProducerRoute : FiBuRecord
NevarisFiBuImportCSVFileProducerRoute --> NevarisFiBuCSVRecord: List<NevarisFiBuCSVRecord>
@enduml