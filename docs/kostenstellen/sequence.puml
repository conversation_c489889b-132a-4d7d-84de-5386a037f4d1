@startuml NEVARISCostcenterJSONConsumer
actor <PERSON><PERSON><PERSON><PERSON> as source
box Middleware Connector + DB
participant NEVARISAdapter as sourceAdapter
database PostgreSQL as db
endbox
queue RabbitMQ as messageBroker

sourceAdapter -> db: getLastTimestamp
activate sourceAdapter
activate db
return Timestamp
sourceAdapter -> source
activate source
note left: OData GET nevaris:1234/kostenstellen
return Kostenstellen
loop all new "costcenters"
	sourceAdapter -> sourceAdapter: transformCostcenter
	sourceAdapter -> messageBroker: Exchange: "costcenters", routingKey="costcenters.nevaris.<NEVARIS_MANDANT_NR>"
	sourceAdapter -> db: updateLastTimestamp
	activate db
	return
end
@enduml