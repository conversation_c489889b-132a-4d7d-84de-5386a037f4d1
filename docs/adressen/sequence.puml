@startuml NEVARISAddressAdapter
queue RabbitMQ as messageBroker
box Middleware Connector + DB
participant NEVARISAdapter as destinationAdapter
database PostgreSQL as db
endbox
participant NEVARIS as destination

messageBroker -> destinationAdapter: Exchange "addresses", routingKey="#"
activate messageBroker
activate destinationAdapter
note right
  {{
      start
      :process incoming "Adresse";
      if(debitor == true) then (true)
        if(nevarisDebitorNr == <empty>) then (true)
          :generate AddressNo (9XXXXXX);
          :insert Address in Address Cache with generated `AddressNo`;
        else (false)
          :Check Cached AddressDatabase by `nevarisDebitorNr`;
            if(AddressFound) then (true)
              :update Address in Address Cache;
            else (false)
              :insert Address in Address Cache with given `nevarisDebitorNr`;
            endif
        endif
        stop
      else (false)
        if(nevarisAdressNr == <empty>) then (true)
          :generate AddressNo (8XXXXXX);
          :insert Address in Address Cache;
        else (false)
          :Check Cached AddressDatabase by `nevarisAdressNr`;
            if(AddressFound) then (true)
              :update Address in Address Cache;
            else (false)
              :insert Address in Address Cache with given `nevarisAdressNr`;
            endif
        endif
        stop
      endif
  }}
end note
destinationAdapter -> db: execute above ruleset
activate db
return Id
destinationAdapter -> destination: sendAddressToNevaris
activate destination
note right: TODO: POST http://nevaris:1234/addresses
return
return ack
deactivate messageBroker
...
destination -> destinationAdapter: callback
activate destination
note left: PATCH http://************:8789/rest/v1/adressen/{ADDRESS_ID}
activate destinationAdapter
destinationAdapter -> db: enrich with NEVARIS information
activate db
return
destinationAdapter -> messageBroker: Exchange: "addresses.ack", routingKey="<SYSTEM_NAME>.<MANDANT_NR>"
return
@enduml

@startuml
start
:Search for File;
if(Is in local cache?) then (yes)
    :Finish;
    stop
else (no)
    :Check Cached SearchObjectToNodeTb;
        if(Find in SearchObjectToNodeTb) then (yes)
            :Send refresh req. SO to SN;
        else(No)
            :Send SO query to closest SymbolToNodeCacheTb match;
            if(Symbol in Table) then (yes)
                switch(What role am I?)
                    case(SearchNode)
                        :Send out Query to SN and get SearchObject;
                    case(FileNode)
                        :Return results;
                endswitch
            else(No)
                switch(What role am I?)
                    case(client)
                        :Send to Node with closest result;
                    case(SearchNode)
                        :Perform alg. for tablesym. creation;
                endswitch
@enduml