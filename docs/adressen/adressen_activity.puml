@startuml Geschaeftspartnerlogik
start

if (adressArt==Kunde?) then (ja)
   :debitor = true;
else (nein)
    :debitor = false;
endif

:Debitorenübersicht abrufen;
if (<PERSON><PERSON><PERSON> ist in unserer Datenbank?) then (ja)
    :Datenbankeintrag aktualisieren;
    if (<PERSON><PERSON><PERSON> in Debitorenübersicht) then (ja)
    else (nein)
        : PATCH Adresscache;
        if (<PERSON><PERSON><PERSON> in Adressuebersicht) then (ja)
            : PATCH Adressuebersicht;
            if (adressArt==Kunde?) then (ja)
                :POST Debitorenübersicht;
            endif
         endif
    endif
else (nein)
    : Adressnr generieren + POST an Adresscache;
endif

stop
@enduml