@startuml
queue RabbitMQ as messageBroker
box Middleware Connector
participant NEVARISAdapter as destinationAdapter
endbox
participant CSVF<PERSON> as output
actor User as user
actor <PERSON><PERSON><PERSON><PERSON> as destination


messageBroker -> destinationAdapter: Exchange "fibu", routingKey="fibu.*"
activate messageBroker
activate destinationAdapter
loop timeout = 1500ms
destinationAdapter -> destinationAdapter: transformFiBuRecord
end
destinationAdapter -> destinationAdapter: aggregateAndMarshalFiBuRecords
destinationAdapter -> output: write CSVFile to <FILE_SHARE>
return ack
deactivate messageBroker
...
output -> user:
activate user
user -> destination: import File
activate destination
return
deactivate user
@enduml