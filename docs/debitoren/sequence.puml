@startuml NEVARISAddressAdapter
queue RabbitMQ as messageBroker
box Middleware Connector + DB
participant NEVARISAdapter as destinationAdapter
database PostgreSQL as db
endbox
participant NEVARIS as destination

messageBroker -> destinationAdapter: Exchange "debtors", routingKey="#"
activate messageBroker
activate destinationAdapter
destinationAdapter -> db: upsert Debtor
activate db
return Id
destinationAdapter -> destination: sendDebtorToNevaris
activate destination
note right: TODO: POST http://nevaris:1234/debtors
return
return ack
deactivate messageBroker
...
destination -> destinationAdapter: callback
activate destination
note left: PATCH http://************:8789/rest/v1/debitoren/{DEBTOR_ID}
activate destinationAdapter
destinationAdapter -> db: enrich with NEVARIS information
activate db
return
destinationAdapter -> messageBroker: Exchange: "debtors.ack", routingKey="<SYSTEM_NAME>.<MANDANT_NR>"
return
@enduml