# Build

pool:
  name: Shared

trigger:
  tags:
    include: 
      - v*.*.*

variables:
  IMAGE_TAG: '$(Build.SourceVersion)'
  REPOSITORY: systeex-cip-nevaris
  REGISTRY: 'romeisie.azurecr.io\/$(REPOSITORY)'

stages:
- stage: Build
  displayName: Build 
  jobs:
    - job: Build_App
      displayName: Build App
      steps:
      - task: Bash@3
        displayName: Set IMAGE_TAG and VERSION
        name: set_variables
        inputs:
         targetType: 'inline'
         script: | 
          if [[ "$(Build.SourceBranchName)" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              echo "Branch matches version pattern: $(Build.SourceBranchName)"
              echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]$(Build.SourceBranchName)"
              echo "##vso[task.setvariable variable=VERSION;isOutput=true]$(Build.SourceBranchName)"
          else
              echo "Branch does not match version pattern, using SourceVersion"
              echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]$(Build.SourceVersion)"
              echo "##vso[task.setvariable variable=VERSION;isOutput=true]$(Build.SourceVersion)"
          fi
      - task: AzureKeyVault@2
        displayName: Get Secrets
        inputs:
          azureSubscription: 'romeisIE SysteexCIP'
          KeyVaultName: 'sharedVariables'
          SecretsFilter: '*'
          RunAsPreJob: true

      - task: CmdLine@2
        displayName: Envsubst
        env:
            MAVENACCESSTOKEN: $(MAVENACCESSTOKEN)
        inputs:
          script: | 
            envsubst < $(Build.SourcesDirectory)/.devOps/maven-settings.xml > $(Build.SourcesDirectory)/.devOps/maven-settings-processed.xml

      - task: Bash@3
        displayName: Install SDKMAN! and Load SDKs from .sdkmanrc
        inputs:
          targetType: 'inline'
          script: |
            curl -s "https://get.sdkman.io" | bash
            source "$HOME/.sdkman/bin/sdkman-init.sh"

            cd $(Build.SourcesDirectory)

            if [ -f .sdkmanrc ]; then
              echo "Using SDK versions from .sdkmanrc"
              sdk env install
            else
              echo ".sdkmanrc not found!"
            fi

      - task: Bash@3
        displayName: Build with Maven
        inputs:
          targetType: 'inline'
          script: |
            source "$HOME/.sdkman/bin/sdkman-init.sh"
            cd $(Build.SourcesDirectory)
            sdk env
            ./mvnw --batch-mode clean verify --settings $(Build.SourcesDirectory)/.devOps/maven-settings-processed.xml

      - task: Docker@2
        displayName: Build Image
        inputs:
          command: 'build'
          containerRegistry: registry
          repository: $(REPOSITORY)
          Dockerfile: '**/Dockerfile'
          tags: $(set_variables.IMAGE_TAG)
          arguments: --build-arg JAR_FILE=target/*.jar

      - task: Docker@2
        displayName: Push Image
        inputs:
          command: push
          containerRegistry: registry
          repository: $(REPOSITORY)
          tags: $(set_variables.IMAGE_TAG)

    - job: publish_artifacts
      displayName: Publish Build Artifacts
      variables:
        IMAGE_TAG: $[ dependencies.Build_App.outputs['set_variables.IMAGE_TAG'] ]  
        VERSION: $[ dependencies.Build_App.outputs['set_variables.VERSION'] ]  
      dependsOn: 
        - Build_App
      steps:
        - task: CmdLine@2
          displayName: Prepare docker-compose
          inputs:
              script: |
                sed "s/\${VERSION}/$(Build.SourceVersion)/g" docker-compose.dist.yaml > docker-compose.vers.yaml
                sed -i "s/\${ADAPTER_DOCKER_TAG}/$(REGISTRY):$(IMAGE_TAG)/g" docker-compose.vers.yaml
        - task: PublishBuildArtifacts@1
          inputs:
            PathtoPublish: 'docker-compose.vers.yaml'
            ArtifactName: 'docker-compose'
            publishLocation: 'Container'