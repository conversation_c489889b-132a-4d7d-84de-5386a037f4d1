# Test
trigger: none

pool:
  name: Shared

stages:
- stage: Compile
  displayName: Compile Java Project
  jobs:
    - job: Compile
      displayName: Compile and Package Jar
      steps:
      - task: AzureKeyVault@2
        inputs:
          azureSubscription: 'romeisIE SysteexCIP'
          KeyVaultName: 'sharedVariables'
          SecretsFilter: '*'
          RunAsPreJob: true
      - task: CmdLine@2
        displayName: Envsubst
        env:
            MAVENACCESSTOKEN: $(MAVENACCESSTOKEN)
        inputs:
                script: 'envsubst < $(Build.SourcesDirectory)/.devOps/maven-settings.xml > $(Build.SourcesDirectory)/.devOps/maven-settings-processed.xml'
      - task: Bash@3
        displayName: Install SDKMAN! and Load SDKs from .sdkmanrc
        inputs:
          targetType: 'inline'
          script: |
            curl -s "https://get.sdkman.io" | bash
            source "$HOME/.sdkman/bin/sdkman-init.sh"

            cd $(Build.SourcesDirectory)

            if [ -f .sdkmanrc ]; then
              echo "Using SDK versions from .sdkmanrc"
              sdk env install
            else
              echo ".sdkmanrc not found!"
            fi

      - task: Bash@3
        displayName: Build with Maven
        inputs:
          targetType: 'inline'
          script: |
            source "$HOME/.sdkman/bin/sdkman-init.sh"
            cd $(Build.SourcesDirectory)
            sdk env
            ./mvnw --batch-mode clean verify --settings $(Build.SourcesDirectory)/.devOps/maven-settings-processed.xml



