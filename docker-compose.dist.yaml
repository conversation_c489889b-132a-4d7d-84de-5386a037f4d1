name: nevaris

services:
  adapter:
    image: ${ADAPTER_DOCKER_TAG}
    restart: unless-stopped
    environment:
      - "MESSAGE_BROKER_HOSTNAME=rabbitmq"
      - "MESSAGE_BROKER_USERNAME=${RABBITMQ_USERNAME}"
      - "MESSAGE_BROKER_PASSWORD=${RABBITMQ_PASSWORD}"
      - "SPRING_DATASOURCE_URL=****************************************"
      - "SPRING_DATASOURCE_USERNAME=postgres"
      - "SPRING_DATASOURCE_PASSWORD=${POSTGRES_PASSWORD}"
      - "NEVARIS_HOST=${NEVARIS_HOST}"
      - "NEVARIS_USERNAME=${NEVARIS_USERNAME}"
      - "NEVARIS_PASSWORD=${NEVARIS_PASSWORD}"
      - "NEVARIS_DEFAULT_BASEPATH=${NEVARIS_DEFAULT_BASEPATH}"
      - "NEVARIS_CUSTOM_BASEPATH_V2=${NEVARIS_CUSTOM_BASEPATH_V2}"
      - "POLL_CRON_ARTIKEL=${POLL_CRON_ARTIKEL}"
      - "POLL_CRON_KOSTENSTELLE=${POLL_CRON_KOSTENSTELLE}"
      - "VERSION=${VERSION}"
      - "MANDANT_MAPPING=${MANDANT_MAPPING}"
      - "MANDANT_MAPPING_ARTIKEL=${MANDANT_MAPPING_ARTIKEL}"
      - "MANDANT_MAPPING_KOSTENSTELLE=${MANDANT_MAPPING_KOSTENSTELLE}"
      - "MANDANT_MAPPING_NUMMER=${MANDANT_MAPPING_NUMMER}"
      - "MANDANT_DEBITORNR_MAPPING=${MANDANT_DEBITORNR_MAPPING}"
      - "COPY_MANDANT=${COPY_MANDANT}"
      - "POLL_CRON_PLZCODES=${POLL_CRON_PLZCODES}"
      - "NEVARIS_PLZCODES_MANDANT=${NEVARIS_PLZCODES_MANDANT}"
      - "SPRING_APPLICATION_NAME=${ADAPTER_NAME}"
      - "POLL_CRON_ARTIKELLIEFERANT=${POLL_CRON_ARTIKELLIEFERANT}"
      - "POLL_CRON_ARTIKELSATZMARKIERUNG=${POLL_CRON_ARTIKELSATZMARKIERUNG}"
      - "POLL_CRON_ARTIKELSATZMARKIERUNGWERT=${POLL_CRON_ARTIKELSATZMARKIERUNGWERT}"
    volumes:
      - "${NEVARIS_OUTPUT_BBA}:/app/data/out/bba"
      - "${NEVARIS_OUTPUT_FIBU}:/app/data/out/fibu"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "7"
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: "4G"
        reservations:
          cpus: "0.25"
          memory: "1G"
    ports:
      - "8789:8789"
    networks:
      - default
      - messaging_rabbitmq

  postgres:
    image: postgres:15.3-alpine3.18
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_PASSWORD: "${POSTGRES_PASSWORD}"

  adminer:
    image: adminer
    restart: unless-stopped
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    ports:
      - "8080:8080"

volumes:
  postgres_data: { }

networks:
  messaging_rabbitmq:
    external: true
