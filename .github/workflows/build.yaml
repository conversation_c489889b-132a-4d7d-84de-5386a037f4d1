name: Build

on:
  # Releases will trigger a production deployment.
  release:
    types:
      - released
  # Pushes on `main` will trigger a staging deployment, on other branches no deployment.
  push: {}

jobs:
  prepare:
    runs-on: ubuntu-22.04
    permissions:
      contents: read
    outputs:
      version: ${{ fromJson(steps.prepare.outputs.result).version }}
      environment: ${{ from<PERSON><PERSON>(steps.prepare.outputs.result).environment }}
      dockerImage: ${{ steps.prepare-docker.outputs.dockerImage }}
    steps:
      - uses: actions/checkout@v3

      - name: Prepare
        uses: actions/github-script@v6
        id: prepare
        with:
          script: |
            const prepare = require('./.github/scripts/prepare.js');
            return prepare(context);

      - name: Pre-process docker tag
        id: docker-tag
        uses: ASzc/change-string-case-action@v2
        with:
          string: ghcr.io/${{ github.repository }}:${{ fromJson(steps.prepare.outputs.result).version }}

      - name: Create docker tag
        id: prepare-docker
        run: |
          echo "dockerImage=${{ steps.docker-tag.outputs.lowercase }}" >> $GITHUB_OUTPUT

      - name: Print outputs
        run: |
          echo "version: ${{ from<PERSON><PERSON>(steps.prepare.outputs.result).version }}"
          echo "environment: ${{ fromJson(steps.prepare.outputs.result).environment }}"
          echo "dockerImage: ${{ steps.prepare-docker.outputs.dockerImage }}"

  build:
    runs-on: ubuntu-22.04
    needs: prepare
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: liberica
          java-version: 17
          cache: maven

      - name: Install dependencies
        run: ./mvnw --batch-mode --settings .github/maven-settings.xml dependency:resolve -U
        env:
          # Access to the Maven repository only works with a personal access token.
          # That's why my name appears here. This is crazy...
          MAVEN_USERNAME: ${{ vars.MAVEN_USERNAME }}
          MAVEN_ACCESS_TOKEN: ${{ secrets.MAVEN_ACCESS_TOKEN }}

      - name: Build and test
        run: ./mvnw --batch-mode clean verify

      - name: Login to docker registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build docker image
        run: |
          docker build \
            --build-arg "JAR_FILE=target/*.jar" \
            --build-arg "VERSION=${{ needs.prepare.outputs.version }}" \
            --tag ${{ needs.prepare.outputs.dockerImage }} \
            .

      - name: Push docker image
        run: |
          docker push ${{ needs.prepare.outputs.dockerImage }}

  deploy:
    if: ${{ needs.prepare.outputs.environment }}
    runs-on:
      group: systeex-cip
      labels: ${{ needs.prepare.outputs.environment }}
    needs:
      - prepare
      - build
    permissions:
      contents: read
      packages: read
    environment: ${{ needs.prepare.outputs.environment }}
    concurrency: ${{ needs.prepare.outputs.environment }}
    steps:
      - uses: actions/checkout@v3

      - name: Login to docker registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Prepare docker compose spec
        run: envsubst < docker-compose.dist.yaml > docker-compose.yaml
        env:
          ADAPTER_DOCKER_TAG: ${{ needs.prepare.outputs.dockerImage }}
          RABBITMQ_USERNAME: ${{ vars.RABBITMQ_USERNAME }}
          RABBITMQ_PASSWORD: ${{ secrets.RABBITMQ_PASSWORD }}
          MANDANT_MAPPING: ${{ vars.MANDANT_MAPPING }}
          POLL_CRON_KOSTENSTELLE: ${{ vars.POLL_CRON_KOSTENSTELLE }}
          MANDANT_MAPPING_KOSTENSTELLE: ${{ vars.MANDANT_MAPPING_KOSTENSTELLE }}
          POLL_CRON_ARTIKEL: ${{ vars.POLL_CRON_ARTIKEL }}
          MANDANT_MAPPING_ARTIKEL: ${{ vars.MANDANT_MAPPING_ARTIKEL }}
          NEVARIS_HOST: ${{ vars.NEVARIS_HOST }}
          NEVARIS_USERNAME: ${{ vars.NEVARIS_USERNAME }}
          NEVARIS_PASSWORD: ${{ secrets.NEVARIS_PASSWORD }}
          NEVARIS_DEFAULT_BASEPATH: ${{ vars.NEVARIS_DEFAULT_BASEPATH }}
          NEVARIS_CUSTOM_BASEPATH_V2: ${{ vars.NEVARIS_CUSTOM_BASEPATH_V2 }}
          VERSION: ${{ github.run_number }}
          POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
          MANDANT_MAPPING_NUMMER: ${{ vars.MANDANT_MAPPING_NUMMER }}
          NEVARIS_OUTPUT_BBA: ${{ vars.NEVARIS_OUTPUT_BBA }}
          NEVARIS_OUTPUT_FIBU: ${{ vars.NEVARIS_OUTPUT_FIBU }}
          MANDANT_DEBITORNR_MAPPING: ${{ vars.MANDANT_DEBITORNR_MAPPING }}
          COPY_MANDANT: ${{ vars.COPY_MANDANT }}
          POLL_CRON_PLZCODES: ${{ vars.POLL_CRON_PLZCODES }}
          NEVARIS_PLZCODES_MANDANT: ${{ vars.NEVARIS_PLZCODES_MANDANT }}

      - name: Copy files
        run: mkdir -p /opt/docker/systeex-cip-nevaris && cp docker-compose.yaml /opt/docker/systeex-cip-nevaris/docker-compose.yaml

      - name: Deploy
        run: docker compose --project-name nevaris --file /opt/docker/systeex-cip-nevaris/docker-compose.yaml up --detach
