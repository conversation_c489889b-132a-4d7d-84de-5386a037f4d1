module.exports = (context) => {
  // Timestamp format: yyyymmdd-hhmmss
  const timestamp = new Date()
    .toISOString()
    // remove milliseconds and time zone
    .replace(/\.\d+Z$/, "")
    // remove separators
    .replace(/[-:]/g, "")
    // replace date-time separator
    .replace("T", "-");

  if ("release" in context.payload) {
    const { release } = context.payload;

    if (!release.draft && !release.prerelease) {
      console.log("Release published --> production");
      return {
        timestamp,
        version: release.name,
        environment: "production",
      };
    }

    console.log("Unpublished release --> no deployment");
    return {
      timestamp,
      version: timestamp,
      environment: undefined,
    };
  }

  if (context.payload.ref === "refs/heads/main") {
    console.log("Push to main --> staging");
    return {
      timestamp,
      version: timestamp,
      environment: "staging",
    };
  }

  console.log("Push to other branch --> no deployment");
  return {
    timestamp,
    version: timestamp,
    environment: undefined,
  };
};
