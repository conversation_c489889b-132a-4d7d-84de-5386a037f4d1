{"@odata.context": "http://test-hu-nvweb.ad.systeex.de:8048/INTEGRATION/ODataV4/$metadata#Company('10%20SBS')/Kostenstellenliste", "value": [{"@odata.etag": "W/\"JzE5Ozc3NTAwOTcyNDAzNjM0NTM4ODAxOzAwOyc=\"", "Control2": "1000000", "Name": "Umlagekostenstelle", "Bezeichnung2": "", "TmpVorschauID": "", "Bemerkung": false, "Suchbegriff": "UMLAGEKOSTENSTELLE", "Auftragswert": 0, "Nachtragswert": 0, "Konsolidierungscode": "", "Gemeindeschlüssel_AGS": "", "Bezeichnung_AGS": "", "Ist_Baustelle": false, "Adresse_KSt_Ort": "", "Adresse_KSt_PLZ": "", "Adresse_KSt_Bundesland": "", "Adresse_KSt_Ländercode": "", "Text_Bundesland": "", "BAB_Zeilendefinition": "", "Angebotsprojekt": "", "Auftragsprojekt": "", "Auftragseingang": "0001-01-01", "Adressnr_der_Kostenstelle": "", "Adressnr_des_Auftraggebers": "", "Debitor": "", "Kundennr_beim_Auftraggeber": "", "Empfangsstelle": "", "Bauleiter": "", "Bauleitergruppe": "", "Oberbauleiter": "", "Adressnr_Bauleiter_Bauherrn": "", "Adressat_Bauleiter_Bauherrn": "", "Adressnr_des_Bauherrn": "", "Adressat_des_Bauherrn": "", "Adressnr_des_Architekten": "", "Adressat_des_Architekten": "", "Adressnr_Polier": "", "Adressat_Polier": "", "Vertragsart": "", "Gewährleistung_in_Monaten": 0, "Bürgschaftsbetrag": 0, "Vertragsbed_Vorspann": 0, "Vertragsbed_Nachspann": 0, "Vertragsbed_Erläuterung": 0, "Zahlungsbedingung": "", "Nachlässe": false, "Niederlassung": "HU", "Ergebniszuordnung": "ZENTRALE", "Sparte": "ZENTRALE", "Typ": "VWL", "Gruppe": "", "Gruppenstufe": "", "Gruppenstufe2": "", "Gruppenstufe3": "", "Gruppenstufe4": "", "Gruppenstufe5": "", "Statistikgruppe": "", "U_Bahn_Steuerpflicht_AT": false, "Gemeindecode_Kommunalsteuer_AT": "", "Stammkostenstelle_für": " ", "Zusammenzählungsart": "Kostenstelle", "Zusammenzählung": "", "MIS_ID": "", "Baubeginndatum": "0001-01-01", "Vorauss_Bauendedatum": "0001-01-01", "Bauendedatum": "0001-01-01", "Bauabnahmedatum": "0001-01-01", "Bauzeit_in_Monaten": 0, "Drucken": true, "Drucken_nur_wenn_Bewegung": true, "Drucken_nach_Bauende": true, "Auf_Ergebnislisten_drucken": false, "Vorjahreswert_ermitteln": false, "Gesamtwert_ermitteln": false, "Umlage_nach_Bauende": false, "Ist_Bauträger": false, "Schlußgerechnet": false, "Datum_Schlußgerechnet": "0001-01-01", "Abgerechnet_Percent": 0, "Fertigstellung_Percent": 0, "Datum_Fertigstellung": "0001-01-01", "Fertig": false, "MwSt_Satz": 0, "Buchen_mit_BAS": " ", "Buchen_mit_Gerät": " ", "Gerätekondition_intern": "", "Gerätekondition_extern": "", "Gerätekondition_int_Leistung": "", "Gerätekondition_ext_Leistung": "", "BAL_alt_intern": false, "BAL_alt_extern": false, "BAL_Percent_neu_intern": 100, "BAL_Percent_neu_extern": 100, "BAL_Percent_neu_Rücknahme_intern": 100, "BAL_Percent_neu_Rücknahme_extern": 100, "BAL_Percent_gebraucht_intern": 100, "BAL_Percent_gebraucht_extern": 100, "BAL_Percent_gebraucht_Rückn_intern": 100, "BAL_Percent_gebraucht_Rückn_extern": 100, "BAL_Percent_Ladepauschale_intern": 0, "BAL_Percent_Ladepauschale_extern": 0, "ARGE_Kostenstelle": false, "Fremdunternehmen": false, "Bauhof": false, "Gerätebestand": true, "Höhenmeter": 0, "CUP": "", "CIG": "", "Kostenart_mit_Menge": " ", "DB_III_Percent_Prognose": 0, "Einfache_Fahrtzeit": 0, "ARG_pflichtig": false, "WWW_Adresse_URL": "", "EasyRapport": " ", "Externe_Geräteergebnisse": false, "ZHS_Loggerbezeichnung_1": "", "ZHS_Loggerbezeichnung_2": "", "ZHS_Aktiv_auf_Logger": false, "ZHS_Kostenstellengruppe": "", "ZHS_Unterkostenstellen": "A", "ZHS_Lohnarten": "A", "ZHS_GEO_Status": "V", "ZHS_GEO_Position": "", "ZHS_StdUKST": "", "Gesperrt": "<PERSON><PERSON>", "Sperrhinweis": "", "Gültig_ab": "0001-01-01", "Gültig_bis": "0001-01-01", "Neuanlagesystem": "", "Neuanlagebenutzer": "SYSTEEX\\ALISA.THOENI", "Neuanlagedatum": "2023-06-13", "Neuanlagezeit": "15:03:53.813", "Änderungssystem": "", "Änderungsbenutzer": "SYSTEEX\\ALISA.THOENI", "Änderungsdatum": "2023-06-13", "Änderungszeit": "15:05:45.977", "Mandantenfilter": "10 SBS", "Auftragsperiodenfilter": ""}, {"@odata.etag": "W/\"JzE5OzEyMTgxMTQ3NDc0NjU0MTYxOTQxOzAwOyc=\"", "Control2": "100005000", "Name": "Verwaltung -ALT", "Bezeichnung2": "", "TmpVorschauID": "", "Bemerkung": false, "Suchbegriff": "VERWALTUNG-ALT", "Auftragswert": 0, "Nachtragswert": 0, "Konsolidierungscode": "", "Gemeindeschlüssel_AGS": "", "Bezeichnung_AGS": "", "Ist_Baustelle": false, "Adresse_KSt_Ort": "", "Adresse_KSt_PLZ": "", "Adresse_KSt_Bundesland": "", "Adresse_KSt_Ländercode": "", "Text_Bundesland": "", "BAB_Zeilendefinition": "", "Angebotsprojekt": "", "Auftragsprojekt": "", "Auftragseingang": "0001-01-01", "Adressnr_der_Kostenstelle": "", "Adressnr_des_Auftraggebers": "", "Debitor": "", "Kundennr_beim_Auftraggeber": "", "Empfangsstelle": "", "Bauleiter": "", "Bauleitergruppe": "", "Oberbauleiter": "", "Adressnr_Bauleiter_Bauherrn": "", "Adressat_Bauleiter_Bauherrn": "", "Adressnr_des_Bauherrn": "", "Adressat_des_Bauherrn": "", "Adressnr_des_Architekten": "", "Adressat_des_Architekten": "", "Adressnr_Polier": "", "Adressat_Polier": "", "Vertragsart": "", "Gewährleistung_in_Monaten": 0, "Bürgschaftsbetrag": 0, "Vertragsbed_Vorspann": 0, "Vertragsbed_Nachspann": 0, "Vertragsbed_Erläuterung": 0, "Zahlungsbedingung": "", "Nachlässe": false, "Niederlassung": "HU", "Ergebniszuordnung": "ZENTRALE", "Sparte": "ZENTRALE", "Typ": "VWL", "Gruppe": "", "Gruppenstufe": "", "Gruppenstufe2": "", "Gruppenstufe3": "", "Gruppenstufe4": "", "Gruppenstufe5": "", "Statistikgruppe": "", "U_Bahn_Steuerpflicht_AT": false, "Gemeindecode_Kommunalsteuer_AT": "", "Stammkostenstelle_für": " ", "Zusammenzählungsart": "Kostenstelle", "Zusammenzählung": "", "MIS_ID": "", "Baubeginndatum": "0001-01-01", "Vorauss_Bauendedatum": "0001-01-01", "Bauendedatum": "2023-05-01", "Bauabnahmedatum": "0001-01-01", "Bauzeit_in_Monaten": 0, "Drucken": true, "Drucken_nur_wenn_Bewegung": true, "Drucken_nach_Bauende": true, "Auf_Ergebnislisten_drucken": false, "Vorjahreswert_ermitteln": false, "Gesamtwert_ermitteln": false, "Umlage_nach_Bauende": false, "Ist_Bauträger": false, "Schlußgerechnet": false, "Datum_Schlußgerechnet": "0001-01-01", "Abgerechnet_Percent": 0, "Fertigstellung_Percent": 0, "Datum_Fertigstellung": "0001-01-01", "Fertig": false, "MwSt_Satz": 0, "Buchen_mit_BAS": " ", "Buchen_mit_Gerät": " ", "Gerätekondition_intern": "", "Gerätekondition_extern": "", "Gerätekondition_int_Leistung": "", "Gerätekondition_ext_Leistung": "", "BAL_alt_intern": false, "BAL_alt_extern": false, "BAL_Percent_neu_intern": 100, "BAL_Percent_neu_extern": 100, "BAL_Percent_neu_Rücknahme_intern": 100, "BAL_Percent_neu_Rücknahme_extern": 100, "BAL_Percent_gebraucht_intern": 100, "BAL_Percent_gebraucht_extern": 100, "BAL_Percent_gebraucht_Rückn_intern": 100, "BAL_Percent_gebraucht_Rückn_extern": 100, "BAL_Percent_Ladepauschale_intern": 0, "BAL_Percent_Ladepauschale_extern": 0, "ARGE_Kostenstelle": false, "Fremdunternehmen": false, "Bauhof": false, "Gerätebestand": true, "Höhenmeter": 0, "CUP": "", "CIG": "", "Kostenart_mit_Menge": " ", "DB_III_Percent_Prognose": 0, "Einfache_Fahrtzeit": 0, "ARG_pflichtig": false, "WWW_Adresse_URL": "", "EasyRapport": " ", "Externe_Geräteergebnisse": false, "ZHS_Loggerbezeichnung_1": "", "ZHS_Loggerbezeichnung_2": "", "ZHS_Aktiv_auf_Logger": false, "ZHS_Kostenstellengruppe": "", "ZHS_Unterkostenstellen": "A", "ZHS_Lohnarten": "A", "ZHS_GEO_Status": "V", "ZHS_GEO_Position": "", "ZHS_StdUKST": "", "Gesperrt": "<PERSON><PERSON>", "Sperrhinweis": "", "Gültig_ab": "0001-01-01", "Gültig_bis": "0001-01-01", "Neuanlagesystem": "", "Neuanlagebenutzer": "SYSTEEX\\A_BS", "Neuanlagedatum": "2023-03-22", "Neuanlagezeit": "10:51:19.657", "Änderungssystem": "", "Änderungsbenutzer": "SYSTEEX\\ALISA.THOENI", "Änderungsdatum": "2023-06-22", "Änderungszeit": "12:08:04.2", "Mandantenfilter": "10 SBS", "Auftragsperiodenfilter": ""}]}