package de.systeex.cip.nevaris.bba.csv_file_producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.bba.csv_file_producer.domain.NevarisBBACSVRecord;
import de.systeex.cip.nevaris.bba.csv_file_producer.routing.NevarisBBACsvImportFileProducer;
import org.apache.camel.Exchange;
import org.apache.camel.RoutesBuilder;
import org.apache.camel.builder.AdviceWith;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.converter.stream.InputStreamCache;
import org.apache.camel.test.junit5.CamelTestSupport;
import org.apache.camel.test.spring.junit5.UseAdviceWith;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import static org.junit.jupiter.api.Assertions.assertEquals;

@UseAdviceWith
public class NevarisBBAImportCSVFileProducerRouteTest extends CamelTestSupport {

    // We override this method with the routes to be tested
    @Override
    protected RoutesBuilder createRouteBuilder() throws Exception {
        RoutesBuilder result = new NevarisBBACsvImportFileProducer();
        var mandantMapping = new HashMap<String, String>();
        mandantMapping.put("10", "10 SBS");
        ReflectionTestUtils.setField(result, "mandantMapping", mandantMapping);
        return result;
    }

    @Override
    public boolean isUseAdviceWith() {
        return true;
    }

    // We write a simple JUnit test case
    @Test
    public void testTransformRoute() throws Exception {

        ObjectMapper jsonStringComparisonObjectMapper = new ObjectMapper();

        String bbaRecordJsonString1 = "{\"b\": \"B\", \"nevarisMandantNr\": \"10\", \"bnr\": \"59-23-04-L0001\", \"bdatum\": \"2023-04-03\", \"monat\": \"2023-05-01\", \"sh\": \"S\", \"kst\": \"105000110\", \"gkst\": \"10105002\", \"koa\": \"60100\", \"betrag\": 487.5, \"text\": \"Hannemann Mark 2023-04-03_LOH\", \"menge\": 9.75, \"me\": \"Std\", \"bw\": \"1\"}";
        String bbaRecordJsonString2 = "{\"b\": \"B\", \"nevarisMandantNr\": \"10\", \"bnr\": \"59-23-04-L0002\", \"bdatum\": \"2023-04-03\", \"monat\": \"2023-05-01\", \"sh\": \"S\", \"kst\": \"105000110\", \"gkst\": \"10105002\", \"koa\": \"60100\", \"betrag\": 425, \"text\": \"Quast Grigori 2023-04-03_LOH\", \"menge\": 8.5, \"me\": \"Std\", \"bw\": \"1\"}";
        String nevarisBBARecordJson1 = "{\"belegDatum\":\"03.04.23\",\"belegNr\":\"59-23-04-L0001\",\"betrag\":\"487,50\",\"menge\":\"9,750\",\"einheit\":\"STD\",\"kostenartBelasten\":\"60100\",\"kostenartEntlasten\":\"60100\",\"stornoInMonat\":\"\",\"erweiterteKontierung\":\"\",\"buchungstext\":\"Hannemann Mark 2023-04-03_LOH\",\"positionsNr\":\"\",\"vorgang\":\"\",\"belegNr2Bedeutung\":\"\",\"belegNr2\":\"\",\"dokumentId\":\"\",\"mandantKStBelasten\":\"10\",\"mandantKStEntlasten\":\"10\",\"artikel\":\"\",\"balArt\":\"\",\"bal\":\"\",\"mandantDesWertbezugs\":\"\",\"geraet\":\"\",\"zusatzkennung\":\"\",\"personalNr\":\"\",\"anlagegut\":\"\",\"wertbezug\":\"\",\"basBelasten\":\"\",\"basEntlasten\":\"\",\"mandantKtrBelasten\":\"\",\"kostentraegerBelasten\":\"\",\"mandantKtrEntlasten\":\"\",\"kostentraegerEntlasten\":\"\",\"projekt\":\"\",\"lvPosition\":\"\",\"debitorKreditor\":\"\",\"debKredNr\":\"\",\"mandantDerHerkunft\":\"\",\"herkunftsNr\":\"\",\"transaktionsNr\":\"\",\"neu\":\"\",\"ruecknahme\":\"\",\"abrechnungsmonat\":\"01.05.23\",\"kstEntlasten\":\"10105002\",\"kstBelasten\":\"105000110\"}";
        String nevarisBBARecordJson2 = "{\"belegDatum\":\"03.04.23\",\"belegNr\":\"59-23-04-L0002\",\"betrag\":\"425,00\",\"menge\":\"8,500\",\"einheit\":\"STD\",\"kostenartBelasten\":\"60100\",\"kostenartEntlasten\":\"60100\",\"stornoInMonat\":\"\",\"erweiterteKontierung\":\"\",\"buchungstext\":\"Quast Grigori 2023-04-03_LOH\",\"positionsNr\":\"\",\"vorgang\":\"\",\"belegNr2Bedeutung\":\"\",\"belegNr2\":\"\",\"dokumentId\":\"\",\"mandantKStBelasten\":\"10\",\"mandantKStEntlasten\":\"10\",\"artikel\":\"\",\"balArt\":\"\",\"bal\":\"\",\"mandantDesWertbezugs\":\"\",\"geraet\":\"\",\"zusatzkennung\":\"\",\"personalNr\":\"\",\"anlagegut\":\"\",\"wertbezug\":\"\",\"basBelasten\":\"\",\"basEntlasten\":\"\",\"mandantKtrBelasten\":\"\",\"kostentraegerBelasten\":\"\",\"mandantKtrEntlasten\":\"\",\"kostentraegerEntlasten\":\"\",\"projekt\":\"\",\"lvPosition\":\"\",\"debitorKreditor\":\"\",\"debKredNr\":\"\",\"mandantDerHerkunft\":\"\",\"herkunftsNr\":\"\",\"transaktionsNr\":\"\",\"neu\":\"\",\"ruecknahme\":\"\",\"abrechnungsmonat\":\"01.05.23\",\"kstEntlasten\":\"10105002\",\"kstBelasten\":\"105000110\"}";
        String bbaAggregated = "[" + nevarisBBARecordJson1 + "," + nevarisBBARecordJson2 + "]";

        var mockOutput = getMockEndpoint("mock:output");
        var mockOutputFile = getMockEndpoint("mock:outputFile");

        AdviceWith.adviceWith(context, "fetchBBARecordFromBroker10", a ->
        {
            a.replaceFromWith("direct:rabbitmq");
            a.weaveByToUri("rabbitmq:*").replace().to(mockOutput.getEndpointUri());
        });

        AdviceWith.adviceWith(context,"fetchBBARecordAggregatedFromBroker10", a ->
        {
            a.replaceFromWith("direct:rabbitmqAggregated");

            a.interceptSendToEndpoint("file:*")
                    .skipSendToOriginalEndpoint()
                    .to(mockOutputFile.getEndpointUri());
        });

        context.start();

        template.sendBody("direct:rabbitmq", bbaRecordJsonString1);
        template.sendBody("direct:rabbitmq", bbaRecordJsonString2);

        mockOutput.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context, 3, TimeUnit.SECONDS);
        var jsonOutput = mockOutput.getExchanges().get(0).getIn().getBody(String.class);
        assertEquals(jsonStringComparisonObjectMapper.readTree(bbaAggregated),jsonStringComparisonObjectMapper.readTree(jsonOutput));

        template.sendBody("direct:rabbitmqAggregated", bbaAggregated);
        mockOutputFile.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context);

        List<Exchange> exchanges = mockOutputFile.getExchanges();

        var inputStreamCache = exchanges.get(0).getIn().getBody(InputStreamCache.class);
        var data = new BufferedReader(new InputStreamReader(inputStreamCache))
                .lines().collect(Collectors.joining("\n"));
        List<String> lines = Arrays.asList(data.split("\n"));
        assertEquals("\"03.04.23\";\"59-23-04-L0001\";\"105000110\";\"10105002\";\"487,50\";\"9,750\";\"STD\";\"60100\";\"60100\";\"\";\"\";\"Hannemann Mark 2023-04-03_LOH\";\"\";\"\";\"\";\"\";\"\";\"10\";\"10\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"01.05.23\"", lines.get(0));
        assertEquals("\"03.04.23\";\"59-23-04-L0002\";\"105000110\";\"10105002\";\"425,00\";\"8,500\";\"STD\";\"60100\";\"60100\";\"\";\"\";\"Quast Grigori 2023-04-03_LOH\";\"\";\"\";\"\";\"\";\"\";\"10\";\"10\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"\";\"01.05.23\"", lines.get(1));
        context.stop();
    }

    @Test
    public void testAggregationStrategyRoute() throws Exception {
        String bbaRecordJsonString1 = "{\"b\": \"B\", \"nevarisMandantNr\": \"10\", \"bnr\": \"59-23-04-L0001\", \"bdatum\": \"2023-04-03\", \"monat\": \"2023-05-01\", \"sh\": \"S\", \"kst\": \"105000110\", \"gkst\": \"10105002\", \"koa\": \"60100\", \"betrag\": 487.5, \"text\": \"Hannemann Mark 2023-04-03_LOH\", \"menge\": 9.75, \"me\": \"Std\", \"bw\": \"1\"}";
        String bbaRecordJsonString2 = "{\"b\": \"B\", \"nevarisMandantNr\": \"10\", \"bnr\": \"59-23-04-L0002\", \"bdatum\": \"2023-04-03\", \"monat\": \"2023-05-01\", \"sh\": \"S\", \"kst\": \"105000110\", \"gkst\": \"10105002\", \"koa\": \"60100\", \"betrag\": 425, \"text\": \"Quast Grigori 2023-04-03_LOH\", \"menge\": 8.5, \"me\": \"Std\", \"bw\": \"1\"}";
        String bbaRecordJsonString3 = "{\"b\": \"B\", \"nevarisMandantNr\": \"10\", \"bnr\": \"59-23-04-L0003\", \"bdatum\": \"2023-04-04\", \"monat\": \"2023-05-01\", \"sh\": \"S\", \"kst\": \"105000110\", \"gkst\": \"10105002\", \"koa\": \"60100\", \"betrag\": 150, \"text\": \"Quast Grigori 2023-04-04_LOH\", \"menge\": 3, \"me\": \"Std\", \"bw\": \"1\"}";
        var mockOutput = getMockEndpoint("mock:output");

        AdviceWith.adviceWith(context, "fetchBBARecordFromBroker10", a ->
        {
            a.replaceFromWith("direct:rabbitmq");
            a.weaveByToUri("rabbitmq:*").replace().to(mockOutput.getEndpointUri());
        });

        //nicht notwendig, aber beseitigt Warnungen:
        AdviceWith.adviceWith(context, "fetchBBARecordAggregatedFromBroker10", a ->
                a.replaceFromWith("direct:rabbitmqAggregated"));

        context.start();

        template.sendBody("direct:rabbitmq", bbaRecordJsonString1);
        template.sendBody("direct:rabbitmq", bbaRecordJsonString2);
        Thread.sleep(5000);
        template.sendBody("direct:rabbitmq", bbaRecordJsonString3);
        mockOutput.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context, 5, TimeUnit.SECONDS);

        List<Exchange> exchanges = mockOutput.getExchanges();

        var jsonString = exchanges.get(0).getIn().getBody(String.class);
        ObjectMapper objectMapper = new ObjectMapper();
        NevarisBBACSVRecord[] records = objectMapper.readValue(jsonString, NevarisBBACSVRecord[].class);
        assertEquals(2, records.length);
        context.stop();
    }
}
