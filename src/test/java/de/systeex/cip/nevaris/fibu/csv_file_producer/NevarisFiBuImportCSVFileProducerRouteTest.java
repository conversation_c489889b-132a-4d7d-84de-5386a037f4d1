package de.systeex.cip.nevaris.fibu.csv_file_producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.fibu.csv_file_producer.domain.NevarisFiBuCSVRecord;
import de.systeex.cip.nevaris.fibu.csv_file_producer.routing.NevarisFiBuImportCSVFileProducerRoute;
import org.apache.camel.Exchange;
import org.apache.camel.RoutesBuilder;
import org.apache.camel.builder.AdviceWith;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.converter.stream.InputStreamCache;
import org.apache.camel.test.junit5.CamelTestSupport;
import org.apache.camel.test.spring.junit5.UseAdviceWith;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

@UseAdviceWith
public class NevarisFiBuImportCSVFileProducerRouteTest extends CamelTestSupport {

    // We override this method with the routes to be tested
    @Override
    protected RoutesBuilder createRouteBuilder() throws Exception {
        RoutesBuilder result = new NevarisFiBuImportCSVFileProducerRoute();
        HashMap<String, String> mandantMapping = new HashMap<>();
        mandantMapping.put("10","10");
        ReflectionTestUtils.setField(result, "mandantMapping", mandantMapping);
        ReflectionTestUtils.setField(result, "mandantMapToNevarisNummer", mandantMapping);
        return result;
    }

    @Override
    public boolean isUseAdviceWith() {
        return true;
    }

    // We write a simple JUnit test case
    @Test
    public void testTransformRoute() throws Exception {
        String fibuRecordJsonString1 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700030\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":1190,\"habenBetrag\":null,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47660\",\"kostenart\":\"47660\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700030\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":1000,\"mwStBetrag\":-190,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"19\",\"mwStSatz\":\"19\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuRecordJsonString2 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700031\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":2140,\"habenBetrag\":null,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47660\",\"kostenart\":\"47660\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700031\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":2000,\"mwStBetrag\":-140,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"7\",\"mwStSatz\":\"7\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuRecordJsonString3 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700032\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":3000,\"habenBetrag\":null,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47601\",\"kostenart\":\"47601\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700032\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":3000,\"mwStBetrag\":0,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"0\",\"mwStSatz\":\"0\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuRecordJsonString4 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700032\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":3000,\"habenBetrag\":null,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47601\",\"kostenart\":\"47601\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700032\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":3000,\"mwStBetrag\":0,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"0\",\"mwStSatz\":\"0\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuRecordJsonString5 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700034\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":214,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47642\",\"kostenart\":\"47642\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700034\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":200,\"habenBetrag\":null,\"mwStBetrag\":14,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"7\",\"mwStSatz\":\"7\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuRecordJsonString6 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700035\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":300,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"60000\",\"kostenart\":\"60000\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700035\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":300,\"habenBetrag\":null,\"mwStBetrag\":0,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"0\",\"mwStSatz\":\"0\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuAggregatedJsonString = "[{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"31001\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700030\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":\"1190\",\"habenbetrag\":null,\"mwstBetrag\":null,\"mwstSatz\":\"\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"47660\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700030\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":null,\"habenbetrag\":\"1000\",\"mwstBetrag\":\"-190\",\"mwstSatz\":\"19\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"47660\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"19\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"31001\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700031\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":\"2140\",\"habenbetrag\":null,\"mwstBetrag\":null,\"mwstSatz\":\"\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"47660\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700031\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":null,\"habenbetrag\":\"2000\",\"mwstBetrag\":\"-140\",\"mwstSatz\":\"7\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"47660\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"7\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"31001\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700032\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":\"3000\",\"habenbetrag\":null,\"mwstBetrag\":null,\"mwstSatz\":\"\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"47601\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700032\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":null,\"habenbetrag\":\"3000\",\"mwstBetrag\":\"0\",\"mwstSatz\":\"0\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"47601\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"0\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"31001\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700032\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":\"3000\",\"habenbetrag\":null,\"mwstBetrag\":null,\"mwstSatz\":\"\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"47601\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700032\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":null,\"habenbetrag\":\"3000\",\"mwstBetrag\":\"0\",\"mwstSatz\":\"0\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"47601\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"0\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"31001\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700034\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":null,\"habenbetrag\":\"214\",\"mwstBetrag\":null,\"mwstSatz\":\"\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"47642\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700034\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":\"200\",\"habenbetrag\":null,\"mwstBetrag\":\"14\",\"mwstSatz\":\"7\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"47642\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"7\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"31001\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700035\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":null,\"habenbetrag\":\"300\",\"mwstBetrag\":null,\"mwstSatz\":\"\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null},{\"kontoart\":\"SACHKONTO\",\"kontonr\":\"60000\",\"buchungsdatum\":[2023,7,1],\"belegart\":\"\",\"belegnr\":\"70-23-KM0700035\",\"externeBelegnr\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"zlgBedingungscode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontierfaehigerBetrag\":null,\"waehrungscode\":null,\"sollbetrag\":\"300\",\"habenbetrag\":null,\"mwstBetrag\":\"0\",\"mwstSatz\":\"0\",\"buchungsgruppe\":null,\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"menge\":null,\"mengeneinheit\":null,\"kostenstelle\":\"********\",\"kostentraeger\":null,\"kostenart\":\"60000\",\"geraet\":null,\"artikel\":null,\"bas\":null,\"lvPosition\":null,\"positionsnr\":null,\"dokumentId\":\"\",\"versicherungsgesellschaft\":null,\"risikonr\":null,\"verkEinkaeufercode\":null,\"niederlassung\":null,\"projektnr\":null,\"abwarten\":null,\"abweichenderMandantKurzbez\":null,\"belegnr2\":null,\"zusatzkennung\":null,\"abgrenzung\":null,\"mahnstufe\":null,\"mahnstopp\":null,\"ausgleichMitBelegart\":null,\"ausgleichMitBelegnr\":null,\"zahlungsform\":null,\"bankkontoFuerZahlungen\":null,\"herkunftscode\":null,\"mwstGruppe\":\"0\",\"zielmandantBeleguebertr\":null,\"zielkostenstelleBeleguebertr\":\"10\",\"adressnr\":null,\"debitorKreditorAnzVorgang\":null,\"anzVorgang\":null,\"belegartAnzVorgang\":null,\"mandant\":null,\"abwAdressnr\":null,\"segment\":null,\"skontoDatumII\":null,\"skontoProzentII\":null,\"infofeld\":null,\"leistungsdatum\":null}]";

        var mockOutput = getMockEndpoint("mock:output");
        var mockOutputFile = getMockEndpoint("mock:outputFile");

        AdviceWith.adviceWith(context, "fetchCSVFiBuRecordFromBroker10", a ->
        {
            a.replaceFromWith("direct:rabbitmq");
            a.weaveByToUri("rabbitmq:*").replace().to(mockOutput.getEndpointUri());
        });

        AdviceWith.adviceWith(context, "fetchNevarisFibuCSVListFromBroker10", a -> {
            a.replaceFromWith("direct:rabbitmqAggregated");
            a.weaveByToUri("file:*").replace().to(mockOutputFile.getEndpointUri());
        });

        context.start();


        template.sendBody("direct:rabbitmq", fibuRecordJsonString1);
        template.sendBody("direct:rabbitmq", fibuRecordJsonString2);
        template.sendBody("direct:rabbitmq", fibuRecordJsonString3);
        template.sendBody("direct:rabbitmq", fibuRecordJsonString4);
        template.sendBody("direct:rabbitmq", fibuRecordJsonString5);
        template.sendBody("direct:rabbitmq", fibuRecordJsonString6);
        mockOutput.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context, 8, TimeUnit.SECONDS);

        template.sendBody("direct:rabbitmqAggregated", fibuAggregatedJsonString);
        mockOutputFile.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context);

        List<Exchange> exchanges = mockOutputFile.getExchanges();

        var byteArrayInputStream = new ByteArrayInputStream(exchanges.get(0).getIn().getBody(byte[].class));
        var data = new BufferedReader(new InputStreamReader(byteArrayInputStream))
                .lines().collect(Collectors.joining("\n"));
//        TODO:  fix Tests: Datei ist jetzt xlsx, nicht mehr CSV
//        List<String> lines = Arrays.asList(data.split("\n"));
//        assertEquals("SACHKONTO|31001|01.07.2023||70-23-KM0700030||21.07.2023|L+G 07_2023|||||||1190|||||||||********||||||||||||||||||||||||||||10|||||||||||", lines.get(0));
//        assertEquals("SACHKONTO|47660|01.07.2023||70-23-KM0700030||21.07.2023|L+G 07_2023||||||||1000|-190|19||DE|LOH|||********||47660||||||||||||||||||||||||19||10|||||||||||", lines.get(1));
//        assertEquals("SACHKONTO|31001|01.07.2023||70-23-KM0700031||21.07.2023|L+G 07_2023|||||||2140|||||||||********||||||||||||||||||||||||||||10|||||||||||", lines.get(2));
//        assertEquals("SACHKONTO|47660|01.07.2023||70-23-KM0700031||21.07.2023|L+G 07_2023||||||||2000|-140|7||DE|LOH|||********||47660||||||||||||||||||||||||7||10|||||||||||", lines.get(3));
//        assertEquals("SACHKONTO|31001|01.07.2023||70-23-KM0700032||21.07.2023|L+G 07_2023|||||||3000|||||||||********||||||||||||||||||||||||||||10|||||||||||", lines.get(4));
//        assertEquals("SACHKONTO|47601|01.07.2023||70-23-KM0700032||21.07.2023|L+G 07_2023||||||||3000|0|0||DE|LOH|||********||47601||||||||||||||||||||||||0||10|||||||||||", lines.get(5));
//        assertEquals("SACHKONTO|31001|01.07.2023||70-23-KM0700032||21.07.2023|L+G 07_2023|||||||3000|||||||||********||||||||||||||||||||||||||||10|||||||||||", lines.get(6));
//        assertEquals("SACHKONTO|47601|01.07.2023||70-23-KM0700032||21.07.2023|L+G 07_2023||||||||3000|0|0||DE|LOH|||********||47601||||||||||||||||||||||||0||10|||||||||||", lines.get(7));
//        assertEquals("SACHKONTO|31001|01.07.2023||70-23-KM0700034||21.07.2023|L+G 07_2023||||||||214||||||||********||||||||||||||||||||||||||||10|||||||||||", lines.get(8));
//        assertEquals("SACHKONTO|47642|01.07.2023||70-23-KM0700034||21.07.2023|L+G 07_2023|||||||200||14|7||DE|LOH|||********||47642||||||||||||||||||||||||7||10|||||||||||", lines.get(9));
//        assertEquals("SACHKONTO|31001|01.07.2023||70-23-KM0700035||21.07.2023|L+G 07_2023||||||||300||||||||********||||||||||||||||||||||||||||10|||||||||||", lines.get(10));
//        assertEquals("SACHKONTO|60000|01.07.2023||70-23-KM0700035||21.07.2023|L+G 07_2023|||||||300||0|0||DE|LOH|||********||60000||||||||||||||||||||||||0||10|||||||||||", lines.get(11));
        context.stop();
    }

    @Test
    public void testAggregationStrategyRoute() throws Exception {
        String fibuRecordJsonString1 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700030\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":1190,\"habenBetrag\":null,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47660\",\"kostenart\":\"47660\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700030\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":1000,\"mwStBetrag\":-190,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"19\",\"mwStSatz\":\"19\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuRecordJsonString2 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700031\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":2140,\"habenBetrag\":null,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47660\",\"kostenart\":\"47660\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700031\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":2000,\"mwStBetrag\":-140,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"7\",\"mwStSatz\":\"7\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        String fibuRecordJsonString3 = "{\"debitorBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"31001\",\"kostenart\":\"\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700032\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":3000,\"habenBetrag\":null,\"mwStBetrag\":null,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"\",\"produktbuchungsgruppe\":\"\",\"mwStGruppe\":\"\",\"mwStSatz\":\"\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"umsatzBuchungen\":[{\"mandant\":\"10\",\"belegart\":\"\",\"kontoart\":\"SACHKONTO\",\"kontonummer\":\"47601\",\"kostenart\":\"47601\",\"buchungsdatum\":[2023,7,1],\"belegnummer\":\"70-23-KM0700032\",\"externeBelegnummer\":\"\",\"dokumentId\":\"\",\"belegdatum\":[2023,7,21],\"beschreibung\":\"L+G 07_2023\",\"sollBetrag\":null,\"habenBetrag\":3000,\"mwStBetrag\":0,\"skontierfaehigerBetrag\":null,\"kostenstelle\":\"********\",\"zielkostenstelleBelegBertr\":\"\",\"geschaeftsbuchungsgruppe\":\"DE\",\"produktbuchungsgruppe\":\"LOH\",\"mwStGruppe\":\"0\",\"mwStSatz\":\"0\",\"zahlungsbedingungCode\":null,\"faelligkeitsdatum\":null,\"skontodatum\":null,\"skontoProzent\":null,\"skontodatum2\":null,\"skontoProzent2\":null}],\"anzahlungsvorgang\":null}";
        var mockOutput = getMockEndpoint("mock:output");

        AdviceWith.adviceWith(context, "fetchCSVFiBuRecordFromBroker10", a ->
        {
            a.replaceFromWith("direct:rabbitmq");
            a.weaveByToUri("rabbitmq:*").replace().to(mockOutput.getEndpointUri());
        });

        //to remove warnings
        AdviceWith.adviceWith(context, "fetchNevarisFibuCSVListFromBroker10", a ->
                a.replaceFromWith("direct:rabbitmqAggregated"));

        context.start();

        template.sendBody("direct:rabbitmq", fibuRecordJsonString1);
        template.sendBody("direct:rabbitmq", fibuRecordJsonString2);
        Thread.sleep(6000);
        template.sendBody("direct:rabbitmq", fibuRecordJsonString3);
        mockOutput.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context, 6, TimeUnit.SECONDS);

        List<Exchange> exchanges = mockOutput.getExchanges();

        var jsonString = exchanges.get(0).getIn().getBody(String.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules();
        NevarisFiBuCSVRecord[] records = objectMapper.readValue(jsonString, NevarisFiBuCSVRecord[].class);

        assertEquals(4, records.length);
        context.stop();
    }
}
