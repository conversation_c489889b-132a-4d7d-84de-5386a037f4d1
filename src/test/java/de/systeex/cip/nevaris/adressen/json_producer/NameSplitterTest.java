package de.systeex.cip.nevaris.adressen.json_producer;

import de.systeex.cip.nevaris.adressen.json_producer.infrastructure.NameSplitter;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class NameSplitterTest {

    @Test
    public void testNameSplitter() {
        String name = "PerfektRichtigLangerFirmenname ÜberMaxFieldLengthLangerFirmenname KurzerFirmenname SehrKurz";

        String[] addressFields = NameSplitter.splitName(name, 30);

        assertEquals(3,addressFields.length);
        assertEquals("PerfektRichtigLangerFirmenname", addressFields[0]);
        assertEquals("ÜberMaxFieldLengthLangerFirmen", addressFields[1]);
        assertEquals("name KurzerFirmenname SehrKurz", addressFields[2]);
    }
}