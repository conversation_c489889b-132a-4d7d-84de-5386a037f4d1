package de.systeex.cip.nevaris.geschaeftspartner.json_producer.routing;


import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.nevaris.adressen.json_producer.infrastructure.UpdateNevarisAdresseProcessor;
import de.systeex.cip.types.Mitarbeiter;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

public class UpdateNevarisAdresseProcessorTest {

    @Test
    public void testMitarbeiterToAdressatenuebersicht() {
        UpdateNevarisAdresseProcessor updateNevarisAdresseProcessor = new UpdateNevarisAdresseProcessor(
                "username",
                "password",
                "workstation",
                "domain",
                "host",
                "basePath"
        );

        Mi<PERSON>beiter mitarbeiter = new <PERSON><PERSON><PERSON>ter(
                "10",
                "PERSO<PERSON>LNR",
                "Ja",
                "1054567",
                "3752",
                "10",
                "20",
                "Dr.",
                "Dr.",
                "<PERSON>. <PERSON>",
                "<PERSON>",
                "<PERSON><PERSON><PERSON>",
                "15",
                "03958-1353-3",
                "0153-13539523",
                "Dienstgruppe",
                "Funktion",
                "Zielfunktion",
                "<EMAIL>",
                "www.test.de",
                "9230425",
                LocalDate.of(1998, 4, 3),
                "Zimmer 404",
                "Berlin",
                "Abteilung",
                "1234",
                "03958-1353-15",
                "03958-1353-3",
                "Info",
                false,
                LocalDate.of(2023, 4, 3),
                null,
                "deutsch",
                true
        );

        NevarisAdressatenuebersicht nevarisAdressatenuebersicht = ReflectionTestUtils.invokeMethod(updateNevarisAdresseProcessor, "mitarbeiterToNevarisAdressatenuebersicht", mitarbeiter, "925670449");

        assertNotNull(nevarisAdressatenuebersicht);
        assertNull(nevarisAdressatenuebersicht.odataContext());
        assertNull(nevarisAdressatenuebersicht.odataEtag());
        assertEquals("925670449", nevarisAdressatenuebersicht.adressnr());
        assertEquals("PERSONALNR", nevarisAdressatenuebersicht.adressat());
        assertEquals("", nevarisAdressatenuebersicht.adressbezeichnungLang());
        assertEquals("Herr Dr. M", nevarisAdressatenuebersicht.anrede());
        assertEquals("Max", nevarisAdressatenuebersicht.vorname());
        assertEquals("Mustermann", nevarisAdressatenuebersicht.name());
        assertEquals("Dr.", nevarisAdressatenuebersicht.titel());
        assertEquals("Dr.", nevarisAdressatenuebersicht.titelImAnschreiben());
        assertEquals("15", nevarisAdressatenuebersicht.durchwahl());
        assertEquals("03958-1353-3", nevarisAdressatenuebersicht.fax());
        assertEquals("03958-1353-15", nevarisAdressatenuebersicht.telefonnrKompl());
        assertEquals("03958-1353-3", nevarisAdressatenuebersicht.faxnrKompl());
        assertEquals("0153-13539523", nevarisAdressatenuebersicht.mobiltelefon());
        assertEquals("Zielfunkti", nevarisAdressatenuebersicht.funktion());
        assertEquals("Abteilung", nevarisAdressatenuebersicht.abteilung());
        assertEquals("<EMAIL>", nevarisAdressatenuebersicht.eMail());
        assertEquals("www.test.de", nevarisAdressatenuebersicht.wwwAdresseURL());
        assertEquals("9230425", nevarisAdressatenuebersicht.adressnrPrivat());
        assertEquals("1998-04-03", nevarisAdressatenuebersicht.geburtsdatum());
        assertEquals("Zimmer 404", nevarisAdressatenuebersicht.raum());
        assertEquals("1234", nevarisAdressatenuebersicht.externeAdressnr());
        assertFalse(nevarisAdressatenuebersicht.inaktiv());
        assertEquals("0001-01-01", nevarisAdressatenuebersicht.austrittsdatum());
        assertEquals("deutsch", nevarisAdressatenuebersicht.sprache());
        assertNull(nevarisAdressatenuebersicht.neuanlagesystem());
        assertNull(nevarisAdressatenuebersicht.neuanlagebenutzer());
        assertNull(nevarisAdressatenuebersicht.neuanlagedatum());
        assertNull(nevarisAdressatenuebersicht.neuanlagezeit());
        assertNull(nevarisAdressatenuebersicht.aenderungssystem());
        assertNull(nevarisAdressatenuebersicht.aenderungsbenutzer());
        assertNull(nevarisAdressatenuebersicht.aenderungsdatum());
        assertNull(nevarisAdressatenuebersicht.aenderungszeit());
    }
}
