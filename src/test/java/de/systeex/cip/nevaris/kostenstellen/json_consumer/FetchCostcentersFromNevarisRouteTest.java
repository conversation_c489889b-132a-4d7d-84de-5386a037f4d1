package de.systeex.cip.nevaris.kostenstellen.json_consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenter;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenterList;
import de.systeex.cip.nevaris.kostenstellen.json_consumer.routing.FetchCostcentersFromNevarisRoute;
import org.apache.camel.Exchange;
import org.apache.camel.RoutesBuilder;
import org.apache.camel.builder.AdviceWith;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.junit5.CamelTestSupport;
import org.apache.camel.test.spring.junit5.UseAdviceWith;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@UseAdviceWith
public class FetchCostcentersFromNevarisRouteTest  extends CamelTestSupport {

    // We override this method with the routes to be tested
    @Override
    protected RoutesBuilder createRouteBuilder() throws Exception {
        var result = new FetchCostcentersFromNevarisRoute(null);
        ReflectionTestUtils.setField(result, "cronExpression", "0 5 * * * *");
        HashMap<String, String> mandantMapToNevaris = new HashMap<>();
        mandantMapToNevaris.put("10","10");
        ReflectionTestUtils.setField(result, "mandantMapToNevaris", mandantMapToNevaris);
        return result;
    }

    @Override
    public boolean isUseAdviceWith() {
        return true;
    }

    // We write a simple JUnit test case
    @Test
    public void testRoute() throws Exception {
        MockEndpoint rabbitmqMockEndpoint = getMockEndpoint("mock:rabbitmq");
        MockEndpoint transformAndSendToBrokerMockEndpoint = getMockEndpoint("mock:TransformAndSendToBroker");
        var inputStream = new FileInputStream("src/test/resources/NEVARIS_Kostenstellen_Beispiel.json");
        var inputString = new BufferedReader(new InputStreamReader(inputStream))
                .lines().collect(Collectors.joining(""));
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        NevarisCostCenterList nevarisCostCenterList = objectMapper.readValue(inputString, NevarisCostCenterList.class);
        List<NevarisCostCenter> nevarisCostCenters = nevarisCostCenterList.getValues();

        AdviceWith.adviceWith(context, "nevarisCostcenterQuartz10", a -> {
            a.replaceFromWith("direct:Input");
            a.weaveById("pollNevarisKostenstellenProcessor10").remove();
            a.weaveById("enrichMitAdresseProcessor10").remove();
            a.interceptSendToEndpoint("direct:TransformAndSendToBroker").skipSendToOriginalEndpoint().to(transformAndSendToBrokerMockEndpoint.getEndpointUri());
        });

        AdviceWith.adviceWith(context, "transformAndSendToBroker", a -> {
            a.replaceFromWith("direct:InTransformAndSendToBroker");
            a.interceptSendToEndpoint("rabbitmq://kostenstellen*").skipSendToOriginalEndpoint().to("mock:rabbitmq");
        });

        context.start();

        Map<String, Object> headers = new HashMap<>();
        headers.put(Exchange.HTTP_RESPONSE_CODE, "200");
        headers.put("DATE_PART", "1900-01-01");
        template.sendBodyAndHeaders("direct:Input", nevarisCostCenters, headers);
        transformAndSendToBrokerMockEndpoint.expectedMessageCount(2);

        // TODO: test rabbitmq, test second payload
        List<Exchange> exchanges = transformAndSendToBrokerMockEndpoint.getExchanges();
        NevarisCostCenter nevarisCostCenter = exchanges.get(0).getIn().getBody(NevarisCostCenter.class);
        assertEquals("1000000", nevarisCostCenter.control2());
        assertEquals("Umlagekostenstelle", nevarisCostCenter.name());
        assertEquals("", nevarisCostCenter.bezeichnung2());
        assertEquals("", nevarisCostCenter.tmpVorschauID());
        assertNotEquals(Boolean.TRUE, nevarisCostCenter.bemerkung());
        assertEquals("UMLAGEKOSTENSTELLE", nevarisCostCenter.suchbegriff());
        assertEquals(0.0, nevarisCostCenter.auftragswert());
        assertEquals("0", nevarisCostCenter.nachtragswert());
        assertEquals("", nevarisCostCenter.konsolidierungscode());
        assertEquals("", nevarisCostCenter.gemeindeschluesselAGS());
        assertEquals("", nevarisCostCenter.bezeichnungAGS());
        assertNotEquals(Boolean.TRUE, nevarisCostCenter.istBaustelle());
        assertEquals("", nevarisCostCenter.adresseKStOrt());
        assertEquals("", nevarisCostCenter.adresseKStPLZ());
        assertEquals("", nevarisCostCenter.adresseKStBundesland());
        assertEquals("", nevarisCostCenter.textBundesland());
        assertEquals("", nevarisCostCenter.babZeilendefinition());
        assertEquals("", nevarisCostCenter.angebotsprojekt());
        assertEquals("", nevarisCostCenter.auftragsprojekt());
        assertEquals("0001-01-01", nevarisCostCenter.auftragseingang());
        assertEquals("", nevarisCostCenter.adressnrDerKostenstelle());
        assertEquals("", nevarisCostCenter.adressnrDesAuftraggebers());
        assertEquals("", nevarisCostCenter.debitor());
        assertEquals("", nevarisCostCenter.kundennrBeimAuftraggeber());
        assertEquals("", nevarisCostCenter.empfangsstelle());
        assertEquals("", nevarisCostCenter.bauleiter());
        assertEquals("", nevarisCostCenter.bauleitergruppe());
        assertEquals("", nevarisCostCenter.oberbauleiter());
        assertEquals("", nevarisCostCenter.adressnrBauleiterBauherrn());
        assertEquals("", nevarisCostCenter.adressatBauleiterBauherrn());
        assertEquals("", nevarisCostCenter.adressnrDesBauherrn());
        assertEquals("", nevarisCostCenter.adressatDesBauherrn());
        assertEquals("", nevarisCostCenter.adressnrDesArchitekten());
        assertEquals("", nevarisCostCenter.adressatDesArchitekten());
        assertEquals("", nevarisCostCenter.adressnrPolier());
        assertEquals("", nevarisCostCenter.adressatPolier());
        assertEquals("", nevarisCostCenter.vertragsart());
        assertEquals(Integer.valueOf(0), nevarisCostCenter.gewaehrleistungInMonaten());

        MockEndpoint.assertIsSatisfied(context);
    }
}
