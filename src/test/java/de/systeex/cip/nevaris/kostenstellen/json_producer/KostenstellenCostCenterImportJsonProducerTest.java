package de.systeex.cip.nevaris.kostenstellen.json_producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenter;
import de.systeex.cip.nevaris.kostenstellen.json_producer.routing.KostenstellenCostCenterImportJsonProducer;
import de.systeex.cip.types.Adresse;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.RoutesBuilder;
import org.apache.camel.builder.AdviceWith;
import org.apache.camel.builder.ExchangeBuilder;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.junit5.CamelTestSupport;
import org.apache.camel.test.spring.junit5.UseAdviceWith;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.apache.camel.language.constant.ConstantLanguage.constant;
import static org.junit.jupiter.api.Assertions.assertEquals;

@UseAdviceWith
public class KostenstellenCostCenterImportJsonProducerTest extends CamelTestSupport {

    @Override
    protected RoutesBuilder createRouteBuilder() throws Exception {
        KostenstellenCostCenterImportJsonProducer result = new KostenstellenCostCenterImportJsonProducer();
        HashMap<String, String> mandantMapToNevaris = new HashMap<>();
        mandantMapToNevaris.put("10", "10");
        ReflectionTestUtils.setField(result, "mandantMapToNevaris", mandantMapToNevaris);
        ReflectionTestUtils.setField(result, "mandantMapToNevarisNummer", mandantMapToNevaris);
        ReflectionTestUtils.setField(result, "ntlmUsername", "user");
        ReflectionTestUtils.setField(result, "ntlmPassword", "password");
        return result;
    }

    @Override
    public boolean isUseAdviceWith() {
        return true;
    }

    @Test
    public void testTransformRoute() throws Exception {
        Adresse baustelle = new Adresse(
                "10",
                "Easytec",
                "4711",
                null,
                "Hornbach",
                null,
                null,
                null,
                null,
                "Musterstraße 1",
                "DE",
                "12345",
                "Musterhausen",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                List.of()
                );

        Kostenstelle kostenstelleInput = new Kostenstelle(
                "10",
                "{{MANDANT}}97800342",
                "HORNBACH Frankfurt Main, Anschlussarbeiten Sprinkl",
                null,
                null,
                LocalDate.parse("2023-02-01"),
                LocalDate.parse("2022-11-07"),
                null,
                null,
                null,
                Instant.parse("2022-11-07T00:00:00Z"),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                LocalDate.parse("2023-08-31"),
                null,
                baustelle,
                null,
                null,
                null,
                "Easytec",
                new ArrayList<>(),
                "25000,00"
        );

        ObjectMapper objectMapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        String kostenstellenJsonString1 = objectMapper.writeValueAsString(kostenstelleInput);

        var mockOutput = getMockEndpoint("mock:output");
        var mockOutput2 = getMockEndpoint("mock:output2");

        AdviceWith.adviceWith(context, "fetchKostenstelleFromBroker10", a ->
        {
            a.replaceFromWith("direct:rabbitmq");

            a.interceptSendToEndpoint("direct:enrichProjektbeteiligte*")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:enrichProjektbeteiligte*");

            a.interceptSendToEndpoint("direct:generateBaustellenAdressnummer")
                    .skipSendToOriginalEndpoint()
                    .setBody(constant("1"))
                    .log("skipped direct:generateBaustellenAdressnummer");

            a.weaveByToUri("sql:*").remove();

            a.weaveById("createAdresseSetPLZCodeProcessor10").remove();
            a.weaveById("notCreateAdresseSetPLZCodeProcessor10").remove();

            a.interceptSendToEndpoint("direct:fetchKostenstelleFromBroker.getAdressuebersichtFromNevaris")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:fetchKostenstelleFromBroker.getAdressuebersichtFromNevaris");

            a.interceptSendToEndpoint("direct:getPLZCodeFromNevaris")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:getPLZCodeFromNevaris");

            a.interceptSendToEndpoint("direct:updateNevarisAdressuebersichtFromKostenstelle")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:updateNevarisAdressuebersichtFromKostenstelle");

            a.interceptSendToEndpoint("direct:updateAdressTabelleForBaustelle")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:updateAdressTabelleForBaustelle");

            a.interceptSendToEndpoint("direct:createNevarisAdressuebersichtFromKostenstelle")
                    .skipSendToOriginalEndpoint()
                    .to("mock:input");

            a.interceptSendToEndpoint("direct:createNevarisAdressuebersicht")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:createNevarisAdressuebersicht");

            a.interceptSendToEndpoint("direct:createBaustellenAdressatFromKonzernAdressat")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:createBaustellenAdressatFromKonzernAdressat");

            a.interceptSendToEndpoint("direct:kostenstellenStandardadressatZuweisen")
                    .skipSendToOriginalEndpoint()
                    .log("skipped direct:kostenstellenStandardadressatZuweisen");

            a.weaveByToUri("rabbitmq:*").replace()
                    .to(mockOutput.getEndpointUri());
        });

        AdviceWith.adviceWith(context, "generateBaustellenAdressnummer", a ->
                a.weaveByToUri("sql:*").remove());

        AdviceWith.adviceWith(context, "rabbitmqKostenstellenSingle10", a -> {
                a.replaceFromWith("direct:rabbitmq2");
                a.weaveByToUri("sql:*").remove();
                a.interceptSendToEndpoint("direct:projektbeteiligteToNevaris")
                        .skipSendToOriginalEndpoint()
                        .log("skipped direct:projektbeteiligteToNevaris");

                a.interceptSendToEndpoint("direct:enrichCostcenter")
                        .skipSendToOriginalEndpoint()
                        .log("skipped direct:enrichCostcenter");
            }
        );

        AdviceWith.adviceWith(context, "createNevarisKostenstelle", a ->
                a.weaveByToUri("http:*")
                        .replace()
                        .setHeader(Exchange.HTTP_RESPONSE_CODE, constant("200"))
                        .to(mockOutput2.getEndpointUri()));

        AdviceWith.adviceWith(context, "updateNevarisKostenstelle", a ->
                a.weaveByToUri("http:*")
                        .replace()
                        .setHeader(Exchange.HTTP_RESPONSE_CODE, constant("200"))
                        .to(mockOutput2.getEndpointUri()));

        context.start();

        template.sendBody("direct:rabbitmq", kostenstellenJsonString1);

        mockOutput.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context, 3, TimeUnit.SECONDS);

        List<Exchange> exchanges = mockOutput.getExchanges();
        String costCenterDataJson = exchanges.get(0).getIn().getBody(String.class);

        Exchange exchange = ExchangeBuilder.anExchange(context)
                .withBody(costCenterDataJson)
                .withHeader("NEVARIS_KOSTENSTELLE_E_TAG", null)
                .withHeader("NEVARIS_KOSTENSTELLE", "1097800342")
                .withHeader("NEVARIS_MANDANT", "10")
                .withHeader("NEVARIS_NIEDERLASSUNG", "NONNWEILER")
                .build();
        template.send("direct:rabbitmq2", exchange);

        exchanges = mockOutput2.getExchanges();
        String nevarisCostCenterJson = exchanges.get(0).getIn().getBody(String.class);
        NevarisCostCenter nevarisCostCenter = objectMapper.readValue(nevarisCostCenterJson, NevarisCostCenter.class);

        assertEquals("1097800342", nevarisCostCenter.control2());
        assertEquals("10", nevarisCostCenter.mandantenfilter());

        context.stop();
    }
}
