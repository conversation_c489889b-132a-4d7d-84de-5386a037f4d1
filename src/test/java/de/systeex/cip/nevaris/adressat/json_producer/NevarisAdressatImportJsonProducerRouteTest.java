package de.systeex.cip.nevaris.adressat.json_producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersichtListe;
import de.systeex.cip.nevaris.adressat.json_producer.routing.NevarisAdressatImportJsonProducerRoute;
import org.apache.camel.Exchange;
import org.apache.camel.RoutesBuilder;
import org.apache.camel.builder.AdviceWith;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.junit5.CamelTestSupport;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.camel.language.constant.ConstantLanguage.constant;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class NevarisAdressatImportJsonProducerRouteTest extends CamelTestSupport {

    @Override
    protected RoutesBuilder createRouteBuilder() throws Exception {
        NevarisAdressatImportJsonProducerRoute result = new NevarisAdressatImportJsonProducerRoute();
        var mandantMapToNevaris = new HashMap<String, String>();
        mandantMapToNevaris.put("10", "10");
        ReflectionTestUtils.setField(result, "mandantMapToNevaris", mandantMapToNevaris);
        return result;
    }

    @Override
    public boolean isUseAdviceWith() {
        return true;
    }

    @Test
    public void testRoute() throws Exception {
        String mitarbeiterJsonString = "{\"mandant\":\"MaWi\",\"personalnr\":\"4711\",\"pze\":null,\"kostenstelle\":null,\"betriebsnummer\":null,\"ak\":null,\"vertragsnummer\":null,\"titel\":\"\",\"titelImAnschreiben\":\"\",\"anrede\":\"\",\"vorname\":\"Andreas\",\"name\":\"Saerdna\",\"durchwahl\":\"\",\"fax\":\"\",\"mobiltelefon\":\"\",\"dienstgruppe\":null,\"funktion\":\"\",\"zielFunktion\":null,\"mail\":\"\",\"wwwAdresse\":\"\",\"adressnrPrivat\":\"\",\"geburtsdatum\":\"0001-01-01\",\"raum\":\"\",\"bundeslandPrivatadresse\":\"\",\"abteilung\":\"\",\"externeAdressnr\":\"\",\"telefonnrKompl\":\"\",\"faxnrKompl\":\"\",\"infofeld\":\"\",\"inaktiv\":false,\"eintrittsdatum\":null,\"austrittsdatum\":\"0001-01-01\",\"sprache\":\"\"}";
        String nevarisAdressatenuebersichtListeJson = "{\"@odata.context\":null,\"value\":[{\"@odata.context\":null,\"@odata.etag\":null,\"Adressnr\":\"123\",\"Adressat\":\"4711\",\"Adressbezeichnung_lang\":null,\"Anrede\":\"\",\"Vorname\":\"Andreas\",\"Name\":\"Saerdna\",\"Titel\":\"\",\"Titel_im_Anschreiben\":\"\",\"Durchwahl\":\"\",\"Fax\":\"\",\"Telefonnr_kompl\":\"\",\"Faxnr_kompl\":\"\",\"Mobiltelefon\":\"\",\"Funktion\":\"\",\"Abteilung\":\"\",\"E_Mail\":\"\",\"WWW_Adresse_URL\":\"\",\"Adressnr_privat\":\"\",\"Geburtsdatum\":\"0001-01-01\",\"Raum\":\"\",\"Externe_Adressnr\":\"\",\"Inaktiv\":false,\"Austrittsdatum\":\"0001-01-01\",\"Sprache\":\"\",\"Neuanlagesystem\":null,\"Neuanlagebenutzer\":null,\"Neuanlagedatum\":null,\"Neuanlagezeit\":null,\"Änderungssystem\":\"\",\"Änderungsdatum\":\"\",\"Änderungszeit\":\"\"}]}";

        ObjectMapper objectMapper = new ObjectMapper();
        NevarisAdressatenuebersichtListe nevarisAdressatenuebersichtExpected = objectMapper.readValue(nevarisAdressatenuebersichtListeJson, NevarisAdressatenuebersichtListe.class);

        var mockUpdateStandardAdressat = getMockEndpoint("mock:updateStandardAdressat");
        var mockUpdateWeitereAdressaten = getMockEndpoint("mock:mockUpdateWeitere");
        var mockAdressatImport = getMockEndpoint("mock:adressatImport");
        var mockGetAlleAdressaten = getMockEndpoint("mock:getAlleAdressaten");

        AdviceWith.adviceWith(context, "fetchAdressatFromBroker10", a ->
        {
            a.replaceFromWith("direct:rabbitmq");

            a.interceptSendToEndpoint("direct:adressatImportEnrichNevarisAdressat")
                    .skipSendToOriginalEndpoint()
                    .to(mockAdressatImport.getEndpointUri());

            a.weaveByToUri("sql:*").remove();
        });

        AdviceWith.adviceWith(context, "updateStandardAdressat", a ->
                a.weaveByToUri("http:*").replace().to(mockUpdateStandardAdressat.getEndpointUri()));

        AdviceWith.adviceWith(context, "updateWeitereAdressaten", a ->
                a.weaveByToUri("http:*").replace().to(mockUpdateWeitereAdressaten.getEndpointUri()));

        AdviceWith.adviceWith(context, "getAlleAdressatenByAdressatFromNevaris", a -> {
            a.weaveByToUri("http:*").after().setHeader("CamelHttpResponseCode", constant(201));
            a.weaveByToUri("http:*").after().transform().constant(nevarisAdressatenuebersichtListeJson);
            a.weaveByToUri("http:*").replace().to(mockGetAlleAdressaten.getEndpointUri());
        });

        context.start();

        Map<String, Object> headers = new HashMap<>();
        headers.put("NEVARIS_ADRESS_NR", "123");
        template.sendBodyAndHeaders("direct:rabbitmq", mitarbeiterJsonString, headers);
        template.sendBodyAndHeader(mockGetAlleAdressaten.getEndpointUri(), nevarisAdressatenuebersichtExpected, Exchange.HTTP_RESPONSE_CODE, "201");
        mockAdressatImport.expectedMessageCount(1);
        mockUpdateStandardAdressat.expectedMessageCount(1);
        mockUpdateWeitereAdressaten.expectedMessageCount(1);
        MockEndpoint.assertIsSatisfied(context);

        List<Exchange> exchanges = mockUpdateStandardAdressat.getExchanges();
        String nevarisAdressatenuebersichtJson = exchanges.get(0).getIn().getBody(String.class);
        NevarisAdressatenuebersicht nevarisAdressatenuebersicht = objectMapper.readValue(nevarisAdressatenuebersichtJson, NevarisAdressatenuebersicht.class);
        assertEquals("123", nevarisAdressatenuebersicht.adressnr());
        assertEquals("4711", nevarisAdressatenuebersicht.adressat());

        exchanges = mockUpdateWeitereAdressaten.getExchanges();
        var weitererAdressatJson = exchanges.get(0).getIn().getBody(String.class);
        NevarisAdressatenuebersicht weitererAdressat = objectMapper.readValue(weitererAdressatJson, NevarisAdressatenuebersicht.class);
        assertEquals("123", weitererAdressat.adressnr());
        assertEquals("4711", weitererAdressat.adressat());
        context.stop();
    }
}
