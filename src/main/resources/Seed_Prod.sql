TRUNCATE TABLE mapping.adressnummern RESTART IDENTITY;
INSERT INTO mapping.adressnummern(mandant,adress_nummer)
VALUES
('10', '7000010'),
('11', '7000011'),
('12', '7000012'),
('13', '7000013'),
('14', '7000014'),
('15', '7000015'),
('16', '7000016'),
('17', '7000017'),
('18', '7000018'),
('19', '7000019'),
('20', '7000020'),
('21', '7000021'),
('22', '7000022'),
('23', '7000023'),
('24', '7000024'),
('25', '7000025'),
('26', '7000026'),
('27', '7000027'),
('28', '7000028'),
('29', '7000029');

TRUNCATE TABLE mapping.companies RESTART IDENTITY;
INSERT INTO mapping.companies(mandant, nevaris_company_key)
VALUES ('10', '84344527-2a6c-ee11-ac10-0050568111b3'),
       ('11', 'a7baa4ea-586c-ee11-ac10-0050568111b3'),
       ('12', '2c47b90c-596c-ee11-ac10-0050568111b3'),
       ('13', 'a3f98b3a-596c-ee11-ac10-0050568111b3'),
       ('14', '6e6eb855-596c-ee11-ac10-0050568111b3'),
       ('15', '20b5ecb2-5b6c-ee11-ac10-0050568111b3'),
       ('16', '0424a6cc-5b6c-ee11-ac10-0050568111b3'),
       ('17', '0c841c05-5c6c-ee11-ac10-0050568111b3'),
       ('18', 'e9b93b36-5c6c-ee11-ac10-0050568111b3'),
       ('19', '4dabbbe9-5c6c-ee11-ac10-0050568111b3'),
       ('20', '977a5f20-5d6c-ee11-ac10-0050568111b3'),
       ('21', '30c2ab3b-5d6c-ee11-ac10-0050568111b3'),
       ('22', 'c7408c33-5f6c-ee11-ac10-0050568111b3'),
       ('23', 'd92dd250-5f6c-ee11-ac10-0050568111b3'),
       ('24', 'a2b59d8f-5f6c-ee11-ac10-0050568111b3'),
       ('25', 'aca4c9bc-5f6c-ee11-ac10-0050568111b3'),
       ('26', 'e13530fe-5f6c-ee11-ac10-0050568111b3'),
       ('27', 'd922c735-606c-ee11-ac10-0050568111b3'),
       ('28', 'fee205b4-606c-ee11-ac10-0050568111b3'),
       ('29', '6e56d3ad-0f77-ee11-ac12-0050568111b3'),
       ('90', 'f3e68315-616c-ee11-ac10-0050568111b3'),
       ('900', '4e863c98-ce06-43e6-9b4c-dcc0557dafca'),
       ('91', 'b7fd874c-616c-ee11-ac10-0050568111b3'),
       ('92', '29a10975-616c-ee11-ac10-0050568111b3'),
       ('93', '7a0590a2-616c-ee11-ac10-0050568111b3'),
       ('99', '65af62ff-616c-ee11-ac10-0050568111b3');

TRUNCATE TABLE mapping.niederlassungen;
INSERT INTO mapping.niederlassungen(src_niederlassung, mandant, dst_niederlassung)
VALUES
    ('NONNWEILER', '10', 'NONNWEILER_SBS'),
    ('NONNWEILER', '23', 'NONNWEILER_23'),

    ('ECHTERNACH', '10', 'ECHTERNACH_SBS'),
    ('ECHTERNACH', '22', 'ECHTERNACH_22'),

    ('HANAU', '24', 'HANAU_SET'),

    ('KÖLN', '25', 'KÖLN_HKE');