CREATE SCHEMA IF NOT EXISTS mapping;
CREATE TABLE mapping.ad<PERSON><PERSON> (
    id BIGSERIAL PRIMARY KEY,
    source_system_name VARCHAR(255) NOT NULL DEFAULT '',
    mandant VARCHAR(255) NOT NULL DEFAULT '',
    source_system_id VARCHAR(255) NOT NULL DEFAULT '',
    data TEXT NULL DEFAULT NULL,
    nevaris_key VARCHAR(255) NOT NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    nevaris_reply_at TIMESTAMP NULL DEFAULT NULL
);
CREATE TABLE mapping.debitoren (
    id BIGSERIAL PRIMARY KEY,
    source_system_name VARCHAR(255) NOT NULL DEFAULT '',
    mandant VARCHAR(255) NOT NULL DEFAULT '',
    source_system_id VARCHAR(255) NOT NULL DEFAULT '',
    data TEXT NULL DEFAULT NULL,
    nevaris_key VARCHAR(255) NOT NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    nevaris_reply_at TIMESTAMP NULL DEFAULT NULL
);