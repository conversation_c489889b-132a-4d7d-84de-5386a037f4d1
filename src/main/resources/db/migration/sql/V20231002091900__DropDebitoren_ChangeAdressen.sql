DROP TABLE mapping.debitoren;

ALTER TABLE mapping.adressen
ADD COLUMN interne_adress_nr VARCHAR(255),
ADD COLUMN is_debitor BOOLEAN,
ADD COLUMN company_name <PERSON><PERSON>HAR(255),
ADD COLUMN "name" VA<PERSON>HAR(255),
ADD COLUMN suchbegriff VARCHAR(255),
ADD COLUMN adresse VARCHAR(255),
ADD COLUMN adresse2 VARCHAR(255),
ADD COLUMN adresse3 VARCHAR(255),
ADD COLUMN uStIdNr VARCHAR(255);

CREATE UNIQUE INDEX IX_System_Mandant_ID
ON mapping.adressen (source_system_name, mandant, source_system_id);
