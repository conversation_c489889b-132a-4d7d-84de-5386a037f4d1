spring.application.name=nevaris
version=dev
camel.component.rabbitmq.automatic-recovery-enabled=true
camel.springboot.main-run-controller=true
poll.cron.kostenstelle=0 38 13 * * ?
mandant.mapping={'10': '10 SBS', '11': '11 SFS', '12': '12 GT', '13': '13 CBG', '14': '14 HKE', '15': '15 ARE D', '16': '16 BALU', '17': '17 SRE', '18': '18 ARE VT', '19': '19 ARE B', '20': '20 ARE C', '21': '21 ARE CF1', '22': '22 LC'}
mandant.mapping.kostenstelle={'10': '10', '11': '11', '12': '12', '13': '13', '14': '14', '18': '18', '21': '21', '22': '22', '23':'23', '24':'24', '25':'25', '26':'26', '27':'27', '30':'30', '32':'32'}
poll.cron.artikel=0 58 14 * * ?
poll.cron.artikellieferant=0 30 15 * * ?
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=PaSSw0rd
message.broker.hostname=localhost
message.broker.port=5672
message.broker.username=guest
message.broker.password=guest
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration/sql
bba.file.output.path=data/out/bba
bba.file.output.filename=NevarisBBA.csv
fibu.file.output.path=data/out/fibu
fibu.file.output.filename=NevarisFiBu.txt
nevaris.host=test-hu-nvweb:8048
nevaris.username=nbauen
#nevaris.username=Raphael.Burbach
nevaris.password=!KinzigWeg2022++
#nevaris.password=MainKinzig1904++
nevaris.default.basepath=/INTEGRATION/ODataV4
nevaris.custom.basepath.v2=/INTEGRATION/api/NevarisPS/MiddelwareConnector/v2.0
mandant.mapping.nummer={'10': '10', '11': '11', '12': '12', '13': '13', '14': '14', '15': '15', '16': '16', '17': '17', '18': '18', '19': '19', '20': '20', '21': '21', '22': '22'}
mandant.debitornr.mapping={'10': '1310000', '12': '1330000', '16': '1350000'}
mandant.mapping.artikel={'10': '10'}
copy.mandant=99 Copy
poll.cron.plzcodes=0 15 11 * * ?
nevaris.plzcodes.mandant=10 SBS
poll.cron.artikelsatzmarkierung=0 45 15 * * ?
poll.cron.artikelsatzmarkierungwert=0 0 16 * * ?
