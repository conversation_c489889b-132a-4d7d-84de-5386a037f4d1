package de.systeex.cip.nevaris.kostenstellen.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;


public record NevarisKostenstellenAuftragswert(
         @JsonProperty("@odata.context")
         @JsonIgnore
         String odataContext,
         @JsonProperty("@odata.etag")
         @JsonIgnore
         String odataEtag,
         @JsonProperty("Lfd_Nr")
         @JsonIgnore
         String lfd_Nr,
         @JsonProperty("Kostenstelle")
         String kostenstelle, // Type="Edm.String" MaxLength="20"
         @JsonProperty("Auftragswert")
         Double auftragswert, //Type="Edm.Decimal" Scale="Variable">
         @JsonProperty("Buchungsperiode")
         @JsonIgnore
         Date buchungsperiode, //Type="Edm.Date">
         @JsonProperty("Auftragsdatum")
         @JsonIgnore
         Date auftragsdatum, //Type="Edm.Date">
         @JsonProperty("Auftrag_KW")
         @JsonIgnore
         Integer auftrag_KW, //Type="Edm.Int32">
         @JsonProperty("Auftrag_KW_Jahr")
         @JsonIgnore
         Integer auftrag_KW_Jahr, //Type="Edm.Int32">
         @JsonProperty("Buchungstext")
         @JsonIgnore
         String buchungstext, //Type="Edm.String" MaxLength="50">
         @JsonProperty("BAS")
         @JsonIgnore
         String bas, //Type="Edm.String" MaxLength="10">
         @JsonProperty("Debitor_Kreditor")
         @JsonIgnore
         String debitor_Kreditor, //Type="Edm.String"
         @JsonProperty("Debitor_Kreditor_Nr")
         @JsonIgnore
         String debitor_Kreditor_Nr, //Type="Edm.String" MaxLength="20">
         @JsonProperty("Kostenart")
         @JsonIgnore
         String kostenart, //Type="Edm.String" MaxLength="20">
         @JsonProperty("Kostenträger")
         @JsonIgnore
         String kostentrer, //Type="Edm.String" MaxLength="20">
         @JsonProperty("Planversion")
         @JsonIgnore
         String planversion, //Type="Edm.String" MaxLength="3">
         @JsonProperty("Dokument_ID")
         @JsonIgnore
         String dokument_ID, //Type="Edm.String" MaxLength="20">
         @JsonProperty("Projekt")
         @JsonIgnore
         String projekt, //Type="Edm.String" MaxLength="20">
         @JsonProperty("LV_Position")
         @JsonIgnore
         String lv_Position, //Type="Edm.String" MaxLength="35">
         @JsonProperty("Ergebniszuordnung")
         @JsonIgnore
         String ergebniszuordnung, //Type="Edm.String" MaxLength="10">
         @JsonProperty("Niederlassung")
         @JsonIgnore
         String niederlassung, // Type="Edm.String" MaxLength="20">
         @JsonProperty("Sparte")
         @JsonIgnore
         String sparte, //Type="Edm.String" MaxLength="10">
         @JsonProperty("Typ")
         @JsonIgnore
         String typ, //Type="Edm.String" MaxLength="10">
         @JsonProperty("Bauleiter")
         @JsonIgnore
         String bauleiter, //Type="Edm.String" MaxLength="10">
         @JsonIgnore
         @JsonProperty("Neuanlagesystem")
         String neuanlagesystem, //Type="Edm.String" MaxLength="10">
         @JsonIgnore
         @JsonProperty("Neuanlagebenutzer")
         String neuanlagebenutzer, // Type="Edm.String" MaxLength="50">
         @JsonIgnore
         @JsonProperty("Neuanlagedatum")
         Date neuanlagedatum, //Type="Edm.Date"
         @JsonIgnore
         @JsonProperty("Neuanlagezeit")
         String neuanlagezeit, // Type="Edm.String"
         @JsonIgnore
         @JsonProperty("Änderungssystem")
         String aenderungssystem, // Type="Edm.String" MaxLength="10">
         @JsonIgnore
         @JsonProperty("Änderungsbenutzer")
         String aenderungsbenutzer, // Type="Edm.String" MaxLength="50">
         @JsonIgnore
         @JsonProperty("Änderungsdatum")
         Date aenderungsdatum, // Type="Edm.Date"
         @JsonIgnore
         @JsonProperty("Änderungszeit")
         String aenderungszeit //Type="Edm.String"
        ) {
    }

