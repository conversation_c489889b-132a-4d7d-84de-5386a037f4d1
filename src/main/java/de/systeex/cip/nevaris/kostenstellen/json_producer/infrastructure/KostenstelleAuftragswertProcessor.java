package de.systeex.cip.nevaris.kostenstellen.json_producer.infrastructure;

import de.systeex.cip.nevaris.kostenstellen.json_producer.domain.KostenstellenData;
import de.systeex.cip.nevaris.kostenstellen.json_producer.domain.NevarisKostenstellenAuftragswert;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.text.NumberFormat;
import java.util.Locale;

public class KostenstelleAuftragswertProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        KostenstellenData kostenstellenData = exchange.getIn().getBody(KostenstellenData.class);
        Kostenstelle kostenstelle = kostenstellenData.kostenstelle();
        double auftragswerteSumme = exchange.getIn().getHeader("AUFTRAGSWERTESUMME", double.class);
        String kostenstellenID = exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE", String.class);
        NumberFormat format = NumberFormat.getInstance(Locale.GERMANY);
        NevarisKostenstellenAuftragswert auftragswert = new NevarisKostenstellenAuftragswert(
            null,
                null,
                null ,
                kostenstellenID,
                kostenstelle.auftragswert() != null ? format.parse(kostenstelle.auftragswert()).doubleValue()-auftragswerteSumme : null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
        exchange.getIn().setBody(auftragswert);
        if(kostenstelle.auftragswert() != null && Math.abs(auftragswert.auftragswert()) >= 0.01) {
            exchange.getIn().setHeader("HAS_AUFTRAGSWERT", true);
        }

    }

}
