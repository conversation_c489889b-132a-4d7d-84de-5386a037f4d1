package de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure;

import de.systeex.cip.nevaris.adressen.json_producer.NevarisAdressuebersicht;
import de.systeex.cip.nevaris.kostenstellen.json_consumer.domain.KostenstellenData;
import de.systeex.cip.types.Adresse;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static de.systeex.cip.nevaris.domain.odata.NevarisCostCenter.NULL_DATE;

public class NevarisCostcenterToKostenstelleProcessor implements Processor {

    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.of("Europe/Berlin");
    private static final DateTimeFormatter TIME_FORMATTER = new DateTimeFormatterBuilder()
            .appendPattern("HH:mm:ss")
            .appendFraction(ChronoField.MILLI_OF_SECOND, 0, 6, true)
            .toFormatter();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    @Override
    public void process(Exchange exchange) throws Exception {
        KostenstellenData kostenstellenData = exchange.getIn().getBody(KostenstellenData.class);

        assert kostenstellenData.nevarisCostCenter().name() != null;
        exchange.getIn().setHeader("ODATA_ETAG", kostenstellenData.nevarisCostCenter().odataEtag());

        Kostenstelle kostenstelle = new Kostenstelle(
                exchange.getIn().getHeader("MANDANT", String.class),
                kostenstellenData.nevarisCostCenter().control2(),
                kostenstellenData.nevarisCostCenter().name(),
                kostenstellenData.nevarisCostCenter().bezeichnung2(),
                kostenstellenData.nevarisCostCenter().suchbegriff(),
                this.parseDate(kostenstellenData.nevarisCostCenter().datumFertigstellung()),
                this.parseDate(kostenstellenData.nevarisCostCenter().baubeginndatum()),
                kostenstellenData.nevarisCostCenter().sparte(),
                this.parseDate(kostenstellenData.nevarisCostCenter().gueltigAb()),
                this.parseDate(kostenstellenData.nevarisCostCenter().gueltigBis()),
                this.parseTimestamp(kostenstellenData.nevarisCostCenter().neuanlagedatum(), kostenstellenData.nevarisCostCenter().neuanlagezeit()),
                kostenstellenData.nevarisCostCenter().neuanlagebenutzer(),
                this.parseTimestamp(kostenstellenData.nevarisCostCenter().aenderungsdatum(), kostenstellenData.nevarisCostCenter().aenderungszeit()),
                kostenstellenData.nevarisCostCenter().aenderungsbenutzer(),
                kostenstellenData.nevarisCostCenter().oberbauleiter(),
                kostenstellenData.nevarisCostCenter().bauleiter(),
                null,
                null,
                null,
                null,
                this.parseDate(kostenstellenData.nevarisCostCenter().datumSchlussgerechnet()),
                null,
                this.getAdresse(kostenstellenData.nevarisAdressuebersicht(), exchange.getIn().getHeader("MANDANT", String.class)),
                kostenstellenData.nevarisCostCenter().ergebniszuordnung(),
                kostenstellenData.nevarisCostCenter().niederlassung(),
                kostenstellenData.nevarisCostCenter().typ(),
                "Nevaris",
                new ArrayList<>(),
                kostenstellenData.nevarisCostCenter().auftragswert().toString()
        );

        exchange.getIn().setBody(kostenstelle);
    }

    private Instant parseTimestamp(String datum, String zeit) {
        if (Objects.equals(datum, NULL_DATE)) {
            return null;
        }

        LocalDate date = LocalDate.parse(datum, DATE_FORMATTER);
        LocalTime time = LocalTime.parse(zeit, TIME_FORMATTER);
        LocalDateTime createdAt = LocalDateTime.of(date, time);

        return ZonedDateTime.of(createdAt, DEFAULT_ZONE_ID).toInstant();
    }

    private LocalDate parseDate(String datum) {
        if (Objects.equals(datum, NULL_DATE)) {
            return null;
        }
        return LocalDate.parse(datum, DATE_FORMATTER);
    }

    private Adresse getAdresse(NevarisAdressuebersicht nevarisAdressuebersicht, String mandant) {
        if (nevarisAdressuebersicht != null) {
            return new Adresse(
                    mandant,
                    "Nevaris",
                    nevarisAdressuebersicht.adressnr(),
                    null,
                    nevarisAdressuebersicht.adresse(),
                    nevarisAdressuebersicht.suchbegriff(),
                    nevarisAdressuebersicht.adresse(),
                    nevarisAdressuebersicht.adresse2(),
                    nevarisAdressuebersicht.adresse3(),
                    nevarisAdressuebersicht.strasse(),
                    nevarisAdressuebersicht.laendercode(),
                    nevarisAdressuebersicht.plz(),
                    nevarisAdressuebersicht.ort(),
                    nevarisAdressuebersicht.plzPostfach(),
                    nevarisAdressuebersicht.postfachNutzen(),
                    nevarisAdressuebersicht.postfach(),
                    nevarisAdressuebersicht.ortPostfach(),
                    nevarisAdressuebersicht.bundesland(),
                    nevarisAdressuebersicht.sprache(),
                    nevarisAdressuebersicht.auslandsvorwahl(),
                    nevarisAdressuebersicht.ortskennzahl(),
                    nevarisAdressuebersicht.hauptanschlussnr(),
                    nevarisAdressuebersicht.durchwahlZentrale(),
                    nevarisAdressuebersicht.durchwahlFax(),
                    nevarisAdressuebersicht.durchwahlModem(),
                    nevarisAdressuebersicht.telefonnrKompl(),
                    nevarisAdressuebersicht.faxnrKompl(),
                    nevarisAdressuebersicht.modemnrKompl(),
                    nevarisAdressuebersicht.wwwAdresseURL(),
                    nevarisAdressuebersicht.eMail(),
                    nevarisAdressuebersicht.landkreis(),
                    nevarisAdressuebersicht.handelsregister(),
                    nevarisAdressuebersicht.ustIdNr(),
                    nevarisAdressuebersicht.steuernummerGesellschaft(),
                    this.parseBooleanString(nevarisAdressuebersicht.gesperrt()),
                    this.parseDate(nevarisAdressuebersicht.gueltigAb()),
                    this.parseDate(nevarisAdressuebersicht.gueltigBis()),
                    List.of()
            );
        }

        return new Adresse(
          mandant,
          "Nevaris",
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
                List.of()
        );
    }

    private Boolean parseBooleanString(String boolValue) {

        return boolValue != null && boolValue.equals("Ja");
    }
}
