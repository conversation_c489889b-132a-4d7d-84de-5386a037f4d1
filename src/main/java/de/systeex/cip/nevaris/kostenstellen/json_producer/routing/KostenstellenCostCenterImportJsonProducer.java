package de.systeex.cip.nevaris.kostenstellen.json_producer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.nevaris.adressen.json_producer.NevarisAdressuebersicht;
import de.systeex.cip.nevaris.adressen.json_producer.infrastructure.KostenstelleToNevarisAdressuebersichtProcessor;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenter;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCode;
import de.systeex.cip.nevaris.infrastructure.CamelHeaderFilterStrategy;
import de.systeex.cip.nevaris.kostenstellen.json_producer.domain.KostenstellenData;
import de.systeex.cip.nevaris.kostenstellen.json_producer.domain.NevarisKostenstellenAuftragswert;
import de.systeex.cip.nevaris.kostenstellen.json_producer.domain.NevarisKostenstellenAuftragswertListe;
import de.systeex.cip.nevaris.kostenstellen.json_producer.infrastructure.*;
import de.systeex.cip.nevaris.projektbeteiligte.json_producer.domain.Projektbeteiligte_KSt;
import de.systeex.cip.nevaris.projektbeteiligte.json_producer.domain.Projektbeteiligte_KStList;
import de.systeex.cip.nevaris.projektbeteiligte.json_producer.infrastructure.KostenstelleToProjektbeteiligteProcessor;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.http.HttpComponent;
import org.apache.camel.component.http.HttpConstants;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.List;
import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;
import static org.apache.camel.component.sql.SqlConstants.SQL_GENERATED_KEYS_DATA;
import static org.apache.camel.component.sql.SqlConstants.SQL_RETRIEVE_GENERATED_KEYS;

@Component
public class KostenstellenCostCenterImportJsonProducer extends ErrorResistantRouteBuilder {

    @Value("${nevaris.host}")
    private String nevarisHost;

    @Value("#{${mandant.mapping}}")
    private Map<String, String> mandantMapToNevaris;

    @Value("#{${mandant.mapping.nummer}}")
    private Map<String, String> mandantMapToNevarisNummer;

    @Value("${nevaris.default.basepath}")
    private String defaultBasePath;

    @Value("${nevaris.username}")
    private String ntlmUsername;
    @Value("${nevaris.password}")
    private String ntlmPassword;

    private final Predicate createAdresse = PredicateBuilder.isNull(header("NEVARIS_ADRESS_NR"));
    private final Predicate fixeAdresse = PredicateBuilder.isNotNull(header("FIXE_ADRESS_NR"));
    private final Predicate kostenstelleIsNew = PredicateBuilder.isNull(header("NEVARIS_KOSTENSTELLE_E_TAG"));
    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final Predicate httpStatusNOk = PredicateBuilder.or(PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final Predicate adresseIstNeu = PredicateBuilder.isNull(header("EXTERNE_ADRESS_NR"));
    private final Predicate hasAuftragswert = PredicateBuilder.isNotNull(header("HAS_AUFTRAGSWERT"));
    private final String ENTITY_KEY_BAUSTELLEN_ADRESSE = "BAUSTELLEN_ADRESSE";
    private final Long START_NUMBER_VALUE_BAUSTELLEN_ADRESSE = 82000000L;

    private final String originQueueName = "nevaris-%s.kostenstellen";

    @Override
    public void configure() throws Exception {
        super.configure();
        String[] exactHeaders = {"request-id", "Server", "Transfer-Encoding", "Vary"};
        String[] startsWithHeaders = {"PROJEKTBETEILIGTE_", "NEVARIS_", "Camel"};
        CamelHeaderFilterStrategy camelHeaderFilterStrategy = new CamelHeaderFilterStrategy(exactHeaders, startsWithHeaders);
        HttpComponent httpComponent = getContext().getComponent("http", HttpComponent.class);
        SetPLZCodeProcessor setPLZCodeProcessor = new SetPLZCodeProcessor(ntlmUsername, ntlmPassword, "", "", nevarisHost, defaultBasePath);
        httpComponent.setHeaderFilterStrategy(camelHeaderFilterStrategy);
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat jacksonDataFormatKostenstelle = new JacksonDataFormat(mapper, Kostenstelle.class);
        JacksonDataFormat jacksonDataFormatNevarisCostCenter = new JacksonDataFormat(mapper, NevarisCostCenter.class);
        JacksonDataFormat jacksonDataFormatNevarisAdressatenuebersicht = new JacksonDataFormat(mapper, NevarisAdressatenuebersicht.class);
        JacksonDataFormat jacksonDataFormatNevarisAdressuebersicht = new JacksonDataFormat(mapper, NevarisAdressuebersicht.class);
        JacksonDataFormat jacksonDataFormatProjektbeteiligte_KStList = new JacksonDataFormat(mapper, Projektbeteiligte_KStList.class);
        JacksonDataFormat jacksonDataFormatProjektbeteiligter_KSt = new JacksonDataFormat(mapper, Projektbeteiligte_KSt.class);
        JacksonDataFormat jacksonDataFormatNevarisPLZCode = new JacksonDataFormat(mapper, NevarisPLZCode.class);
        JacksonDataFormat jacksonDataFormatKostenstellenData = new JacksonDataFormat(mapper, KostenstellenData.class);
        JacksonDataFormat jacksonDataFormatKostenstelleAuftragswert = new JacksonDataFormat(mapper, NevarisKostenstellenAuftragswert.class);
        JacksonDataFormat jacksonDataFormatKostenstelleAuftragswertListe = new JacksonDataFormat(mapper, NevarisKostenstellenAuftragswertListe.class);
        CreateBaustellenAdressatFromKonzernAdressatProcessor createBaustellenAdressatFromKonzernAdressatProcessor = new CreateBaustellenAdressatFromKonzernAdressatProcessor(ntlmUsername, ntlmPassword, "", "", nevarisHost, defaultBasePath);

        mandantMapToNevaris.keySet().forEach(mandant -> {
            String queueName = String.format(originQueueName, mandantMapToNevaris.get(mandant));
            from("rabbitmq:kostenstellen-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=" + queueName + "&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapToNevaris.get(mandant) + ".kostenstellen.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".kostenstellen&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchKostenstelleFromBroker" + mandantMapToNevaris.get(mandant))
                    .removeHeaders("Camel*", "CamelRabbitmqDeliveryMode|MESSAGE_ID|DELIVERY_MODE")
                    .unmarshal(jacksonDataFormatKostenstelle)
                    .setHeader("NEVARIS_MANDANT", constant(mandantMapToNevaris.get(mandant)))
                    .setHeader("MANDANT", simple("${body.mandant}"))
                    .process(exchange -> {
                        Kostenstelle kostenstelle = exchange.getIn().getBody(Kostenstelle.class);
                        if (kostenstelle.baustelle() == null) {
                            throw new Exception("Keine Adressdaten in der Kostenstelle enthalten");
                        }
                        exchange.getIn().setHeader("NEVARIS_MANDANT", mandantMapToNevaris.get(kostenstelle.mandant()));
                        exchange.getIn().setHeader("NEVARIS_KOSTENSTELLE", kostenstelle.id().replace("{{MANDANT}}", mandantMapToNevarisNummer.get(kostenstelle.mandant())));
                        exchange.getIn().setHeader("FIXE_ADRESS_NR", kostenstelle.fixeBaustellenadresse());
                        exchange.getIn().setHeader("SRC_ADRESS_ID", kostenstelle.baustelle().sourceSystemKey());
                        exchange.getIn().setHeader("SRC_KOSTENSTELLE", kostenstelle.id().replace("{{MANDANT}}", ""));
                        exchange.getIn().setHeader("BAUSTELLE_PLZ", kostenstelle.baustelle().plz());
                    })
                    .choice()
                    .when(PredicateBuilder.and(simple("${body.funktionKfm}").isNotNull(), simple("${body.funktionKfm}").isNotEqualTo("")))
                    .enrich("direct:enrichProjektbeteiligteKfm", (oldExchange, newExchange) -> {
                        NevarisAdressatenuebersicht nevarisAdressatenuebersicht = newExchange.getIn().getBody(NevarisAdressatenuebersicht.class);
                        if (nevarisAdressatenuebersicht != null) {
                            oldExchange.getIn().setHeader("PROJEKTBETEILIGTE_KFM", nevarisAdressatenuebersicht);
                        }
                        return oldExchange;
                    })
                    .end()
                    .choice()
                    .when(PredicateBuilder.and(simple("${body.funktionPl}").isNotNull(), simple("${body.funktionPl}").isNotEqualTo("")))
                    .enrich("direct:enrichProjektbeteiligtePl", (oldExchange, newExchange) -> {
                        NevarisAdressatenuebersicht nevarisAdressatenuebersicht = newExchange.getIn().getBody(NevarisAdressatenuebersicht.class);
                        if (nevarisAdressatenuebersicht != null) {
                            oldExchange.getIn().setHeader("PROJEKTBETEILIGTE_PL", nevarisAdressatenuebersicht);
                            oldExchange.getIn().setHeader("BAULEITER_KEY", getBauleiterKey(nevarisAdressatenuebersicht));
                        }
                        return oldExchange;
                    })
                    .end()
                    .choice()
                    .when(PredicateBuilder.and(simple("${body.funktionTl}").isNotNull(), simple("${body.funktionTl}").isNotEqualTo("")))
                    .enrich("direct:enrichProjektbeteiligteTl", (oldExchange, newExchange) -> {
                        NevarisAdressatenuebersicht nevarisAdressatenuebersicht = newExchange.getIn().getBody(NevarisAdressatenuebersicht.class);
                        if (nevarisAdressatenuebersicht != null) {
                            oldExchange.getIn().setHeader("PROJEKTBETEILIGTE_TL", nevarisAdressatenuebersicht);
                            oldExchange.getIn().setHeader("OBERBAULEITER_KEY", getBauleiterKey(nevarisAdressatenuebersicht));
                        }
                        return oldExchange;
                    })
                    .end()
                    .choice()
                    .when(fixeAdresse)
                    .log("fixeAdresse")
                    .setHeader("NEVARIS_ADRESS_NR", header("FIXE_ADRESS_NR"))
                    .otherwise()
                    .log("fixeAdresse Otherwise")
                    .toD("sql:SELECT adress_key FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND source_system_adress_id = '${body.baustelle.sourceSystemKey}' AND source_kostenstelle = '${headers.SRC_KOSTENSTELLE}'?outputType=SelectOne&outputHeader=NEVARIS_ADRESS_NR")
                    .enrich("direct:fetchKostenstelleFromBroker.getAdressuebersichtFromNevaris", (oldExchange, newExchange) -> {
                        NevarisAdressuebersicht nevarisAdressuebersicht = newExchange.getIn().getBody(NevarisAdressuebersicht.class);
                        oldExchange.getIn().removeHeader("NEVARIS_ADRESS_NR");
                        if (nevarisAdressuebersicht != null) {
                            oldExchange.getIn().setHeader("NEVARIS_ADRESS_NR", nevarisAdressuebersicht.adressnr());
                            oldExchange.getIn().setHeader("NEVARIS_ADRESS_E_TAG", nevarisAdressuebersicht.odataEtag());
                        }
                        return oldExchange;
                    })
                    .end()
                    .choice()
                    .when(createAdresse)
                    .log("createAdresse")
                    .process(setPLZCodeProcessor)
                    .id("createAdresseSetPLZCodeProcessor" + mandantMapToNevaris.get(mandant))
                    .enrich("direct:generateBaustellenAdressnummer", (oldExchange, newExchange) -> {
                        Long baustellenAdressnummer = newExchange.getIn().getBody(Long.class);
                        oldExchange.getIn().setHeader("NEVARIS_ADRESS_NR", baustellenAdressnummer.toString());
                        return oldExchange;
                    })
                    .to("direct:updateAdressTabelleForBaustelle")
                    .enrich("direct:createNevarisAdressuebersichtFromKostenstelle", (oldExchange, newExchange) -> oldExchange)
                    .enrich("direct:createBaustellenAdressatFromKonzernAdressat", (oldExchange, newExchange) -> oldExchange)
                    .enrich("direct:kostenstellenStandardadressatZuweisen", (oldExchange, newExchange) -> oldExchange)
                    .otherwise()
                    .log("createAdresse Otherwise")
                    .choice()
                    .when(PredicateBuilder.not(fixeAdresse))
                    .log("not fixeAdresse")
                    .process(exchange -> {

                    })
                    .process(setPLZCodeProcessor)
                    .id("notCreateAdresseSetPLZCodeProcessor" + mandantMapToNevaris.get(mandant))
                    .to("direct:updateAdressTabelleForBaustelle")
                    .enrich("direct:updateNevarisAdressuebersichtFromKostenstelle", (oldExchange, newExchange) -> oldExchange)
                    .enrich("direct:createBaustellenAdressatFromKonzernAdressat", ((oldExchange, newExchange) -> oldExchange))
                    .enrich("direct:kostenstellenStandardadressatZuweisen", (oldExchange, newExchange) -> oldExchange)
                    .endChoice()
                    .end()
                    .endChoice()
                    .end()
                    .process(exchange -> {
                        Kostenstelle kostenstelle = exchange.getIn().getBody(Kostenstelle.class);
                        NevarisAdressatenuebersicht oberbauleiter = exchange.getIn().getHeader("PROJEKTBETEILIGTE_TL", NevarisAdressatenuebersicht.class);
                        NevarisAdressatenuebersicht bauleiter = exchange.getIn().getHeader("PROJEKTBETEILIGTE_PL", NevarisAdressatenuebersicht.class);
                        NevarisAdressatenuebersicht kauffrau = exchange.getIn().getHeader("PROJEKTBETEILIGTE_KFM", NevarisAdressatenuebersicht.class);
                        KostenstellenData kostenstellenData = new KostenstellenData(kostenstelle, oberbauleiter, bauleiter, kauffrau);
                        exchange.getIn().setBody(kostenstellenData);
                    })
                    .marshal(jacksonDataFormatKostenstellenData)
                    .setHeader(DELIVERY_MODE, constant(2))
                    .to("rabbitmq:kostenstellen-single?exchangeType=topic&routingKey=" + mandant + "&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");

            from("rabbitmq:kostenstellen-single?exchangeType=topic&routingKey=" + mandant + "&queue=nevaris-" + mandantMapToNevaris.get(mandant) + ".kostenstellen-single&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapToNevaris.get(mandant) + ".kostenstellen-single.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".kostenstellen-single&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("rabbitmqKostenstellenSingle" + mandantMapToNevaris.get(mandant))
                    .removeHeaders("Camel*", DELIVERY_MODE+"|MESSAGE_ID|DELIVERY_MODE")
                    .unmarshal(jacksonDataFormatKostenstellenData)
                    .toD("sql:SELECT dst_niederlassung FROM mapping.niederlassungen WHERE mandant = '" + mandant + "' AND src_niederlassung = '${body.kostenstelle.niederlassung}'?outputType=SelectOne&outputHeader=NEVARIS_NIEDERLASSUNG")
                    .process(exchange -> {
                        KostenstellenData kostenstellenData = exchange.getIn().getBody(KostenstellenData.class);
                        exchange.getIn().setHeader("NEVARIS_KOSTENSTELLE", kostenstellenData.kostenstelle().id().replace("{{MANDANT}}", mandantMapToNevarisNummer.get(mandant)));
                        exchange.getIn().setHeader("NEVARIS_MANDANT", mandantMapToNevaris.get(mandant));
                    })
                    .enrich("direct:enrichCostcenter", (oldExchange, newExchange) -> {
                        NevarisCostCenter nevarisCostCenter = newExchange.getIn().getBody(NevarisCostCenter.class);
                        if (nevarisCostCenter != null) {
                            oldExchange.getIn().setHeader("NEVARIS_KOSTENSTELLE", nevarisCostCenter.control2());
                            oldExchange.getIn().setHeader("NEVARIS_KOSTENSTELLE_E_TAG", nevarisCostCenter.odataEtag());
                            oldExchange.getIn().setHeader("NEVARIS_KOSTENSTELLE_DATA", nevarisCostCenter);
                        }
                        return oldExchange;
                    })
                    .choice()
                    .when(kostenstelleIsNew)
                    .log("kostenstelleIsNew")
                    .enrich("direct:createNevarisKostenstelle", (oldExchange, newExchange) -> {
                        NevarisCostCenter nevarisCostCenter = newExchange.getIn().getBody(NevarisCostCenter.class);
                        if (nevarisCostCenter != null) {
                            oldExchange.getIn().setHeader("NEVARIS_KOSTENSTELLE", nevarisCostCenter.control2());
                        }
                        oldExchange.getIn().setHeader("NEVARIS_KOSTENSTELLE_E_TAG", nevarisCostCenter.odataEtag());
                        return oldExchange;
                    })
                    .otherwise()
                    .log("kostenstelleIsNew Otherwise")
                    .enrich("direct:updateNevarisKostenstelle", (oldExchange, newExchange) -> oldExchange)
                    .end()
                    .enrich("direct:transmitAuftragswert", (oldExchange, newExchange) -> oldExchange)
                    //.choice() // FIXME
                    //.when(kostenstelleIsNew)
                        .to("direct:projektbeteiligteToNevaris")
                    //.end()
                    .to("log:kostenstellenResponse?showHeaders=true&showExchangeId=true");
        });

        from("direct:enrichCostcenter")
                .routeId("enrichCostcenter")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*", "MESSAGE_ID|DELIVERY_MODE")
                .to("log:enrichCostcenter?showHeaders=true&showExchangeId=true")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/mcCostcenter('${headers.NEVARIS_KOSTENSTELLE}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:enrichCostcenter?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisCostCenter);

        from("direct:enrichProjektbeteiligteKfm")
                .routeId("enrichProjektbeteiligteKfm")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*","MESSAGE_ID|DELIVERY_MODE")
                .toD("sql:SELECT adressnr FROM mapping.adressaten WHERE personalnr = '${body.funktionKfm}' AND mandant = '${headers.MANDANT}'?outputType=SelectOne&outputHeader=ADRESSNR_KFM")
                .removeHeaders("Content-Type","MESSAGE_ID|DELIVERY_MODE")
                .setHeader("FUNKTION_KFM", simple("${body.funktionKfm}"))
                .to("log:enrichProjektbeteiligteKfm?showHeaders=true&showExchangeId=true")
                .log("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.ADRESSNR_KFM}','${body.funktionKfm}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.ADRESSNR_KFM}','${body.funktionKfm}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:enrichProjektbeteiligteKfm?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisAdressatenuebersicht)
                .otherwise()
                .throwException(Exception.class, "Projektbeteiligte Kfm nicht gefunden: AdressNr: ${headers.ADRESSNR_KFM}, Adressat: ${headers.FUNKTION_KFM}, Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}, Body: ${body}, Headers: ${headers}")
                .end();

        from("direct:enrichProjektbeteiligtePl")
                .routeId("enrichProjektbeteiligtePl")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*","MESSAGE_ID|DELIVERY_MODE")
                .removeHeaders("Content-Type")
                .setHeader("FUNKTION_PL", simple("${body.funktionPl}"))
                .toD("sql:SELECT adressnr FROM mapping.adressaten WHERE personalnr = '${headers.FUNKTION_PL}' AND mandant = '${headers.MANDANT}'?outputType=SelectOne&outputHeader=ADRESSNR_PL")
                .to("log:enrichProjektbeteiligtePl?showHeaders=true&showExchangeId=true")
                .log("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.ADRESSNR_PL}','${headers.FUNKTION_PL}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.ADRESSNR_PL}','${headers.FUNKTION_PL}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:enrichProjektbeteiligtePl?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisAdressatenuebersicht)
                .otherwise()
                .throwException(Exception.class, "Projektbeteiligte Pl nicht gefunden: AdressNr: ${headers.ADRESSNR_PL}, Adressat: ${headers.FUNKTION_PL}, Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}, Body: ${body}, Headers: ${headers}")
                .end();

        from("direct:enrichProjektbeteiligteTl")
                .routeId("enrichProjektbeteiligteTl")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .setHeader("FUNKTION_TL", simple("${body.funktionTl}"))
                .toD("sql:SELECT adressnr FROM mapping.adressaten WHERE personalnr = '${headers.FUNKTION_TL}' AND mandant = '${headers.MANDANT}'?outputType=SelectOne&outputHeader=ADRESSNR_TL")
                .to("log:enrichProjektbeteiligteTl?showHeaders=true&showExchangeId=true")
                .log("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.ADRESSNR_TL}','${headers.FUNKTION_TL}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.ADRESSNR_TL}','${headers.FUNKTION_TL}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:enrichProjektbeteiligteTl?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisAdressatenuebersicht)
                .otherwise()
                .throwException(Exception.class, "Projektbeteiligte Tl nicht gefunden: AdressNr: ${headers.ADRESSNR_TL}, Adressat: ${headers.FUNKTION_TL}, Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}, Body: ${body}, Headers: ${headers}")
                .end();

        from("direct:createNevarisKostenstelle")
                .routeId("createNevarisKostenstelle")
                .process(new KostenstelleToNevarisCostCenterProcessor())
                .marshal(jacksonDataFormatNevarisCostCenter)
                .convertBodyTo(String.class, "UTF-8")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .to("log:kostenllenBody?showHeaders=true&showExchangeId=true")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/mcCostcenter?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=POST")
                .streamCaching()
                .to("log:createNevarisKostenstelle?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisCostCenter)
                .otherwise()
                .throwException(Exception.class, "Fehler beim Erstellen der Kostenstelle: Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}, Headers: ${headers}, Body: ${body}")
                .end();

        from("direct:updateNevarisKostenstelle")
                .routeId("updateNevarisKostenstelle")
                .log("${body}")
                .setHeader("If-Match", header("NEVARIS_KOSTENSTELLE_E_TAG"))
                .process(new KostenstelleToNevarisCostCenterProcessor())
                .log(LoggingLevel.DEBUG,"updateNevarisKostenstelle after Processor: ${body}")
                .marshal(jacksonDataFormatNevarisCostCenter)
                .log(LoggingLevel.DEBUG,"updateNevarisKostenstelle jacksonDataFormatNevarisCostCenter result: ${body}")
                .convertBodyTo(String.class, "UTF-8")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .to("log:kostenllenBody?showHeaders=true&showExchangeId=true")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/mcCostcenter('${headers.NEVARIS_KOSTENSTELLE}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=PATCH")
                .streamCaching()
                .to("log:updateNevarisKostenstelle?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusNOk)
                .throwException(Exception.class, "Fehler beim Aktualisieren der Kostenstelle: Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}, Headers: ${headers}, Body: ${body}")
                .end();

        from("direct:getAuftragswerteFromKostenstelle")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/Kostenstellen_Auftragswerte?$filter=Kostenstelle eq '${headers.NEVARIS_KOSTENSTELLE}'&clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .log(LoggingLevel.DEBUG, "Existing Auftragswerte: ${body}")
                .unmarshal(jacksonDataFormatKostenstelleAuftragswertListe)
        ;

        from("direct:transmitAuftragswert")
                .id("transmitAuftragswert")
                .enrich("direct:getAuftragswerteFromKostenstelle", (oldExchange, newExchange) -> {
                    NevarisKostenstellenAuftragswertListe auftragswerte = newExchange.getIn().getBody(NevarisKostenstellenAuftragswertListe.class);
                    double auftragswertSumme = 0.0;
                    if(auftragswerte != null) {
                        for (NevarisKostenstellenAuftragswert auftragswert : auftragswerte.values()) {
                            auftragswertSumme += auftragswert.auftragswert();
                        }
                    }
                    oldExchange.getIn().setHeader("AUFTRAGSWERTESUMME", auftragswertSumme);
                    return oldExchange;
                })
                .process(new KostenstelleAuftragswertProcessor())
                .choice()
                .when(hasAuftragswert)
                    .marshal(jacksonDataFormatKostenstelleAuftragswert)
                    .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                    .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                    .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/Kostenstellen_Auftragswerte?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=POST")
                    .choice()
                    .when(httpStatusNOk)
                        .throwException(Exception.class, "Fehler beim Anlegen des Auftragswerts, Headers: ${headers}, Body: ${body}")
                    .end()
                .end()
;


        from("direct:enrichBauleiter")
                .routeId("enrichBauleiter")
                .setHeader("BAULEITER", simple("${body.funktionTl}"))
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .toD("sql:SELECT adressnr FROM mapping.adressaten WHERE personalnr = '${headers.FUNKTION_TL}' AND mandant = '${headers.MANDANT}'?outputType=SelectOne&outputHeader=ADRESSNR_BAULEITER")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.ADRESSNR_BAULEITER}','${headers.BAULEITER}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:enrichProjektbeteiligteTl?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisAdressatenuebersicht)
                .otherwise()
                .throwException(Exception.class, "Bauleiter nicht gefunden: AdressNr: ${headers.ADRESSNR_BAULEITER}, Adressat: ${headers.BAULEITER}, Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}, Body: ${body}, Headers: ${headers}")
        ;

        from("direct:updateAdressTabelleForBaustelle")
                .routeId("updateAdressTabelleForBaustelle")
                .setHeader(SQL_RETRIEVE_GENERATED_KEYS, constant(true))
                .toD("sql:SELECT id FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND source_system_adress_id = '${headers.SRC_ADRESS_ID}' AND source_kostenstelle = '${headers.SRC_KOSTENSTELLE}'?outputType=SelectOne&outputHeader=EXTERNE_ADRESS_NR")
                .choice()
                .when(adresseIstNeu)
                .toD("sql:INSERT INTO mapping.adressen (source_system_name, mandant, source_system_adress_id, created_at, adress_key, source_system_debitor_id, source_kostenstelle) VALUES" +
                        "('${headers.SOURCE_SYSTEM_NAME}', '${headers.MANDANT}', '${headers.SRC_ADRESS_ID}', NOW(), '${headers.NEVARIS_ADRESS_NR}', '${headers.SRC_DEBITOR_ID}', '${headers.SRC_KOSTENSTELLE}')")
                .process(exchange -> {
                    Map<String, Object> map = (Map<String, Object>) exchange.getIn().getHeader(SQL_GENERATED_KEYS_DATA, List.class).get(0);
                    exchange.getIn().setHeader("EXTERNE_ADRESS_NR", map.get("id"));
                });

        from("direct:updateNevarisAdressuebersichtFromKostenstelle")
                .routeId("updateNevarisAdressuebersicht")
                .process(new KostenstelleToNevarisAdressuebersichtProcessor())
                .setHeader("If-Match", header("NEVARIS_ADRESS_E_TAG"))
                .marshal(jacksonDataFormatNevarisAdressuebersicht)
                .convertBodyTo(String.class, "UTF-8")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressuebersicht('${headers.NEVARIS_ADRESS_NR}')?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=PATCH&throwExceptionOnFailure=false")
                .streamCaching()
                .to("log:updateAdressuebersichtFromKostenstelle?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisAdressuebersicht)
                .otherwise()
                .throwException(Exception.class, "Fehler beim Updaten der Adressuebersicht: AdressNr: ${headers.NEVARIS_ADRESS_NR}, Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}");

        from("direct:createNevarisAdressuebersichtFromKostenstelle")
                .routeId("createNevarisAdressuebersicht")
                .process(new KostenstelleToNevarisAdressuebersichtProcessor())
                .marshal(jacksonDataFormatNevarisAdressuebersicht)
                .convertBodyTo(String.class, "UTF-8")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .to("log:createNevarisAdressuebersichtFromKostenstelle?showHeaders=true&showExchangeId=true")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressuebersicht?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=POST&throwExceptionOnFailure=false")
                .streamCaching()
                .to("log:createAdressuebersichtFromKostenstelle?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisAdressuebersicht)
                .otherwise()
                .throwException(Exception.class, "Fehler beim Anlegen der Adressuebersicht: Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}");

        from("direct:projektbeteiligteToNevaris")
                .routeId("projektbeteiligteToNevaris")
                .process(new KostenstelleToProjektbeteiligteProcessor())
                .log("exchange: ${headers} ${body}")
                .enrich("direct:deleteNevarisProjektbeteiligte_KSt", (oldExchange, newExchange) -> oldExchange)
                .split(body())
                .to("direct:rest.api.projektbeteiligte")
                .end();

        from("direct:deleteNevarisProjektbeteiligte_KSt")
                .routeId("deleteNevarisProjektbeteiligte_KSt")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/Projektbeteiligte_KSt?$filter=Kostenstelle eq '${headers.NEVARIS_KOSTENSTELLE}'&clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=GET&throwExceptionOnFailure=false")
                .streamCaching()
                .log("${headers} ${body}")
                .choice()
                .when(httpStatusNOk)
                .throwException(Exception.class, "Fehler beim Abrufen der Projektbeteiligten der Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}")
                .otherwise()
                .unmarshal(jacksonDataFormatProjektbeteiligte_KStList)
                .process(exchange -> {
                    Projektbeteiligte_KStList projektbeteiligteKStList = exchange.getIn().getBody(Projektbeteiligte_KStList.class);
                    exchange.getIn().setBody(projektbeteiligteKStList.values());
                })
                .split(body())
                .setHeader("If-Match", simple("${body.odataEtag}"))
                .process(exchange -> {
                    Projektbeteiligte_KSt projektbeteiligteKSt = exchange.getIn().getBody(Projektbeteiligte_KSt.class);
                    exchange.getIn().setHeader("PKST_KOSTENSTELLE", projektbeteiligteKSt.kostenstelle());
                    exchange.getIn().setHeader("PKST_ADRESSNR", projektbeteiligteKSt.adressnr());
                    exchange.getIn().setHeader("PKST_ADRESSAT", projektbeteiligteKSt.adressat());
                    exchange.getIn().setHeader("PKST_FUNKTION", projektbeteiligteKSt.funktion());
                    exchange.getIn().setHeader("PKST_VON", projektbeteiligteKSt.von());
                })
                .marshal(jacksonDataFormatProjektbeteiligte_KStList)
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .log("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/Projektbeteiligte_KSt('${headers.PKST_KOSTENSTELLE}','${headers.PKST_ADRESSNR}','${headers.PKST_ADRESSAT}','${headers.PKST_FUNKTION}',${headers.PKST_VON})?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=DELETE&throwExceptionOnFailure=false")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/Projektbeteiligte_KSt('${headers.PKST_KOSTENSTELLE}','${headers.PKST_ADRESSNR}','${headers.PKST_ADRESSAT}','${headers.PKST_FUNKTION}',${headers.PKST_VON})?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=DELETE&throwExceptionOnFailure=false")
                .convertBodyTo(String.class, "UTF-8")
                .choice()
                .when(httpStatusNOk)
                .throwException(Exception.class, "Fehler beim Löschen der Projektbeteiligten: Kostenstelle: ${headers.PKST_KOSTENSTELLE}, Adressnr: ${headers.PKST_ADRESSNR}, Adressat: ${headers.PKST_ADRESSAT}, Funktion: ${headers.PKST_FUNKTION}, Von: ${headers.PKST_VON}")
                .end()
                .end()
                .end();

        from("direct:rest.api.projektbeteiligte")
                .routeId("restApiProjektbeteiligte")
                .marshal(jacksonDataFormatProjektbeteiligter_KSt)
                .convertBodyTo(String.class, "UTF-8")
                //.log("REST-Payload: ${body}")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .to("log:NevarisProjektbeteiligteInsert?showHeaders=true&showExchangeId=true")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/Projektbeteiligte_KSt?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=POST&throwExceptionOnFailure=false")
                .streamCaching()
                //.log("Response: ${body}")
                .choice()
                .when(httpStatusNOk)
                .throwException(Exception.class, "Fehler beim Zuweisen der Projektbeteiligten: Kostenstelle: ${headers.PKST_KOSTENSTELLE}, Body: ${body}")
                .end();

        from("direct:fetchKostenstelleFromBroker.getAdressuebersichtFromNevaris")
                .routeId("fetchKostenstelleFromBroker.getAdressuebersichtFromNevaris")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressuebersicht('${headers.NEVARIS_ADRESS_NR}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:fetchKostenstelleFromBroker.getAdressuebersichtFromNevaris?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(jacksonDataFormatNevarisAdressuebersicht);

        from("direct:generateBaustellenAdressnummer")
                .routeId("generateBaustellenAdressnummer")
                .to("sql:SELECT current_value FROM number_generator.number_generator WHERE entity = '" + ENTITY_KEY_BAUSTELLEN_ADRESSE + "' AND mandant = ''?outputType=SelectOne&outputHeader=CURRENT_VALUE")
                .process(exchange -> {
                    Long currentValue = exchange.getIn().getHeader("CURRENT_VALUE", Long.class);
                    if (currentValue == null) {
                        currentValue = START_NUMBER_VALUE_BAUSTELLEN_ADRESSE;
                    }
                    exchange.getIn().setBody(currentValue + 1);
                    exchange.getIn().setHeader("ENTITY_KEY", ENTITY_KEY_BAUSTELLEN_ADRESSE);
                })
                .to("direct:saveNummerngeneratorNummer");

        from("direct:createBaustellenAdressatFromKonzernAdressat")
                .routeId("createBaustellenAdressatFromKonzernAdressat")
                .toD("sql:SELECT adressnr FROM mapping.adressaten WHERE personalnr = '${body.funktionPl}' AND mandant = '${headers.MANDANT}'?outputType=SelectOne&outputHeader=KONZERN_ADRESS_NR")
                .process(createBaustellenAdressatFromKonzernAdressatProcessor)
                .end();

        from("direct:kostenstellenStandardadressatZuweisen")
                .routeId("kostenstellenStandardadressatZuweisen")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .removeHeaders("Content-Type")
                .to("log:kostenstellenStandardadressatZuweisen?showHeaders=true")
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressuebersicht('${headers.NEVARIS_ADRESS_NR}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:kostenstellenStandardadressatZuweisen?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusNOk)
                .throwException(Exception.class, "Fehler beim Ermitteln des Standardadressaten: Adressnr: ${headers.NEVARIS_ADRESS_NR}, Headers: ${headers}, Body: ${body}")
                .otherwise()
                .unmarshal(jacksonDataFormatNevarisAdressuebersicht)
                .process(exchange -> {
                    NevarisAdressuebersicht nevarisAdressuebersicht = exchange.getIn().getBody(NevarisAdressuebersicht.class);
                    NevarisAdressatenuebersicht pl = exchange.getIn().getHeader("PROJEKTBETEILIGTE_PL", NevarisAdressatenuebersicht.class);
                    exchange.getIn().setHeader("If-Match", nevarisAdressuebersicht.odataEtag());
                    NevarisAdressuebersicht nevarisAdressuebersicht1 = new NevarisAdressuebersicht(
                            null,
                            null,
                            nevarisAdressuebersicht.adressnr(),
                            nevarisAdressuebersicht.nameLang(),
                            nevarisAdressuebersicht.adresseLang(),
                            nevarisAdressuebersicht.adresse2Lang(),
                            nevarisAdressuebersicht.adresse3Lang(),
                            nevarisAdressuebersicht.strasseLang(),
                            nevarisAdressuebersicht.name(),
                            nevarisAdressuebersicht.adresse(),
                            nevarisAdressuebersicht.adresse2(),
                            nevarisAdressuebersicht.adresse3(),
                            nevarisAdressuebersicht.strasse(),
                            nevarisAdressuebersicht.laendercode(),
                            nevarisAdressuebersicht.plz(),
                            nevarisAdressuebersicht.ort(),
                            nevarisAdressuebersicht.suchbegriff(),
                            pl.adressat(),
                            nevarisAdressuebersicht.plzPostfach(),
                            nevarisAdressuebersicht.ortPostfach(),
                            nevarisAdressuebersicht.postfachNutzen(),
                            nevarisAdressuebersicht.postfach(),
                            nevarisAdressuebersicht.sprache(),
                            nevarisAdressuebersicht.bundesland(),
                            nevarisAdressuebersicht.landkreis(),
                            nevarisAdressuebersicht.auslandsvorwahl(),
                            nevarisAdressuebersicht.ortskennzahl(),
                            nevarisAdressuebersicht.hauptanschlussnr(),
                            nevarisAdressuebersicht.durchwahlZentrale(),
                            nevarisAdressuebersicht.durchwahlFax(),
                            nevarisAdressuebersicht.durchwahlModem(),
                            nevarisAdressuebersicht.telefonnrKompl(),
                            nevarisAdressuebersicht.faxnrKompl(),
                            nevarisAdressuebersicht.modemnrKompl(),
                            nevarisAdressuebersicht.eMail(),
                            nevarisAdressuebersicht.wwwAdresseURL(),
                            nevarisAdressuebersicht.adressnrDerZentrale(),
                            nevarisAdressuebersicht.adressnrDerOrganschaft(),
                            nevarisAdressuebersicht.datenquelle(),
                            nevarisAdressuebersicht.gruppe(),
                            nevarisAdressuebersicht.externeAdressnr(),
                            nevarisAdressuebersicht.duplikat(),
                            nevarisAdressuebersicht.unsereKundennrDort(),
                            nevarisAdressuebersicht.adresszeile2KomplLang(),
                            nevarisAdressuebersicht.adresszeile3KomplLang(),
                            nevarisAdressuebersicht.adresszeile4KomplLang(),
                            nevarisAdressuebersicht.adresszeile2Kompl(),
                            nevarisAdressuebersicht.adresszeile3Kompl(),
                            nevarisAdressuebersicht.adresszeile4Kompl(),
                            nevarisAdressuebersicht.adressnrPrimaererBetreuer(),
                            nevarisAdressuebersicht.adressatPrimaererBetreuer(),
                            nevarisAdressuebersicht.debitorVorhanden(),
                            nevarisAdressuebersicht.kreditorVorhanden(),
                            nevarisAdressuebersicht.lieferantVorhanden(),
                            nevarisAdressuebersicht.aehnlichkeit(),
                            nevarisAdressuebersicht.gesperrt(),
                            nevarisAdressuebersicht.sperrhinweis(),
                            nevarisAdressuebersicht.gueltigAb(),
                            nevarisAdressuebersicht.gueltigBis(),
                            nevarisAdressuebersicht.handelsregister(),
                            nevarisAdressuebersicht.neuanlagesystem(),
                            nevarisAdressuebersicht.neuanlagebenutzer(),
                            nevarisAdressuebersicht.neuanlagedatum(),
                            nevarisAdressuebersicht.neuanlagezeit(),
                            nevarisAdressuebersicht.aenderungssystem(),
                            nevarisAdressuebersicht.aenderungsbenutzer(),
                            nevarisAdressuebersicht.aenderungsdatum(),
                            nevarisAdressuebersicht.aenderungszeit(),
                            nevarisAdressuebersicht.verweisAufAdressnr(),
                            nevarisAdressuebersicht.qmSachbearbeiter(),
                            nevarisAdressuebersicht.gueltigkeitDesZertifikats(),
                            nevarisAdressuebersicht.neuzertifizierung(),
                            nevarisAdressuebersicht.zertifikat(),
                            nevarisAdressuebersicht.zertifizierung(),
                            nevarisAdressuebersicht.internePruefung(),
                            nevarisAdressuebersicht.ursprungsnr(),
                            nevarisAdressuebersicht.ustIdNr(),
                            nevarisAdressuebersicht.steuernummerGesellschaft(),
                            nevarisAdressuebersicht.vorname(),
                            nevarisAdressuebersicht.nachname(),
                            nevarisAdressuebersicht.namenskuerzel(),
                            nevarisAdressuebersicht.anrede(),
                            nevarisAdressuebersicht.titel(),
                            nevarisAdressuebersicht.titelImAnschreiben(),
                            nevarisAdressuebersicht.geburtsdatum(),
                            nevarisAdressuebersicht.vollstaendigkeitProzent(),
                            nevarisAdressuebersicht.lguFirmencode(),
                            nevarisAdressuebersicht.mitInformNichtSynchr()
                    );
                    exchange.getIn().setBody(nevarisAdressuebersicht1);
                })
                .marshal(jacksonDataFormatNevarisAdressuebersicht)
                .convertBodyTo(String.class, "UTF-8")
                .to("log:kostenstellenStandardadressatZuweisen?showHeaders=true&showExchangeId=true")
                .removeHeaders("Camel*|Transfer-Encoding|Vary|Access*")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .toD("http://" + nevarisHost + defaultBasePath + "/Company('${headers.NEVARIS_MANDANT}')/adressuebersicht('${headers.NEVARIS_ADRESS_NR}')?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=PATCH&throwExceptionOnFailure=false")
                .convertBodyTo(String.class, "UTF-8")
                .to("log:kostenstellenStandardadressatZuweisen?showHeaders=true&showExchangeId=true")
                .choice()
                .when(httpStatusNOk)
                .throwException(Exception.class, "Fehler beim Zuweisen des Standardadressaten: Kostenstelle: ${headers.NEVARIS_KOSTENSTELLE}, Adressnr: ${headers.NEVARIS_ADRESS_NR} Body: ${body}")
                .end();

    }

    private String getBauleiterKey(NevarisAdressatenuebersicht nevarisAdressatenuebersicht) {
        if (nevarisAdressatenuebersicht == null) {
            return null;
        }
        String name = nevarisAdressatenuebersicht.name().toUpperCase();
        name = name.replaceAll("Ä", "AE");
        name = name.replaceAll("Ö", "OE");
        name = name.replaceAll("Ü", "UE");
        name = name.replaceAll("ß", "SS");

        String vorname = nevarisAdressatenuebersicht.vorname().toUpperCase();
        vorname = vorname.replaceAll("Ä", "AE");
        vorname = vorname.replaceAll("Ö", "OE");
        vorname = vorname.replaceAll("Ü", "UE");
        vorname = vorname.replaceAll("ß", "SS");

        return name.substring(0, Math.min(7, name.length())) + vorname.substring(0, 3);
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisKostenStellenCostCenterImportErrorMessageTransformer("Kostenstelle", "NEVARIS", originQueueName, mandantMapToNevaris);
    }
}
