package de.systeex.cip.nevaris.kostenstellen.json_producer.infrastructure;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.net.URL;
import java.util.concurrent.TimeUnit;

public class CreateBaustellenAdressatFromKonzernAdressatProcessor implements Processor {

    private final BasicCredentialsProvider credentialsProvider;
    private final String nevarisHost;
    private final String nevarisBasePath;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public CreateBaustellenAdressatFromKonzernAdressatProcessor(String username, String password, String workstation, String domain, String nevarisHost, String nevarisBasePath) {
        Credentials credentials = new NTCredentials(username, password, workstation, domain);
        this.credentialsProvider = new BasicCredentialsProvider();
        this.credentialsProvider.setCredentials(AuthScope.ANY, credentials);
        this.nevarisHost = nevarisHost;
        this.nevarisBasePath = nevarisBasePath;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        String konzernAdressNr = exchange.getIn().getHeader("KONZERN_ADRESS_NR", String.class);
        String nevarisMandant = exchange.getIn().getHeader("NEVARIS_MANDANT", String.class);
        String nevarisAdressNr = exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class);
        Kostenstelle kostenstelle = exchange.getIn().getBody(Kostenstelle.class);

        HttpGet httpGet;
        HttpPost httpPost;
        URL url;
        URI uri;
        CloseableHttpResponse response;
        String responseBody;

        assert kostenstelle.baustelle() != null;
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .setMaxConnPerRoute(100000)
                .build()) {

            url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht('" + konzernAdressNr + "','" + kostenstelle.funktionPl() + "')");
            uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            httpGet = new HttpGet(uri.toASCIIString());

            response = httpClient.execute(httpGet);
            responseBody = EntityUtils.toString(response.getEntity());
            if (response.getStatusLine().getStatusCode() == 200) {
                NevarisAdressatenuebersicht nevarisAdressatenuebersicht = objectMapper.readValue(responseBody, NevarisAdressatenuebersicht.class);

                url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht('" + nevarisAdressNr + "','" + kostenstelle.funktionPl() + "')");
                uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                httpGet = new HttpGet(uri.toASCIIString());

                response = httpClient.execute(httpGet);
                if (response.getStatusLine().getStatusCode() == 404) {
                    NevarisAdressatenuebersicht baustellenAdressatenuebersicht = new NevarisAdressatenuebersicht(
                            null,
                            null,
                            nevarisAdressNr,
                            nevarisAdressatenuebersicht.adressat(),
                            nevarisAdressatenuebersicht.adressbezeichnungLang(),
                            nevarisAdressatenuebersicht.anrede(),
                            nevarisAdressatenuebersicht.vorname(),
                            nevarisAdressatenuebersicht.name(),
                            nevarisAdressatenuebersicht.titel(),
                            nevarisAdressatenuebersicht.titelImAnschreiben(),
                            nevarisAdressatenuebersicht.durchwahl(),
                            nevarisAdressatenuebersicht.fax(),
                            nevarisAdressatenuebersicht.telefonnrKompl(),
                            nevarisAdressatenuebersicht.faxnrKompl(),
                            nevarisAdressatenuebersicht.mobiltelefon(),
                            nevarisAdressatenuebersicht.funktion(),
                            nevarisAdressatenuebersicht.abteilung(),
                            nevarisAdressatenuebersicht.eMail(),
                            nevarisAdressatenuebersicht.wwwAdresseURL(),
                            nevarisAdressatenuebersicht.adressnrPrivat(),
                            nevarisAdressatenuebersicht.geburtsdatum(),
                            nevarisAdressatenuebersicht.raum(),
                            nevarisAdressatenuebersicht.externeAdressnr(),
                            nevarisAdressatenuebersicht.inaktiv(),
                            nevarisAdressatenuebersicht.austrittsdatum(),
                            nevarisAdressatenuebersicht.sprache(),
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null
                    );

                    url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht");
                    uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                    httpPost = new HttpPost(uri.toASCIIString());
                    httpPost.setEntity(new StringEntity(objectMapper.writeValueAsString(baustellenAdressatenuebersicht), ContentType.APPLICATION_JSON));
                    response = httpClient.execute(httpPost);

                    responseBody = EntityUtils.toString(response.getEntity());
                    if (response.getStatusLine().getStatusCode() < 200 || response.getStatusLine().getStatusCode() >= 300) {
                        throw new Exception("Fehler beim Erstellen des Baustellenadressaten: Kostenstelle: " + exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE", String.class) + ", Adresse '" + nevarisAdressNr + "', FunktionPL: '" + kostenstelle.funktionPl() + "', Body: " + responseBody + ", Header: " + response.getStatusLine().toString());
                    } else {
                        System.out.println("Baustellenadressat erfolgreich erstellt: Adresse '" + nevarisAdressNr + "', FunktionPL: '" + kostenstelle.funktionPl() + "'");
                    }
                }
            } else {
                throw new Exception("Fehler beim Ermitteln des Konzernadessaten: Adressnr: '" + konzernAdressNr + "', FunktionPL: '" + kostenstelle.funktionPl() +  "', Response: '" + responseBody + "', Header: '" + response.getStatusLine().toString() + "'");
            }
        }
    }
}
