package de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure;

import de.systeex.cip.types.ErrSrcType;
import systeex.cip.error_handler.ErrorMessageTransformer;

import java.util.Map;

public class NevarisFetchCostcenterErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisFetchCostcenterErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.CRON;
    }
}
