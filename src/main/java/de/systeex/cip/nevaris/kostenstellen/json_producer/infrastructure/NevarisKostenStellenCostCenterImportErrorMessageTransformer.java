package de.systeex.cip.nevaris.kostenstellen.json_producer.infrastructure;

import de.systeex.cip.types.ErrSrcType;
import systeex.cip.error_handler.ErrorMessageTransformer;

import java.util.Map;

public class NevarisKostenStellenCostCenterImportErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisKostenStellenCostCenterImportErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.QUEUE;
    }
}
