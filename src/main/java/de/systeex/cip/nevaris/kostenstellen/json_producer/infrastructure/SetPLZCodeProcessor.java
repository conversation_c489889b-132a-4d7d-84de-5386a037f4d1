package de.systeex.cip.nevaris.kostenstellen.json_producer.infrastructure;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCode;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCodeList;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.net.URL;
import java.util.concurrent.TimeUnit;

public class SetPLZCodeProcessor implements Processor {

    private final BasicCredentialsProvider credentialsProvider;
    private final String nevarisHost;
    private final String nevarisBasePath;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public SetPLZCodeProcessor(String username, String password, String workstation, String domain, String nevarisHost, String nevarisBasePath) {
        Credentials credentials = new NTCredentials(username, password, workstation, domain);
        this.credentialsProvider = new BasicCredentialsProvider();
        this.credentialsProvider.setCredentials(AuthScope.ANY, credentials);
        this.nevarisHost = nevarisHost;
        this.nevarisBasePath = nevarisBasePath;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        HttpGet httpGet;
        URL url;
        URI uri;
        String nevarisMandant = exchange.getIn().getHeader("NEVARIS_MANDANT", String.class);
        Kostenstelle kostenstelle = exchange.getIn().getBody(Kostenstelle.class);
        CloseableHttpResponse response;
        String responseBody;


        assert kostenstelle.baustelle() != null;
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .setMaxConnPerRoute(100000)
                .build()) {

            url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/PLZCodes('" + kostenstelle.baustelle().plz() + "', '" + kostenstelle.baustelle().ort() + "')");
            uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            httpGet = new HttpGet(uri.toASCIIString());

            response = httpClient.execute(httpGet);

            if (response.getStatusLine().getStatusCode() == 200) {
                responseBody = EntityUtils.toString(response.getEntity());
                exchange.getIn().setHeader("NEVARIS_PLZ_CODE", objectMapper.readValue(responseBody, NevarisPLZCode.class));
            } else {
                url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/PLZCodes?$filter=Code eq '" + kostenstelle.baustelle().plz() + "'");
                uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                httpGet = new HttpGet(uri.toASCIIString());
                response = httpClient.execute(httpGet);
                responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() == 200) {
                    NevarisPLZCodeList nevarisPLZCodeList = objectMapper.readValue(responseBody, NevarisPLZCodeList.class);
                    if (nevarisPLZCodeList.values().size() == 1) {
                        exchange.getIn().setHeader("NEVARIS_PLZ_CODE", nevarisPLZCodeList.values().get(0));
                    } else {
                        throw new Exception("Failed to get PLZ code from Nevaris. PLZ: '" + kostenstelle.baustelle().plz() + "', Ort: '" + kostenstelle.baustelle().ort() + "'");
                    }
                } else {
                    throw new Exception("Failed to get PLZ code from Nevaris. Response: " + responseBody);
                }
            }
        }
    }
}
