package de.systeex.cip.nevaris.kostenstellen.json_consumer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.application.NevarisService;
import de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure.EnrichMitAdresseProcessor;
import de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure.NevarisCostcenterToKostenstelleProcessor;
import de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure.NevarisFetchCostcenterErrorMessageTransformer;
import de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure.PollNevarisKostenstellenProcessor;

import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.ExchangePattern;
import org.apache.camel.LoggingLevel;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class FetchCostcentersFromNevarisRoute extends ErrorResistantRouteBuilder {

    @Value("${poll.cron.kostenstelle}")
    private String cronExpression;

    @Value("#{${mandant.mapping.kostenstelle}}")
    private Map<String, String> mandantMapToNevaris;

    private final NevarisService nevarisService;

    @Autowired
    public FetchCostcentersFromNevarisRoute(NevarisService nevarisService) {
        this.nevarisService = nevarisService;
    }

    @Override
    public void configure() throws Exception {
        super.configure();
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat kostenstelleJacksonDataFormat = new JacksonDataFormat(mapper, Kostenstelle.class);

        mandantMapToNevaris.keySet().forEach(mandant -> {
            from("quartz://nevarisCostcenters" + mandantMapToNevaris.get(mandant) + "?cron=" + cronExpression.replace(' ', '+'))
                    .routeId("nevarisCostcenterQuartz" + mandantMapToNevaris.get(mandant))
                    .log(LoggingLevel.INFO, "Started scheduled route: nevarisCostcenterQuartz" + mandantMapToNevaris.get(mandant))
                    .setHeader("MANDANT", constant(mandant))
                    .process(new PollNevarisKostenstellenProcessor(mandantMapToNevaris.get(mandant), nevarisService)).id("pollNevarisKostenstellenProcessor" + mandantMapToNevaris.get(mandant))
                    .split(body())
                        .log(LoggingLevel.DEBUG, "processing ${headers} ${body}")
                        .process(new EnrichMitAdresseProcessor(mandantMapToNevaris.get(mandant), nevarisService)).id("enrichMitAdresseProcessor" + mandantMapToNevaris.get(mandant))
                        .to("direct:TransformAndSendToBroker");

            });

        from("direct:TransformAndSendToBroker")
                .routeId("transformAndSendToBroker")
                .process(new NevarisCostcenterToKostenstelleProcessor())
                .marshal(kostenstelleJacksonDataFormat)
                .removeHeaders("*", "MANDANT|ODATA_ETAG|MESSAGE_ID|DELIVERY_MODE")
                .setExchangePattern(ExchangePattern.InOnly)
                .setHeader(DELIVERY_MODE, constant(2))
                .toD("rabbitmq://kostenstellen?exchangeType=topic&routingKey=${headers.MANDANT}&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");

    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisFetchCostcenterErrorMessageTransformer("Kostenstelle", "NEVARIS", "nevarisCostcenterQuartz", mandantMapToNevaris);
    }
}
