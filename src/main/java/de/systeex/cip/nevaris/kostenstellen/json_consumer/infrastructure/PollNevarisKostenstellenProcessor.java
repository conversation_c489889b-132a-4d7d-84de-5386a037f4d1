package de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure;

import de.systeex.cip.nevaris.application.NevarisService;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenter;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenterList;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class PollNevarisKostenstellenProcessor implements Processor {

    private final String nevarisMandant;
    private final NevarisService nevarisService;

    public PollNevarisKostenstellenProcessor(String nevarisMandant, NevarisService nevarisService) {
        this.nevarisMandant = nevarisMandant;
        this.nevarisService = nevarisService;
    }

    @Override
    public void process(Exchange exchange) throws Exception {

        NevarisCostCenterList nevarisKostenstelleList = nevarisService.getKostenstellen(nevarisMandant, "?$filter=Ist_Baustelle eq true");

        List<NevarisCostCenter> nevarisKostenstellen = nevarisKostenstelleList.getValues();

        while (nevarisKostenstelleList.getoDataNextLink() != null) {
            String queryString = URLDecoder.decode(nevarisKostenstelleList.getoDataNextLink().substring(nevarisKostenstelleList.getoDataNextLink().indexOf("?")), StandardCharsets.UTF_8);
            nevarisKostenstelleList = nevarisService.getKostenstellen(nevarisMandant, queryString);
            if(nevarisKostenstelleList != null){
                nevarisKostenstellen.addAll(nevarisKostenstelleList.getValues());
            }else {
                throw new Exception("Nevaris Kostenstelle list is null: Query String: " + queryString);
            }
        }

        exchange.getIn().setBody(nevarisKostenstellen);
    }
}
