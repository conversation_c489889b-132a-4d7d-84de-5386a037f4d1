package de.systeex.cip.nevaris.kostenstellen.json_producer.infrastructure;

import de.systeex.cip.nevaris.adressen.json_producer.infrastructure.NameSplitter;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenter;
import de.systeex.cip.nevaris.kostenstellen.json_producer.domain.KostenstellenData;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

import static de.systeex.cip.nevaris.domain.odata.NevarisCostCenter.NULL_DATE;

public class KostenstelleToNevarisCostCenterUpdateProcessor implements Processor {

    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.of("Europe/Berlin");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public void process(Exchange exchange) throws Exception {
        KostenstellenData kostenstellenData = exchange.getIn().getBody(KostenstellenData.class);
        Kostenstelle kostenstelle = kostenstellenData.kostenstelle();
        NevarisCostCenter nevarisCostCenterExisting = exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE_DATA", NevarisCostCenter.class);
        String datumFertigstellung = plusDays(kostenstelle.datumSchlussgerechnet(), 90);
        String[] names = NameSplitter.splitName(kostenstelle.name(), 50);

        //some fields do need to be null, if not a new entry
        boolean isNew = exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE_E_TAG")== null;

        String sparte = nevarisCostCenterExisting != null ? nevarisCostCenterExisting.sparte() : kostenstelle.sparte();
        String ergebniszuordnung = nevarisCostCenterExisting != null ? nevarisCostCenterExisting.ergebniszuordnung() : kostenstelle.ergebniszuordnung();

        boolean niederlassungTranslationNotFound = Objects.toString(exchange.getIn().getHeader("NEVARIS_NIEDERLASSUNG", String.class), "").isEmpty();

        NevarisCostCenter costCenter = new NevarisCostCenter(
                null,
                null,
                truncate(exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE", String.class), 20),
                truncate(names[0], 50),
                truncate(names[1], 50),
                "",
                null,
                truncate(kostenstelle.suchbegriff(), 30),
                null,
                null,
                null,
                null,
                null,
                true,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                truncate(exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class), 10),
                null,
                null,
                null,
                null,
                Objects.toString(exchange.getIn().getHeader("BAULEITER_KEY", String.class), "").isEmpty() ? null : truncate(exchange.getIn().getHeader("BAULEITER_KEY", String.class), 10),
                null,
                Objects.toString(exchange.getIn().getHeader("OBERBAULEITER_KEY", String.class), "").isEmpty() ? null : truncate(exchange.getIn().getHeader("OBERBAULEITER_KEY", String.class), 10),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                isNew ? truncate(niederlassungTranslationNotFound ? kostenstelle.niederlassung() : exchange.getIn().getHeader("NEVARIS_NIEDERLASSUNG", String.class), 20) : null,
                isNew ? truncate(ergebniszuordnung, 10) : null,
                isNew ? truncate(sparte, 10) : null,
                isNew ? truncate(kostenstelle.typ(), 10) : null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                toLocalDate(kostenstelle.datumBaubeginn()),
                toLocalDate(kostenstelle.datumFertigstellung()),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                kostenstelle.datumSchlussgerechnet() != null,
                toLocalDate(kostenstelle.datumSchlussgerechnet()),
                null,
                null,
                datumFertigstellung,
                kostenstelle.datumSchlussgerechnet() != null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                toLocalDate(kostenstelle.gueltigAb()),
                toLocalDate(kostenstelle.gueltigBis()),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                truncate(exchange.getIn().getHeader("NEVARIS_MANDANT", String.class), 30),
                null
        );

        System.out.println("CostCenter in Processor:" + costCenter);
        exchange.getIn().setBody(costCenter);
    }

    private String toLocalDate(Instant dateTimeValue) {
        if (dateTimeValue == null) {
            return NULL_DATE;
        } else {
            return LocalDate.ofInstant(dateTimeValue, DEFAULT_ZONE_ID).format(DATE_FORMATTER);
        }
    }

    private String toLocalDate(LocalDate localDateValue) {
        if (localDateValue == null) {
            return NULL_DATE;
        } else {
            return localDateValue.format(DATE_FORMATTER);
        }
    }

    private String plusDays(LocalDate dateValue, int daysToAdd) {
        if (dateValue == null) {
            return NULL_DATE;
        } else {
            return toLocalDate(dateValue.plusDays(daysToAdd));
        }
    }

    private String truncate(String value, int length) {
        if (value == null)
            return "";

        return value.substring(0, Math.min(value.length(), length));
    }


}
