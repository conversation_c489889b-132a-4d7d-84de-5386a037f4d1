package de.systeex.cip.nevaris.kostenstellen.json_consumer.infrastructure;

import de.systeex.cip.nevaris.adressen.json_producer.NevarisAdressuebersicht;
import de.systeex.cip.nevaris.application.NevarisService;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenter;
import de.systeex.cip.nevaris.kostenstellen.json_consumer.domain.KostenstellenData;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

public class EnrichMitAdresseProcessor implements Processor {

    private final String nevarisMandant;
    private final NevarisService nevarisService;

    public EnrichMitAdresseProcessor(String nevarisMandant, NevarisService nevarisService) {
        this.nevarisMandant = nevarisMandant;
        this.nevarisService = nevarisService;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        NevarisCostCenter nevarisCostCenter = exchange.getIn().getBody(NevarisCostCenter.class);
        NevarisAdressuebersicht nevarisAdressuebersicht = null;
        if (nevarisCostCenter.adressnrDerKostenstelle() != null && !nevarisCostCenter.adressnrDerKostenstelle().isEmpty()) {
            nevarisAdressuebersicht = nevarisService.getAdressuebersichtByAdressnr(nevarisMandant, nevarisCostCenter.adressnrDerKostenstelle());
        }
        KostenstellenData kostenstellenData = new KostenstellenData(nevarisCostCenter, nevarisAdressuebersicht);
        exchange.getIn().setBody(kostenstellenData);
    }
}
