package de.systeex.cip.nevaris.projektbeteiligte.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public record Projektbeteiligte_KSt(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonIgnore
        String odataEtag,
        @JsonProperty("Kostenstelle")
        String kostenstelle,

        @JsonProperty("Adressnr")
        String adressnr,

        @JsonProperty("Adressat")
        String adressat,

        @JsonProperty("Funktion")
        String funktion,

        @JsonProperty("Von")
        String von,

        @JsonProperty("Bis")
        String bis,

        @JsonProperty("Beteiligung_in_Percent")
        BigDecimal beteiligungInPercent,

        @JsonProperty("Verantwortlich")
        Boolean verantwortlich,

        @JsonProperty("Tägliche_Postmappe")
        Boolean taeglichePostmappe,

        @JsonProperty("Intern")
        @JsonIgnore
        Boolean intern,

        @JsonProperty("Vorname")
        @JsonIgnore
        String vorname,

        @JsonProperty("Name")
        @JsonIgnore
        String name,

        @JsonProperty("Email")
        @JsonIgnore
        String email,

        @JsonProperty("Telefon")
        @JsonIgnore
        String telefon,

        @JsonProperty("Fax")
        @JsonIgnore
        String fax,

        @JsonProperty("Mobil")
        @JsonIgnore
        String mobil,

        @JsonProperty("Interner_Rechnungsempfänger")
        Boolean internerRechnungsempfaenger,

        @JsonProperty("Bestellungsempfänger")
        Boolean bestellungsempfaenger,

        @JsonProperty("Extern")
        Boolean extern,

        @JsonProperty("Fehlertext")
        @JsonIgnore
        String fehlertext,

        @JsonProperty("Neuanlagesystem")
        @JsonIgnore
        String neuanlagesystem,

        @JsonProperty("Neuanlagebenutzer")
        @JsonIgnore
        String neuanlagebenutzer,

        @JsonProperty("Neuanlagedatum")
        @JsonIgnore
        String neuanlagedatum,

        @JsonProperty("Neuanlagezeit")
        @JsonIgnore
        String neuanlagezeit,

        @JsonProperty("Änderungssystem")
        @JsonIgnore
        String aenderungssystem,

        @JsonProperty("Änderungsbenutzer")
        @JsonIgnore
        String aenderungsbenutzer,

        @JsonProperty("Änderungsdatum")
        @JsonIgnore
        String aenderungsdatum,

        @JsonProperty("Änderungszeit")
        @JsonIgnore
        String aenderungszeit
) {
        public static final String NULL_DATE = "0001-01-01";
}