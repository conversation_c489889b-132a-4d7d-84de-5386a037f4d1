package de.systeex.cip.nevaris.projektbeteiligte.json_producer.infrastructure;

import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.nevaris.kostenstellen.json_producer.domain.KostenstellenData;
import de.systeex.cip.nevaris.projektbeteiligte.json_producer.domain.Projektbeteiligte_KSt;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class KostenstelleToProjektbeteiligteProcessor implements Processor {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.of("Europe/Berlin");

    private static final String PROJEKTBETEILIGTE_FUNKTION_TL = "TL";
    private static final String PROJEKTBETEILIGTE_FUNKTION_PL = "PL";
    private static final String PROJEKTBETEILIGTE_FUNKTION_KFM = "KFM";

    @Override
    public void process(Exchange exchange) throws Exception {
        KostenstellenData kostenstellenData = exchange.getIn().getBody(KostenstellenData.class);
        List<Projektbeteiligte_KSt> projektbeteiligte = new ArrayList<>();
        Kostenstelle kostenstelle = kostenstellenData.kostenstelle();
        NevarisAdressatenuebersicht oberbauleiterAdressatenuebersicht = kostenstellenData.oberbauleiter();
        NevarisAdressatenuebersicht bauleiterAdressatenuebersicht = kostenstellenData.bauleiter();
        NevarisAdressatenuebersicht kaufmannAdressatenuebersicht = kostenstellenData.kauffrau();

        Projektbeteiligte_KSt oberbauleiter = new Projektbeteiligte_KSt(
                null,
                null,
                truncate(exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE", String.class), 20),
                truncate(oberbauleiterAdressatenuebersicht.adressnr(), 10),
                truncate(kostenstelle.funktionTl(), 10),
                truncate(PROJEKTBETEILIGTE_FUNKTION_TL, 10),
                this.parseInstant(null),
                oberbauleiterAdressatenuebersicht.austrittsdatum(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
        projektbeteiligte.add(oberbauleiter);

        Projektbeteiligte_KSt projektleiter = new Projektbeteiligte_KSt(
                null,
                null,
                truncate(exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE", String.class), 20),
                truncate(bauleiterAdressatenuebersicht.adressnr(), 10),
                truncate(kostenstelle.funktionPl(), 10),
                truncate(PROJEKTBETEILIGTE_FUNKTION_PL, 10),
                this.parseInstant(null),
                bauleiterAdressatenuebersicht.austrittsdatum(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
        projektbeteiligte.add(projektleiter);

        Projektbeteiligte_KSt kfm = new Projektbeteiligte_KSt(
                null,
                null,
                truncate(exchange.getIn().getHeader("NEVARIS_KOSTENSTELLE", String.class), 20),
                truncate(kaufmannAdressatenuebersicht.adressnr(), 10),
                truncate(kostenstelle.funktionKfm(), 10),
                truncate(PROJEKTBETEILIGTE_FUNKTION_KFM, 10),
                this.parseInstant(null),
                kaufmannAdressatenuebersicht.austrittsdatum(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
        projektbeteiligte.add(kfm);

        exchange.getIn().setBody(projektbeteiligte);
    }

    private String truncate(String value, int length) {
        if (value == null)
            return null;

        return value.substring(0, Math.min(value.length(), length));
    }

    private String parseInstant(Instant dateTimeValue) {
        if (dateTimeValue == null) {
            return Projektbeteiligte_KSt.NULL_DATE;
        } else {
            return LocalDate.ofInstant(dateTimeValue, DEFAULT_ZONE_ID).format(DATE_FORMATTER);
        }
    }
}
