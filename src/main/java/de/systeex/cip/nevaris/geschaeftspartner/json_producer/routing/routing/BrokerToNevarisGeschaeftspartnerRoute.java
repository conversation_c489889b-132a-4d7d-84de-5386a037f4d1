package de.systeex.cip.nevaris.geschaeftspartner.json_producer.routing.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.adressen.json_producer.infrastructure.*;
import de.systeex.cip.nevaris.domain.exceptions.TableLockException;
import de.systeex.cip.nevaris.geschaeftspartner.json_producer.routing.infrastructure.NevarisBrokerToNevarisGeschaeftspartnerErrorMessageTransformer;
import de.systeex.cip.types.AdresseDebitorNrPayload;
import de.systeex.cip.types.Geschaeftspartner;
import de.systeex.cip.types.GeschaeftspartnerArt;
import org.apache.camel.ExchangePattern;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.*;
import static org.apache.camel.component.sql.SqlConstants.SQL_GENERATED_KEYS_DATA;
import static org.apache.camel.component.sql.SqlConstants.SQL_RETRIEVE_GENERATED_KEYS;

@Component
public class BrokerToNevarisGeschaeftspartnerRoute extends ErrorResistantRouteBuilder {
    @Value("${nevaris.host}")
    private String nevarisHost;

    @Value("${nevaris.default.basepath}")
    private String defaultBasePath;

    @Value("#{${mandant.mapping}}")
    private Map<String, String> mandantMapToNevaris;

    @Value("#{${mandant.debitornr.mapping}}")
    private Map<String, String> mandantMapToDebitornr;

    @Value("${copy.mandant}")
    private String copyMandant;

    @Value("${nevaris.username}")
    private String ntlmAddressUsername;
    @Value("${nevaris.password}")
    private String ntlmAddressPassword;

    private final String ENTITY_KEY_INTERESSENTEN_KUNDEN_ADRESSE = "KUNDEN_INTERESSENTEN_ADRESSE";
    private final String ENTITY_KEY_DEBITOR = "DEBITOR";
    private final Long START_NUMBER_VALUE_INTERESSENTEN_KUNDEN_ADRESSE = 90000000L;
    private final Predicate adresseIstNeu = PredicateBuilder.isNull(header("EXTERNE_ADRESS_NR"));
    private final Predicate insertNummer = PredicateBuilder.isNull(header("CURRENT_VALUE"));

    private final String originQueueName = "nevaris-%s.interessenten";

    @Override
    public void configure() throws Exception {
        super.configure();
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat jacksonDataFormatGeschaeftspartner = new JacksonDataFormat(mapper, Geschaeftspartner.class);
        JacksonDataFormat jacksonDataFormatAdresseDebitorNrPayload = new JacksonDataFormat(mapper, AdresseDebitorNrPayload.class);

        UpdateNevarisAdresseProcessor updateNevarisAdresseProcessor = new UpdateNevarisAdresseProcessor(ntlmAddressUsername, ntlmAddressPassword, "", "", nevarisHost, defaultBasePath);
        UpdateNevarisDebitorProcessor updateNevarisDebitorKopiermandantProcessor = new UpdateNevarisDebitorProcessor(ntlmAddressUsername, ntlmAddressPassword, "", "", nevarisHost, defaultBasePath, copyMandant);
        UpdateNevarisDebitorProcessor updateNevarisDebitorMandantProcessor = new UpdateNevarisDebitorProcessor(ntlmAddressUsername, ntlmAddressPassword, "", "", nevarisHost, defaultBasePath, "");

        onException(TableLockException.class)
                .log("TableLockException occurred, retrying in 5 seconds...")
                .maximumRedeliveries(3).redeliveryDelay(5000);

        mandantMapToNevaris.keySet().forEach(mandant -> {
            String queueName = String.format(originQueueName, mandantMapToNevaris.get(mandant));
            from("rabbitmq:interessenten-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=" + queueName + "&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapToNevaris.get(mandant) + ".interessenten.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".interessenten&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchInteressentenFromBroker" + mandantMapToNevaris.get(mandant))
                    .unmarshal(jacksonDataFormatGeschaeftspartner)
                    .process(exchange -> {
                        Geschaeftspartner geschaeftspartner = exchange.getIn().getBody(Geschaeftspartner.class);
                        exchange.getIn().setHeader("MANDANT", geschaeftspartner.mandant());
                        exchange.getIn().setHeader("NEVARIS_MANDANT", mandantMapToNevaris.get(geschaeftspartner.mandant()));
                        exchange.getIn().setHeader("NEVARIS_ADRESS_NR", geschaeftspartner.adresse().destinationSystemKey());
                        exchange.getIn().setHeader("SRC_DEBITOR_ID", geschaeftspartner.sourceSystemKey());
                        exchange.getIn().setHeader("SRC_ADRESS_ID", geschaeftspartner.adresse().sourceSystemKey());
                        exchange.getIn().setHeader("SOURCE_SYSTEM_NAME", geschaeftspartner.sourceSystemName());
                    })
                    .to("direct:processInteressent");
        });

        mandantMapToNevaris.keySet().forEach(mandant -> {
            from("rabbitmq:debitoren-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=nevaris-" + mandantMapToNevaris.get(mandant) + ".debitoren&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapToNevaris.get(mandant) + ".debitoren.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".debitoren&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchDebitorenFromBroker" + mandantMapToNevaris.get(mandant))
                    .unmarshal(jacksonDataFormatGeschaeftspartner)
                    .process(exchange -> {
                        Geschaeftspartner geschaeftspartner = exchange.getIn().getBody(Geschaeftspartner.class);
                        exchange.getIn().setHeader("MANDANT", geschaeftspartner.mandant());
                        exchange.getIn().setHeader("NEVARIS_MANDANT", mandantMapToNevaris.get(geschaeftspartner.mandant()));
                        exchange.getIn().setHeader("NEVARIS_ADRESS_NR", geschaeftspartner.adresse().destinationSystemKey());
                        exchange.getIn().setHeader("SRC_DEBITOR_ID", geschaeftspartner.sourceSystemKey());
                        exchange.getIn().setHeader("SRC_ADRESS_ID", geschaeftspartner.adresse().sourceSystemKey());
                        exchange.getIn().setHeader("SOURCE_SYSTEM_NAME", geschaeftspartner.sourceSystemName());
                    })
                    .toD("sql:SELECT adress_key FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND source_system_adress_id = '${body.adresse.sourceSystemKey}'?outputType=SelectOne&outputHeader=NEVARIS_ADRESS_NR")
                    .choice()
                    .when(PredicateBuilder.or(PredicateBuilder.isNull(header("NEVARIS_ADRESS_NR")), PredicateBuilder.isEqualTo(header("NEVARIS_ADRESS_NR"), constant(""))))
                    .enrich("direct:generateGeschaeftspartnerAdressnummer", (oldExchange, newExchange) -> {
                        oldExchange.getIn().setHeader("NEVARIS_ADRESS_NR", newExchange.getIn().getBody(Long.class));
                        return oldExchange;
                    })
                    .endChoice()
                    .end()
                    .toD("sql:SELECT debitor_key FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND source_system_adress_id = '${body.adresse.sourceSystemKey}'?outputType=SelectOne&outputHeader=NEVARIS_DEBITOR_NR")
                    .choice()
                    .when(PredicateBuilder.or(PredicateBuilder.isNull(header("NEVARIS_DEBITOR_NR")), PredicateBuilder.isEqualTo(header("NEVARIS_DEBITOR_NR"), constant(""))))
                    .enrich("direct:generateDebitorNummer", (oldExchange, newExchange) -> {
                        oldExchange.getIn().setHeader("NEVARIS_DEBITOR_NR", newExchange.getIn().getBody(Long.class));
                        return oldExchange;
                    })
                    .endChoice()
                    .end()
                    .log("Debitor fromRabbit: ${body}")
                    .process(updateNevarisAdresseProcessor)
                    .process(updateNevarisDebitorKopiermandantProcessor)
                    .enrich("direct:updateAdressTabelle", (oldExchange, newExchange) -> oldExchange)
                    .enrich("direct:adresseDebitorToBroker", (oldExchange, newExchange) -> oldExchange)
                    .removeHeaders("*","MANDANT|NEVARIS_MANDANT|NEVARIS_ADRESS_NR|NEVARIS_DEBITOR_NR|SRC_DEBITOR_ID|SRC_ADRESS_ID|SOURCE_SYSTEM_NAME|MESSAGE_ID|DELIVERY_MODE")
                    .setHeader(DELIVERY_MODE, constant(2))
                    .marshal(jacksonDataFormatGeschaeftspartner)
                    .log("Debitor toNevaris: ${body}")
                    .toD("rabbitmq:debitoren-single?exchangeType=topic&routingKey=" + mandant + "&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");

            from("rabbitmq:debitoren-single?exchangeType=topic&routingKey=" + mandant + "&queue=nevaris-" + mandantMapToNevaris.get(mandant) + ".debitoren-single&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterQueue=nevaris-" + mandantMapToNevaris.get(mandant) + ".debitoren-single.dlq&deadLetterExchangeType=topic&deadLetterExchange=nevaris.dlx&deadLetterRoutingKey=nevaris-" + mandantMapToNevaris.get(mandant) + ".debitoren-single&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("debitorenToNevaris" + mandantMapToNevaris.get(mandant))
                    .unmarshal(jacksonDataFormatGeschaeftspartner)
                    .process(updateNevarisDebitorMandantProcessor)
                    .enrich("direct:updateAdressTabelle", (oldExchange, newExchange) -> oldExchange)
                    .to("direct:adresseDebitorToBroker");
        });

        from("direct:processInteressent")
            .toD("sql:SELECT adress_key FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND source_system_adress_id = '${body.adresse.sourceSystemKey}'?outputType=SelectOne&outputHeader=NEVARIS_ADRESS_NR")
            .toD("sql:SELECT debitor_key FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND source_system_adress_id = '${body.adresse.sourceSystemKey}'?outputType=SelectOne&outputHeader=NEVARIS_DEBITOR_NR")
            .choice()
            .when(PredicateBuilder.or(PredicateBuilder.isNull(header("NEVARIS_ADRESS_NR")), PredicateBuilder.isEqualTo(header("NEVARIS_ADRESS_NR"), constant(""))))
                .enrich("direct:generateGeschaeftspartnerAdressnummer", (oldExchange, newExchange) -> {
                    oldExchange.getIn().setHeader("NEVARIS_ADRESS_NR", newExchange.getIn().getBody(Long.class));
                    return oldExchange;
                })
                .endChoice()
            .end()
            .process(updateNevarisAdresseProcessor)
            .enrich("direct:updateAdressTabelle", (oldExchange, newExchange) -> oldExchange)
            .to("direct:adresseDebitorToBroker");

        from("direct:adresseDebitorToBroker")
                .toD("sql:SELECT source_system_adress_id, source_system_debitor_id, debitor_key FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND adress_key = '${headers.NEVARIS_ADRESS_NR}'?outputType=SelectOne")
                .process(exchange -> {
                    Map<String, Object> adressen = exchange.getIn().getBody(Map.class);
                    String adressKey = exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class);
                    String sourceSystemAdressId = Objects.toString(adressen.get("source_system_adress_id"), null);
                    String sourceSystemDebitorId = Objects.toString(adressen.get("source_system_debitor_id"), null);
                    String mandant = exchange.getIn().getHeader("MANDANT", String.class);
                    String debitorKey = Objects.toString(adressen.get("debitor_key"), null);
                    AdresseDebitorNrPayload adressNrPayload = new AdresseDebitorNrPayload(mandant, adressKey, debitorKey, sourceSystemAdressId, sourceSystemDebitorId);
                    exchange.getIn().setBody(adressNrPayload);
                })
                .marshal(jacksonDataFormatAdresseDebitorNrPayload)
                .log("AdresseDebitorNrPayload: ${body}")
                .setExchangePattern(ExchangePattern.InOnly)
                .removeHeaders("*", "MANDANT|MESSAGE_ID|DELIVERY_MODE")
                .setHeader(DELIVERY_MODE, constant(2))
                .toD("rabbitmq:adressnr?exchangeType=topic&routingKey=${headers.MANDANT}&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");


        from("direct:generateGeschaeftspartnerAdressnummer")
                .toD("sql:SELECT current_value FROM number_generator.number_generator WHERE entity = '" + ENTITY_KEY_INTERESSENTEN_KUNDEN_ADRESSE + "' AND mandant = ''?outputType=SelectOne&outputHeader=CURRENT_VALUE")
                .process(exchange -> {
                    Long currentValue = exchange.getIn().getHeader("CURRENT_VALUE", Long.class);
                    if (currentValue == null) {
                        currentValue = START_NUMBER_VALUE_INTERESSENTEN_KUNDEN_ADRESSE;
                    }
                    exchange.getIn().setBody(currentValue + 1);
                    exchange.getIn().setHeader("ENTITY_KEY", ENTITY_KEY_INTERESSENTEN_KUNDEN_ADRESSE);
                    exchange.getIn().setHeader("MANDANT_KEY", null);
                })
                .to("direct:saveNummerngeneratorNummer");

        from("direct:generateDebitorNummer")
                .toD("sql:SELECT current_value FROM number_generator.number_generator WHERE entity = '" + ENTITY_KEY_DEBITOR + "' AND mandant = '${headers.MANDANT}'?outputType=SelectOne&outputHeader=CURRENT_VALUE")
                .process(exchange -> {
                    Long currentValue = exchange.getIn().getHeader("CURRENT_VALUE", Long.class);
                    if (currentValue == null) {
                        currentValue = Long.parseLong(mandantMapToDebitornr.get(exchange.getIn().getHeader("MANDANT", String.class)));
                    }
                    exchange.getIn().setBody(currentValue + 1);
                    exchange.getIn().setHeader("ENTITY_KEY", ENTITY_KEY_DEBITOR);
                    exchange.getIn().setHeader("MANDANT_KEY", exchange.getIn().getHeader("MANDANT", String.class));
                })
                .to("direct:saveNummerngeneratorNummer");

        from("direct:saveNummerngeneratorNummer")
                .choice()
                .when(insertNummer)
                    .toD("sql:INSERT INTO number_generator.number_generator (entity, updated, current_value, mandant) VALUES ('${headers.ENTITY_KEY}', NOW(), ${body}, '${headers.MANDANT_KEY}')")
                .otherwise()
                    .toD("sql:UPDATE number_generator.number_generator SET current_value = ${body}, updated = NOW() WHERE entity = '${headers.ENTITY_KEY}' AND mandant = '${headers.MANDANT_KEY}'")
                .end();

        from("direct:updateAdressTabelle")
                .setHeader(SQL_RETRIEVE_GENERATED_KEYS, constant(true))
                .toD("sql:SELECT id FROM mapping.adressen WHERE mandant = '${headers.MANDANT}' AND source_system_adress_id = '${headers.SRC_ADRESS_ID}'?outputType=SelectOne&outputHeader=EXTERNE_ADRESS_NR")
                .process(exchange -> {
                    Geschaeftspartner geschaeftspartner = exchange.getIn().getBody(Geschaeftspartner.class);
                    boolean debitorExistiert = exchange.getIn().getHeader("NEVARIS_DEBITOR_NR", String.class) != null && !exchange.getIn().getHeader("NEVARIS_DEBITOR_NR", String.class).isEmpty();
                    Map<String, Object> map = new HashMap<>();
                    map.put("source_system_name", geschaeftspartner.sourceSystemName());
                    map.put("mandant", geschaeftspartner.mandant());
                    map.put("source_system_adress_id", exchange.getIn().getHeader("SRC_ADRESS_ID", String.class));
                    map.put("adress_key", exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class));
                    map.put("source_system_debitor_id", exchange.getIn().getHeader("SRC_DEBITOR_ID", String.class));
                    map.put("adress_art", debitorExistiert ? GeschaeftspartnerArt.KUNDE.getValue() : geschaeftspartner.geschaeftspartnerArt().getValue());
                    map.put("company_name", geschaeftspartner.adresse().name());
                    map.put("debitor_key", exchange.getIn().getHeader("NEVARIS_DEBITOR_NR", String.class));

                    exchange.getIn().setBody(map);
                })
                .choice()
                    .when(adresseIstNeu)
                        .toD("sql:INSERT INTO mapping.adressen (source_system_name, mandant, source_system_adress_id, created_at, adress_key, source_system_debitor_id, adress_art, company_name, debitor_key) VALUES" +
                                "(:#source_system_name, :#mandant, :#source_system_adress_id, NOW(), :#adress_key, :#source_system_debitor_id, :#adress_art, :#company_name, :#debitor_key)")
                        .process(exchange -> {
                            Map<String, Object> map = (Map<String, Object>) exchange.getIn().getHeader(SQL_GENERATED_KEYS_DATA, List.class).get(0);
                            exchange.getIn().setHeader("EXTERNE_ADRESS_NR", map.get("id"));
                        })
                    .otherwise()
                        .toD("sql:UPDATE mapping.adressen SET adress_art = :#adress_art, company_name = :#company_name, updated_at = NOW(), debitor_key = :#debitor_key " +
                                " WHERE source_system_name = :#source_system_name AND mandant = :#mandant AND source_system_adress_id = :#source_system_adress_id AND source_system_debitor_id = :#source_system_debitor_id")
                .end();

    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisBrokerToNevarisGeschaeftspartnerErrorMessageTransformer("Geschaeftspartner", "NEVARIS", originQueueName, mandantMapToNevaris);
    }
}
