package de.systeex.cip.nevaris.geschaeftspartner.json_producer.routing.infrastructure;

import de.systeex.cip.types.ErrSrcType;
import systeex.cip.error_handler.ErrorMessageTransformer;
import java.util.Map;

public class NevarisBrokerToNevarisGeschaeftspartnerErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisBrokerToNevarisGeschaeftspartnerErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.QUEUE;
    }
}
