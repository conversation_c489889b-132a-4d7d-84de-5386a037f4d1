package de.systeex.cip.nevaris.adressen.json_producer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.validation.constraints.Null;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisAdressuebersicht(
//        <EntityType Name="adressuebersicht">
//        <Key>
//        <PropertyRef Name="Adressnr"/>
//        </Key>
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,

        @JsonProperty("@odata.etag")
        @JsonIgnore
        String odataEtag,
        @JsonProperty("Adressnr")
        @Nonnull
        String adressnr, // Type="Edm.String" Nullable="false" MaxLength="10">
        @JsonProperty("Name_lang")
        @Nullable
        String nameLang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse_lang")
        @Nullable
        String adresseLang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse_2_lang")
        @Nullable
        String adresse2Lang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse_3_lang")
        @Nullable
        String adresse3Lang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Straße_lang")
        @Nullable
        String strasseLang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Name")
        @JsonIgnore
        @Nullable
        String name, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Adresse")
        @JsonIgnore
        @Nullable
        String adresse, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Adresse2")
        @JsonIgnore
        @Nullable
        String adresse2, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Adresse3")
        @JsonIgnore
        @Nullable
        String adresse3, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Straße")
        @JsonIgnore
        @Nullable
        String strasse, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Ländercode")
        @Nullable
        String laendercode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("PLZ")
        @Nullable
        String plz, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Ort")
        @Nullable
        String ort, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Suchbegriff")
        @Nullable
        String suchbegriff, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Standard_Adressat")
        @Nullable
        String standardAdressat, // Type="Edm.String" MaxLength="10">
        @JsonProperty("PLZ_Postfach")
        @Nullable
        String plzPostfach, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Ort_Postfach")
        @Nullable
        String ortPostfach, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Postfach_nutzen")
        @Nullable
        Boolean postfachNutzen, // Type="Edm.Boolean">
        @JsonProperty("Postfach")
        @Nullable
        String postfach, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Sprache")
        @Nullable
        String sprache, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Bundesland")
        @Nullable
        String bundesland, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Landkreis")
        @Nullable
        String landkreis, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Auslandsvorwahl")
        @Nullable
        String auslandsvorwahl, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Ortskennzahl")
        @Nullable
        String ortskennzahl, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Hauptanschlußnr")
        @Nullable
        String hauptanschlussnr, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Durchwahl_Zentrale")
        @Nullable
        String durchwahlZentrale, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Durchwahl_Fax")
        @Nullable
        String durchwahlFax, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Durchwahl_Modem")
        @Nullable
        String durchwahlModem, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Telefonnr_kompl")
        @Nullable
        String telefonnrKompl, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Faxnr_kompl")
        @Nullable
        String faxnrKompl, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Modemnr_kompl")
        @Nullable
        String modemnrKompl, // Type="Edm.String" MaxLength="100">
        @JsonProperty("E_Mail")
        @Nullable
        String eMail, // Type="Edm.String" MaxLength="50">
        @JsonProperty("WWW_Adresse_URL")
        @Nullable
        String wwwAdresseURL, // Type="Edm.String" MaxLength="250">
        @JsonProperty("Adressnr_der_Zentrale")
        @Nullable
        String adressnrDerZentrale, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressnr_der_Organschaft")
        @Nullable
        String adressnrDerOrganschaft, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Datenquelle")
        @Nullable
        String datenquelle, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Gruppe")
        @Nullable
        String gruppe, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Externe_Adressnr")
        @Nullable
        String externeAdressnr, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Duplikat")
        @Nullable
        Boolean duplikat, // Type="Edm.Boolean">
        @JsonProperty("Unsere_Kundennr_dort")
        @Nullable
        String unsereKundennrDort, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Adresszeile2_kompl_lang")
        @Nullable
        String adresszeile2KomplLang, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Adresszeile3_kompl_lang")
        @Nullable
        String adresszeile3KomplLang, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Adresszeile4_kompl_lang")
        @Nullable
        String adresszeile4KomplLang, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Adresszeile2_kompl")
        @Nullable
        String adresszeile2Kompl, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Adresszeile3_kompl")
        @Nullable
        String adresszeile3Kompl, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Adresszeile4_kompl")
        @Nullable
        String adresszeile4Kompl, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Adressnr_Primärer_Betreuer")
        @Nullable
        String adressnrPrimaererBetreuer, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressat_Primärer_Betreuer")
        @Nullable
        String adressatPrimaererBetreuer, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Debitor_vorhanden")
        @JsonIgnore
        @Nullable
        Boolean debitorVorhanden, // Type="Edm.Boolean">
        @JsonProperty("Kreditor_vorhanden")
        @JsonIgnore
        @Nullable
        Boolean kreditorVorhanden, // Type="Edm.Boolean">
        @JsonProperty("Lieferant_vorhanden")
        @Nullable
        Boolean lieferantVorhanden, // Type="Edm.Boolean">
        @JsonProperty("Ähnlichkeit")
        @JsonIgnore
        @Nullable
        Integer aehnlichkeit, // Type="Edm.Int32">
        @JsonProperty("Gesperrt")
        @Nullable
        String gesperrt, // Type="Edm.String">
        @JsonProperty("Sperrhinweis")
        @Nullable
        String sperrhinweis, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Gültig_ab")
        @Nullable
        String gueltigAb, // Type="Edm.Date">
        @JsonProperty("Gültig_bis")
        @Nullable
        String gueltigBis, // Type="Edm.Date">
        @JsonProperty("Handelsregister")
        @Nullable
        String handelsregister, // Type="Edm.String" MaxLength="80">
        @JsonProperty("Neuanlagesystem")
        @Nullable
        @JsonIgnore
        String neuanlagesystem, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Neuanlagebenutzer")
        @Nullable
                @JsonIgnore
        String neuanlagebenutzer, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Neuanlagedatum")
        @Nullable
        @JsonIgnore
        String neuanlagedatum, // Type="Edm.Date">
        @JsonProperty("Neuanlagezeit")
        @Nullable
        @JsonIgnore
        String neuanlagezeit, // Type="Edm.String">
        @JsonProperty("Änderungssystem")
        @Nullable
        @JsonIgnore
        String aenderungssystem, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Änderungsbenutzer")
        @Nullable
        @JsonIgnore
        String aenderungsbenutzer, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Änderungsdatum")
        @Nullable
        @JsonIgnore
        String aenderungsdatum, // Type="Edm.Date">
        @JsonProperty("Änderungszeit")
        @Nullable
        @JsonIgnore
        String aenderungszeit, // Type="Edm.String">
        @JsonProperty("Verweis_auf_Adressnr")
        @Nullable
        String verweisAufAdressnr, // Type="Edm.String" MaxLength="10">
        @JsonProperty("QM_Sachbearbeiter")
        @Nullable
        String qmSachbearbeiter, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Gültigkeit_des_Zertifikats")
        @Nullable
        String gueltigkeitDesZertifikats, // Type="Edm.String">
        @JsonProperty("Neuzertifizierung")
        @Nullable
        String neuzertifizierung, // Type="Edm.Date">
        @JsonProperty("Zertifikat")
        @Nullable
        String zertifikat, // Type="Edm.String">
        @JsonProperty("Zertifizierung")
        @Nullable
        String zertifizierung, // Type="Edm.Date">
        @JsonProperty("Interne_Prüfung")
        @Nullable
        String internePruefung, // Type="Edm.Date">
        @JsonProperty("Ursprungsnr")
        @Nullable
        String ursprungsnr, // Type="Edm.String" MaxLength="20">
        @JsonProperty("USt_IdNr")
        @Nullable
        String ustIdNr, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Steuernummer_Gesellschaft")
        @Nullable
        String steuernummerGesellschaft, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Vorname")
        @Nullable
        String vorname, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Nachname")
        @Nullable
        String nachname, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Namenskürzel")
        @Nullable
        String namenskuerzel, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Anrede")
        @Nullable
        String anrede, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Titel")
        @Nullable
        String titel, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Titel_im_Anschreiben")
        @Nullable
        String titelImAnschreiben, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Geburtsdatum")
        @Nullable
        String geburtsdatum, // Type="Edm.Date">
        @JsonProperty("Vollstaendigkeit_Prozent")
        @Nullable
        Integer vollstaendigkeitProzent, // Type="Edm.Int32">
        @JsonProperty("LgU_Firmencode")
        @Nullable
        String lguFirmencode, // Type="Edm.String" MaxLength="36">
        @JsonProperty("Mit_Inform_nicht_synchr")
        @Nullable
        Boolean mitInformNichtSynchr // Type="Edm.Boolean">
//        <NavigationProperty Name="Ländercode_Link" Type="Collection(NAV.Laender)" ContainsTarget="true">
//                <ReferentialConstraint Property="Ländercode" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="PLZ_Link" Type="Collection(NAV.PLZCodes)" ContainsTarget="true">
//                <ReferentialConstraint Property="PLZ" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Standard_Adressat_Link" Type="Collection(NAV.adressatenuebersicht)" Partner="adressuebersicht" ContainsTarget="true">
//                <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr"/>
//                </NavigationProperty>
//                <NavigationProperty Name="PLZ_Postfach_Link" Type="Collection(NAV.PLZCodes)" ContainsTarget="true">
//                <ReferentialConstraint Property="PLZ_Postfach" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Sprache_Link" Type="Collection(NAV.Sprachen)" ContainsTarget="true">
//                <ReferentialConstraint Property="Sprache" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Bundesland_Link" Type="Collection(NAV.Bundeslaender)" ContainsTarget="true">
//                <ReferentialConstraint Property="Bundesland" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Landkreis_Link" Type="Collection(NAV.Landkreise)" ContainsTarget="true">
//                <ReferentialConstraint Property="Landkreis" ReferencedProperty="Landkreis"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Adressat_Primärer_Betreuer_Link" Type="Collection(NAV.adressatenuebersicht)" ContainsTarget="true">
//                <ReferentialConstraint Property="Adressat_Primärer_Betreuer" ReferencedProperty="Adressat"/>
//                <ReferentialConstraint Property="Adressnr_Primärer_Betreuer" ReferencedProperty="Adressnr"/>
//                </NavigationProperty>
//                <NavigationProperty Name="QM_Sachbearbeiter_Link" Type="Collection(NAV.adressatenuebersicht)" ContainsTarget="true">
//                <ReferentialConstraint Property="QM_Sachbearbeiter" ReferencedProperty="Adressat"/>
//                <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr"/>
//                </NavigationProperty>
) {
        public static final String NULL_DATE = "0001-01-01";
}
