package de.systeex.cip.nevaris.adressen.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisDebitorenkarte(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonIgnore
        String odataEtag,
        @JsonProperty("No")
        @Nonnull
        String no, //<Property Name="No" Type="Edm.String" Nullable="false" MaxLength="20">
        @JsonProperty("Adressnr_des_Debitors")
        String adressnrDesDebitors, //<Property Name="Adressnr_des_Debitors" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adresse")
        @JsonIgnore
        String adresse, //<Property Name="Adresse" Type="Edm.String">
        @JsonProperty("Ländercode")
        @JsonIgnore
        String laendercode, //<Property Name="Ländercode" Type="Edm.String">
        @JsonProperty("Search_Name")
        String searchName, // <Property Name="Search_Name" Type="Edm.String" MaxLength="100">
        @JsonProperty("Contact")
        @JsonIgnore
        String contact, //<Property Name="Contact" Type="Edm.String" MaxLength="100">
        @JsonProperty("Kundengruppe")
        String kundengruppe, //<Property Name="Kundengruppe" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gutschriftsverfahren")
        Boolean gutschriftsverfahren, //<Property Name="Gutschriftsverfahren" Type="Edm.Boolean">
        @JsonProperty("Steuernummer")
        String steuernummer, //<Property Name="Steuernummer" Type="Edm.String" MaxLength="20">
        @JsonProperty("ESR_System")
        String esrSystem, //<Property Name="ESR_System" Type="Edm.String">
        @JsonProperty("Ist_Niederlassung")
        @JsonIgnore
        Boolean istNiederlasung, //<Property Name="Ist_Niederlassung" Type="Edm.Boolean">
        @JsonProperty("Trab_Debitor")
        Boolean trabDebitor, //<Property Name="Trab_Debitor" Type="Edm.Boolean">
        @JsonProperty("Currency_Code")
        String currencyCode, //<Property Name="Currency_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Language_Code")
        String languageCode, //<Property Name="Language_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("VAT_Registration_No")
        String vatRegistrationNo, //<Property Name="VAT_Registration_No" Type="Edm.String" MaxLength="20">
        @JsonProperty("ToolsActions_ReturnString_TXTUIDAbfrage")
        @JsonIgnore
        String toolsActionsReturnStringTxtuidAbfrage, //<Property Name="ToolsActions_ReturnString_TXTUIDAbfrage" Type="Edm.String">
        @JsonProperty("USt_IdNr_Betriebsstätte")
        String ustIdNrBetriebsstaette, //<Property Name="USt_IdNr_Betriebsstätte" Type="Edm.String" MaxLength="20">
        @JsonProperty("Steuernummer_IT")
        String steuernummerIt, //<Property Name="Steuernummer_IT" Type="Edm.String" MaxLength="20">
        @JsonProperty("Gen_Bus_Posting_Group")
        String genBusPostingGroup, //<Property Name="Gen_Bus_Posting_Group" Type="Edm.String" MaxLength="20">
        @JsonProperty("VAT_Bus_Posting_Group")
        String vatBusPostingGroup, //<Property Name="VAT_Bus_Posting_Group" Type="Edm.String" MaxLength="20">
        @JsonProperty("Customer_Posting_Group")
        String customerPostingGroup, //<Property Name="Customer_Posting_Group" Type="Edm.String" MaxLength="20">
        @JsonProperty("Öffentlicher_Auftraggeber")
        Boolean oeffentlicherAuftraggeber, //<Property Name="Öffentlicher_Auftraggeber" Type="Edm.Boolean">
        @JsonProperty("DR_Prozent")
        BigDecimal drProzent, //<Property Name="DR_Prozent" Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("DR_Fälligkeitsformel")
        String drFaelligkeitsformel, //<Property Name="DR_Fälligkeitsformel" Type="Edm.String">
        @JsonProperty("Zielmandant_Belegübertragung")
        String zielmandantBeleguebertragung, //<Property Name="Zielmandant_Belegübertragung" Type="Edm.String" MaxLength="30">
        @JsonProperty("Zielkreditor_Belegübertragung")
        String zielkreditorBeleguebertragung, //<Property Name="Zielkreditor_Belegübertragung" Type="Edm.String" MaxLength="20">
        @JsonProperty("Global_Dimension_1_Code")
        String globalDimension1Code, //<Property Name="Global_Dimension_1_Code" Type="Edm.String" MaxLength="20">
        @JsonProperty("Global_Dimension_2_Code")
        String globalDimension2Code, //<Property Name="Global_Dimension_2_Code" Type="Edm.String" MaxLength="20">
        @JsonProperty("Bankkonto")
        String bankkonto, //<Property Name="Bankkonto" Type="Edm.String" MaxLength="20">
        @JsonProperty("Application_Method")
        String applicationMethod, //<Property Name="Application_Method" Type="Edm.String">
        @JsonProperty("Payment_Terms_Code")
        String paymentTermsCode, //<Property Name="Payment_Terms_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Payment_Method_Code")
        String paymentMethodCode, //<Property Name="Payment_Method_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Aktuelles_Bankkonto")
        String aktuellesBankkonto, //<Property Name="Aktuelles_Bankkonto" Type="Edm.String" MaxLength="20">
        @JsonProperty("Bankname")
        @JsonIgnore
        String bankname, //<Property Name="Bankname" Type="Edm.String" MaxLength="100">
        @JsonProperty("BLZ")
        @JsonIgnore
        String blz, //<Property Name="BLZ" Type="Edm.String" MaxLength="10">
        @JsonProperty("Bankkontonr")
        @JsonIgnore
        String bankkontonr, //<Property Name="Bankkontonr" Type="Edm.String" MaxLength="30">
        @JsonProperty("SWIFT_Code")
        @JsonIgnore
        String swiftCode, //<Property Name="SWIFT_Code" Type="Edm.String" MaxLength="20">
        @JsonProperty("IBAN")
        @JsonIgnore
        String iban, //<Property Name="IBAN" Type="Edm.String" MaxLength="50">
        @JsonProperty("Reminder_Terms_Code")
        String reminderTermsCode, //<Property Name="Reminder_Terms_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Fin_Charge_Terms_Code")
        String finChargeTermsCode, //<Property Name="Fin_Charge_Terms_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressnr_Avise")
        String adressnrAvise, //<Property Name="Adressnr_Avise" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressat_Avise")
        String adressatAvise, //<Property Name="Adressat_Avise" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressnr_Mahnungen")
        String adressnrMahnungen, //<Property Name="Adressnr_Mahnungen" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressat_Mahnung")
        String adressatMahnung, //<Property Name="Adressat_Mahnung" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressnr_Mahnungen_Kopie")
        String adressnrMahnungenKopie, //<Property Name="Adressnr_Mahnungen_Kopie" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressat_Mahnung_Kopie")
        String adressatMahnungKopie, //<Property Name="Adressat_Mahnung_Kopie" Type="Edm.String" MaxLength="10">
        @JsonProperty("Anzahl_Posten_pro_Zahlsatz")
        Integer anzahlPostenProZahlsatz, //<Property Name="Anzahl_Posten_pro_Zahlsatz" Type="Edm.Int32">
        @JsonProperty("max_Anzahl_Zahlsätze")
        String maxAnzahlZahlsaetze, //<Property Name="max_Anzahl_Zahlsätze" Type="Edm.String" MaxLength="3">
        @JsonProperty("Verwendungszweck_SEPA")
        @JsonIgnore
        String verwendungszweckSEPA, //<Property Name="Verwendungszweck_SEPA" Type="Edm.String" MaxLength="140">
        @JsonProperty("Mandatreferenz")
        @JsonIgnore
        String mandatreferenz, //<Property Name="Mandatreferenz" Type="Edm.String" MaxLength="35">
        @JsonProperty("Versicherbarkeit")
        String versicherbarkeit, //<Property Name="Versicherbarkeit" Type="Edm.String">
        @JsonProperty("Versicherung")
        String versicherung, //<Property Name="Versicherung" Type="Edm.String" MaxLength="20">
        @JsonProperty("Risikonr")
        String risikonr, //<Property Name="Risikonr" Type="Edm.String" MaxLength="20">
        @JsonProperty("Salesperson_Code")
        String salespersonCode, //<Property Name="Salesperson_Code" Type="Edm.String" MaxLength="20">
        @JsonProperty("Location_Code")
        String locationCode, //<Property Name="Location_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Shipment_Method_Code")
        String shipmentMethodCode, //<Property Name="Shipment_Method_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Transportzeit")
        String transportzeit, //<Property Name="Transportzeit" Type="Edm.String">
        @JsonProperty("Shipping_Agent_Code")
        String shippingAgentCode, //<Property Name="Shipping_Agent_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Combine_Shipments")
        Boolean combineShipments, //<Property Name="Combine_Shipments" Type="Edm.Boolean">
        @JsonProperty("Bill_to_Customer_No")
        String billToCustomerNo, //<Property Name="Bill_to_Customer_No" Type="Edm.String" MaxLength="20">
        @JsonProperty("Invoice_Copies")
        Integer invoiceCopies, //<Property Name="Invoice_Copies" Type="Edm.Int32">
        @JsonProperty("Anzahl_Lieferscheinkopien")
        Integer anzahlLieferscheinkopien, //<Property Name="Anzahl_Lieferscheinkopien" Type="Edm.Int32">
        @JsonProperty("Reserve")
        String reserve, //<Property Name="Reserve" Type="Edm.String">
        @JsonProperty("Credit_Limit_LCY")
        BigDecimal creditLimitLCY, //<Property Name="Credit_Limit_LCY" Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("Customer_Price_Group")
        String customerPriceGroup, //<Property Name="Customer_Price_Group" Type="Edm.String" MaxLength="10">
        @JsonProperty("Invoice_Disc_Code")
        String invocieDiscCode, //<Property Name="Invoice_Disc_Code" Type="Edm.String" MaxLength="20">
        @JsonProperty("Customer_Disc_Group")
        String customerDiscGroup, //<Property Name="Customer_Disc_Group" Type="Edm.String" MaxLength="20">
        @JsonProperty("Mengenrabatt_zulassen_II")
        Boolean mengenrabattZulassenII, //<Property Name="Mengenrabatt_zulassen_II" Type="Edm.Boolean">
        @JsonProperty("Debitorenzuschlagsgruppe")
        String debitorenzuschlagsgruppe, //<Property Name="Debitorenzuschlagsgruppe" Type="Edm.String" MaxLength="10">
        @JsonProperty("Our_Account_No")
        String ourAccountNo, //<Property Name="Our_Account_No" Type="Edm.String" MaxLength="20">
        @JsonProperty("Zeilenrabattberechnung")
        String zeilenrabattberechnung, //<Property Name="Zeilenrabattberechnung" Type="Edm.String">
        @JsonProperty("Provisionsberechtigter")
        String provisionsberechtigter, //<Property Name="Provisionsberechtigter" Type="Edm.String" MaxLength="20">
        @JsonProperty("Rech_an_abw_Adressnr")
        String rechAnAbwAdressnr, //<Property Name="Rech_an_abw_Adressnr" Type="Edm.String" MaxLength="10">
        @JsonProperty("E_Mail")
        String eMail, //<Property Name="E_Mail" Type="Edm.String" MaxLength="80">
        @JsonProperty("E_Mail_nicht_aus_Adresse")
        Boolean eMailNichtAusAdresse, //<Property Name="E_Mail_nicht_aus_Adresse" Type="Edm.Boolean">
        @JsonProperty("Document_Sending_Profile")
        String documentSendingProfile, //<Property Name="Document_Sending_Profile" Type="Edm.String" MaxLength="20">
        @JsonProperty("ZUGFeRD_Rechnungen")
        Boolean zugFeRDRechnungen, //<Property Name="ZUGFeRD_Rechnungen" Type="Edm.Boolean">
        @JsonProperty("LeitwegID")
        String leitwegID, //<Property Name="LeitwegID" Type="Edm.String" MaxLength="46">
        @JsonProperty("Bauabzugssteuer_Adressnr")
        @JsonIgnore
        String bauabzugssteuerAdressnr, //<Property Name="Bauabzugssteuer_Adressnr" Type="Edm.String" MaxLength="10">
        @JsonProperty("Leistungsempfänger")
        Boolean leistungsempfaenger, //<Property Name="Leistungsempfänger" Type="Edm.Boolean">
        @JsonProperty("Freistellung_LE_von")
        @JsonIgnore
        String freistellungLEVon, //<Property Name="Freistellung_LE_von" Type="Edm.Date">
        @JsonProperty("Freistellung_LE_bis")
        @JsonIgnore
        String freistellungLEBis, //<Property Name="Freistellung_LE_bis" Type="Edm.Date">
        @JsonProperty("Datensatz_Gesperrt")
        String datensatzGesperrt, //<Property Name="Datensatz_Gesperrt" Type="Edm.String">
        @JsonProperty("Sperrhinweis")
        String sperrhinweis, //<Property Name="Sperrhinweis" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gültig_ab")
        String gueltigAb, //<Property Name="Gültig_ab" Type="Edm.Date">
        @JsonProperty("Gültig_bis")
        String gueltigBis, //<Property Name="Gültig_bis" Type="Edm.Date">
        @JsonProperty("Global_Dimension_1_Filter")
        String globalDimension1Filter //<Property Name="Global_Dimension_1_Filter" Type="Edm.String" MaxLength="20">
//        <NavigationProperty Name="Language_Code_Link" Type="Collection(NAV.Sprachen)" ContainsTarget="true">
//        <ReferentialConstraint Property="Language_Code" ReferencedProperty="Code" />
//        </NavigationProperty>
//        <NavigationProperty Name="Adressat_Avise_Link" Type="Collection(NAV.adressatenuebersicht)" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressat_Avise" ReferencedProperty="Adressat" />
//        <ReferentialConstraint Property="Adressnr_Avise" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="Adressat_Mahnung_Link" Type="Collection(NAV.adressatenuebersicht)" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressat_Mahnung" ReferencedProperty="Adressat" />
//        <ReferentialConstraint Property="Adressnr_Mahnungen" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="Adressat_Mahnung_Kopie_Link" Type="Collection(NAV.adressatenuebersicht)" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressat_Mahnung_Kopie" ReferencedProperty="Adressat" />
//        <ReferentialConstraint Property="Adressnr_Mahnungen_Kopie" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="Bill_to_Customer_No_Link" Type="Collection(NAV.debitorenuebersicht)" ContainsTarget="true">
//        <ReferentialConstraint Property="Bill_to_Customer_No" ReferencedProperty="No" />
//        </NavigationProperty>
//        <NavigationProperty Name="Invoice_Disc_Code_Link" Type="Collection(NAV.debitorenuebersicht)" ContainsTarget="true">
//        <ReferentialConstraint Property="Invoice_Disc_Code" ReferencedProperty="No" />
//        </NavigationProperty>
//        <Annotation Term="NAV.LabelId" String="debitorenkarte" />
//        </EntityType>
) {
}
