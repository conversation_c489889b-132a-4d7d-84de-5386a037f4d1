package de.systeex.cip.nevaris.adressen.json_producer.infrastructure;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.adressen.json_producer.domain.NevarisMcCustomer;
import de.systeex.cip.types.Adresse;
import de.systeex.cip.types.Geschaeftspartner;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class UpdateNevarisDebitorProcessor implements Processor {

    private final BasicCredentialsProvider credentialsProvider;
    private final String nevarisHost;
    private final String nevarisBasePath;
    private final String nevarisMandant;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public UpdateNevarisDebitorProcessor(String username, String password, String workstation, String domain, String nevarisHost, String nevarisBasePath, String nevarisMandant) {
        Credentials credentials = new NTCredentials(username, password, workstation, domain);
        this.credentialsProvider = new BasicCredentialsProvider();
        this.credentialsProvider.setCredentials(AuthScope.ANY, credentials);
        this.nevarisHost = nevarisHost;
        this.nevarisBasePath = nevarisBasePath;
        this.nevarisMandant = nevarisMandant;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        Adresse adresse = this.trimAdresse(exchange.getIn().getBody(Geschaeftspartner.class).adresse());
        Geschaeftspartner geschaeftspartner = this.trimGeschaeftspartner(exchange.getIn().getBody(Geschaeftspartner.class), adresse);
        HttpGet httpGet;
        HttpPost httpPost;
        CloseableHttpResponse response;
        String responseBody;
        URL url;
        URI uri;

        String nevarisAdressNr = exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class);
        String nevarisDebitorNr = exchange.getIn().getHeader("NEVARIS_DEBITOR_NR", String.class);
        String mandant = Objects.toString(this.nevarisMandant, "").isEmpty() ? exchange.getIn().getHeader("NEVARIS_MANDANT", String.class) : this.nevarisMandant;

        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .setMaxConnPerRoute(100000)
                .build()) {

            // McCustomer aktualisieren
            // gewünschter Mandant
            url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + mandant + "')/mcCustomer('" + nevarisDebitorNr + "')");
            uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            httpGet = new HttpGet(uri.toASCIIString());
            response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == 404) {
                url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + mandant + "')/mcCustomerInsert");
                uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                httpPost = new HttpPost(uri.toASCIIString());
                httpPost.setEntity(new StringEntity(objectMapper.writeValueAsString(this.geschaeftspartnerToNevarisMcCustomer(geschaeftspartner, nevarisDebitorNr, nevarisAdressNr)), ContentType.APPLICATION_JSON));
                System.out.println("POST mcCustomer Payload: " + objectMapper.writeValueAsString(this.geschaeftspartnerToNevarisMcCustomer(geschaeftspartner, nevarisDebitorNr, nevarisAdressNr)));
                response = httpClient.execute(httpPost);
                responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() >= 300 || response.getStatusLine().getStatusCode() < 200) {
                    throw new Exception("Error while creating mcCustomer Mandant '" + mandant + "': " + responseBody + " Status: " + response.getStatusLine().getStatusCode());
                }
                System.out.println("mcCustomer POST: " + responseBody);
            }
        }

        exchange.getIn().setBody(geschaeftspartner);
    }

    private Adresse trimAdresse(Adresse adresse) {
        return new Adresse(
                adresse.mandant().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse.sourceSystemName().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.sourceSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.destinationSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.name(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.suchbegriff(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.adresse(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.adresse2(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.adresse3(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.strasse(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.laendercode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.plz(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.ort(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.plzPostfach(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse.postfachNutzen(),
                Objects.toString(adresse.postfach(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.ortPostfach(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.bundesland(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.sprache(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.auslandsvorwahl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.ortskennzahl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.hauptanschlussnr(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.durchwahlZentrale(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.durchwahlFax(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.durchwahlModem(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.telefonnrKompl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.faxNrKompl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.modemNrKompl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.mail(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.wwwAdresse(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.landkreis(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.handelsregister(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.uStIdNr(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.steuernummerGesellschaft(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse.gesperrt(),
                adresse.gueltigAb(),
                adresse.gueltigBis(),
                adresse.adressaten()
        );
    }

    private Geschaeftspartner trimGeschaeftspartner(Geschaeftspartner geschaeftspartner, Adresse adresse) {
        return new Geschaeftspartner(
                geschaeftspartner.mandant().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                geschaeftspartner.sourceSystemName().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.sourceSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.unsereKundennummerDort(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.destinationSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                geschaeftspartner.geschaeftspartnerArt(),
                Objects.toString(geschaeftspartner.zahlungsbedingungsCode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.zahlungsmethodeCode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.zahlungserinnerungCode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.debitorenbuchungsgruppe(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.mahnmethodencode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.zinskonditionencode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse
        );
    }

    private NevarisMcCustomer geschaeftspartnerToNevarisMcCustomer(Geschaeftspartner geschaeftspartner, String debitorenNummer, String adressNummer) {
        return new NevarisMcCustomer(
                null,
                null,
                debitorenNummer,
                truncate(geschaeftspartner.adresse().name(), 70),
                truncate(geschaeftspartner.adresse().name(), 100),
                null,
                null,
                truncate(geschaeftspartner.adresse().name(), 100),
                adressNummer,
                null,
                null,
                null,
                null,
                null,
                null,
                geschaeftspartner.debitorenbuchungsgruppe(),
                geschaeftspartner.zinskonditionencode(),
                geschaeftspartner.mahnmethodencode()
        );
    }

    private String truncate(String value, int length) {
        if (value == null)
            return null;

        return value.substring(0, Math.min(value.length(), length));
    }
}
