package de.systeex.cip.nevaris.adressen.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisMcCustomer(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonIgnore
        String odataEtag,
        @JsonProperty("No")
        @Nonnull
        String no, //<Property Name="No" Type="Edm.String" Nullable="false" MaxLength="20">
        @JsonProperty("Name_lang")
        String nameLang, //<Property Name="Name_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Name")
        String name, //<Property Name="Name" Type="Edm.String" MaxLength="100">
        @JsonProperty("Name_2")
        String name2, //<Property Name="Name_2" Type="Edm.String" MaxLength="50">
        @JsonProperty("Name_2_lang")
        @JsonIgnore
        String name2Lang, //<Property Name="Name_2_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Search_Name")
        String searchName, //<Property Name="Search_Name" Type="Edm.String" MaxLength="100">
        @JsonProperty("Adressnr_des_Debitors")
        String adressnrDesDebitors, //<Property Name="Adressnr_des_Debitors" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adresse")
        String address, //<Property Name="Address" Type="Edm.String" MaxLength="100">
        @JsonProperty("Adresse_lang")
        String addressLang, //<Property Name="Adresse_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Address_2")
        String address2, //<Property Name="Address_2" Type="Edm.String" MaxLength="50">
        @JsonProperty("Adresse_2_lang")
        String address2Lang, //<Property Name="Adresse_2_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Ort_II")
        String ortII, //<Property Name="Ort_II" Type="Edm.String" MaxLength="70">
        @JsonProperty("Country_Region_Code")
        String countryRegionCode, //<Property Name="Country_Region_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Customer_Posting_Group")
        String customerPostingGroup, //<Property Name="Customer_Posting_Group" Type="Edm.String" MaxLength="20">
        @JsonProperty("Fin_Charge_Terms_Code")
        String finChargeTermsCode, //<Property Name="Fin_Charge_Terms_Code" Type="Edm.String" MaxLength="10">
        @JsonProperty("Reminder_Terms_Code")
        String reminderTermsCode //<Property Name="Reminder_Terms_Code" Type="Edm.String" MaxLength="10">
//        <NavigationProperty Name="Country_Region_Code_Link" Type="Collection(NAV.Laender)" ContainsTarget="true">
//        <ReferentialConstraint Property="Country_Region_Code" ReferencedProperty="Code" />
//        </NavigationProperty>
//        <Annotation Term="NAV.LabelId" String="mcCustomer" />
//        </EntityType>
) {
}
