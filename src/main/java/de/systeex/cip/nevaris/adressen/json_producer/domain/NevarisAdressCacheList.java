package de.systeex.cip.nevaris.adressen.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

public class NevarisAdressCacheList implements Serializable {

    @JsonProperty("@odata.context")
    private String oDataContext;

    private List<NevarisAdressCache> value;

    private NevarisAdressCacheList() {

    }

    public String getoDataContext() {
        return oDataContext;
    }

    public List<NevarisAdressCache> getValue() {
        return value;
    }
}
