package de.systeex.cip.nevaris.adressen.json_producer.infrastructure;

import java.util.ArrayList;

public class NameSplitter {

    public static String[] splitName(String name, int maxFieldLength) {
        // Aufteilen des Namens in Wörter
        String[] nameParts = name.split(" ");
        ArrayList<String> updatedNameParts = new ArrayList<>();
        for (String word : nameParts) {
            if (word.length() > 30) {
                int startIndex = 0;
                while (startIndex < word.length()) {
                    int endIndex = Math.min(startIndex + 30, word.length());
                    updatedNameParts.add(word.substring(startIndex, endIndex));
                    startIndex = endIndex;
                }
            } else {
                updatedNameParts.add(word);
            }
        }

        // Initialisieren der Adressfelder
        String[] addressFields = {"", "", ""};

        // Iteration durch die aufgeteilten Namensteile
        int currentField = 0;
        for (String word : updatedNameParts) {
            if (addressFields[currentField].length() + word.length() + (addressFields[currentField].isEmpty()?0:1) > maxFieldLength) {
                // Wenn das aktuelle Feld die maximale Länge überschreitet, verschiebe den Inhalt in das nächste leere Feld
                currentField++;
            }
            if (currentField >= 3) {
                break; // Alle Felder sind bereits gefüllt
            }
            if (!addressFields[currentField].isEmpty()) {
                addressFields[currentField] += " ";
            }
            addressFields[currentField] += word;
        }

        // Rückgabe der aufgeteilten Namensteile als Array
        return addressFields;
    }
}
