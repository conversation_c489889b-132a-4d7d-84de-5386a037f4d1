package de.systeex.cip.nevaris.adressen.json_producer.infrastructure;

import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public class NevarisAdressIdToBrokerErrorMessageTransformer extends ErrorMessageTransformer {
    public NevarisAdressIdToBrokerErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.INTERNAL;
    }
}
