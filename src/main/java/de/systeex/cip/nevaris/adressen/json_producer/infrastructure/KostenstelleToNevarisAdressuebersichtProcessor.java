package de.systeex.cip.nevaris.adressen.json_producer.infrastructure;

import de.systeex.cip.nevaris.adressen.json_producer.NevarisAdressuebersicht;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCode;
import de.systeex.cip.types.Adresse;
import de.systeex.cip.types.Kostenstelle;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

import static de.systeex.cip.nevaris.adressen.json_producer.domain.NevarisAdressCache.NULL_DATE;

public class KostenstelleToNevarisAdressuebersichtProcessor implements Processor {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public void process(Exchange exchange) throws Exception {
        Kostenstelle kostenstelle = exchange.getIn().getBody(Kostenstelle.class);
        Adresse baustelle = kostenstelle.baustelle();
        NevarisPLZCode nevarisPLZCode = exchange.getIn().getHeader("NEVARIS_PLZ_CODE", NevarisPLZCode.class);

        if (baustelle == null) {
            throw new Exception("Keine Adressdaten in der Kostenstelle vorhanden");
        }

        String adresse1 = null;
        String adresse2 = null;
        String adresse3 = null;
        if (baustelle.name() != null) {
            String[] adressFields = NameSplitter.splitName(baustelle.name(),30);
            adresse1 = adressFields[0];
            adresse2 = adressFields[1];
            adresse3 = adressFields[2];
        }

        NevarisAdressuebersicht nevarisAdressuebersicht = new NevarisAdressuebersicht(
                null,
                null,
                truncate(exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class), 10),
                truncate(Objects.toString(baustelle.name(), ""), 70),
                truncate(Objects.toString(adresse1, ""), 70),
                truncate(Objects.toString(adresse2, ""), 70),
                truncate(Objects.toString(adresse3, ""), 70),
                truncate(Objects.toString(baustelle.strasse(), ""), 70),
                null,
                null,
                null,
                null,
                null,
                truncate(Objects.toString(nevarisPLZCode.land(), "DE"), 10),
                truncate(Objects.toString(nevarisPLZCode.code(), ""), 20),
                truncate(Objects.toString(nevarisPLZCode.city(), ""), 70),
                truncate(Objects.toString(baustelle.suchbegriff(), ""), 30),
                "",
                truncate(Objects.toString(baustelle.plzPostfach(), ""), 20),
                truncate(Objects.toString(baustelle.ortPostfach(), ""), 70),
                baustelle.postfachNutzen() != null && baustelle.postfachNutzen(),
                truncate(Objects.toString(baustelle.postfach(), ""), 10),
                truncate(Objects.toString(baustelle.sprache(), ""), 10),
                truncate(Objects.toString(nevarisPLZCode.bundesland(), ""), 10),
                truncate(Objects.toString(nevarisPLZCode.landkreis(), ""), 10),
                truncate(Objects.toString(baustelle.auslandsvorwahl(), ""), 20),
                truncate(Objects.toString(baustelle.ortskennzahl(), ""), 20),
                truncate(Objects.toString(baustelle.hauptanschlussnr(), ""), 30),
                truncate(Objects.toString(baustelle.durchwahlZentrale(), ""), 10),
                truncate(Objects.toString(baustelle.durchwahlFax(), ""), 10),
                truncate(Objects.toString(baustelle.durchwahlModem(), ""), 10),
                truncate(Objects.toString(baustelle.telefonnrKompl(), ""), 100),
                truncate(Objects.toString(baustelle.faxNrKompl(), ""), 100),
                truncate(Objects.toString(baustelle.modemNrKompl(), ""), 100),
                truncate(Objects.toString(baustelle.mail(), ""), 50),
                truncate(Objects.toString(baustelle.wwwAdresse(), ""), 250),
                "",
                "",
                "",
                "",
                "",
                false,
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                null,
                null,
                false,
                null,
                this.parseBoolean(baustelle.gesperrt()),
                "",
                this.parseDate(baustelle.gueltigAb()),
                this.parseDate(baustelle.gueltigBis()),
                truncate(Objects.toString(baustelle.handelsregister(), ""), 80),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "",
                "",
                "",
                this.parseDate(null),
                "Keine Angabe",
                this.parseDate(null),
                this.parseDate(null),
                "",
                truncate(Objects.toString(baustelle.uStIdNr(), ""), 20),
                truncate(Objects.toString(baustelle.steuernummerGesellschaft(), ""), 20),
                "",
                "",
                 "",
                "",
                "",
                "",
                NevarisAdressuebersicht.NULL_DATE,
                0,
                "",
                false
                );

        exchange.getIn().setBody(nevarisAdressuebersicht);
    }

    private String parseDate(LocalDate datum) {
        if (datum == null) {
            return NULL_DATE;
        } else {
            return datum.format(DATE_FORMATTER);
        }
    }

    private String parseBoolean(Boolean boolValue) {
        if (boolValue != null && boolValue) {
            return "Ja";
        } else {
            return "Nein";
        }
    }

    private String truncate(String value, int length) {
        if (value == null)
            return null;

        return value.substring(0, Math.min(value.length(), length));
    }


}
