package de.systeex.cip.nevaris.adressen.json_producer.infrastructure;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersichtListe;
import de.systeex.cip.nevaris.adressen.json_producer.NevarisAdressuebersicht;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCodeList;
import de.systeex.cip.nevaris.domain.exceptions.GenericNevarisExceptionWrapper;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCode;
import de.systeex.cip.nevaris.domain.exceptions.TableLockException;
import de.systeex.cip.types.Adresse;
import de.systeex.cip.types.Geschaeftspartner;
import de.systeex.cip.types.Mitarbeiter;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.methods.*;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


public class UpdateNevarisAdresseProcessor implements Processor {

    private final BasicCredentialsProvider credentialsProvider;
    private final String nevarisHost;
    private final String nevarisBasePath;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public UpdateNevarisAdresseProcessor(String username, String password, String workstation, String domain, String nevarisHost, String nevarisBasePath) {
        Credentials credentials = new NTCredentials(username, password, workstation, domain);
        this.credentialsProvider = new BasicCredentialsProvider();
        this.credentialsProvider.setCredentials(AuthScope.ANY, credentials);
        this.nevarisHost = nevarisHost;
        this.nevarisBasePath = nevarisBasePath;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        Adresse adresse = this.trimAdresse(exchange.getIn().getBody(Geschaeftspartner.class).adresse());
        Geschaeftspartner geschaeftspartner = this.trimGeschaeftspartner(exchange.getIn().getBody(Geschaeftspartner.class), adresse);
        List<Mitarbeiter> mitarbeiterList = geschaeftspartner.adresse().adressaten();
        HttpGet httpGet;
        HttpPatch httpPatch;
        HttpPost httpPost;
        HttpDelete httpDelete;
        CloseableHttpResponse response;
        String responseBody;
        URL url;
        URI uri;

        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .setMaxConnPerRoute(100000)
                .build()) {

            String nevarisAdressNr = exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class);
            String nevarisMandant = exchange.getIn().getHeader("NEVARIS_MANDANT", String.class);
            String nevarisDebitorNr = exchange.getIn().getHeader("NEVARIS_DEBITOR_NR", String.class);


            url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/mcCustomer('" + nevarisDebitorNr + "')");
            uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            httpGet = new HttpGet(uri.toASCIIString());
            response = httpClient.execute(httpGet);
            boolean debitorExists = response.getStatusLine().getStatusCode() != 404;

            // PLZ Objekt holen
            url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/PLZCodes('" + geschaeftspartner.adresse().plz() + "', '" + geschaeftspartner.adresse().ort() + "')");
            uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            httpGet = new HttpGet(uri.toASCIIString());

            response = httpClient.execute(httpGet);

            NevarisPLZCode nevarisPLZCode;

            if (response.getStatusLine().getStatusCode() == 200) {
                responseBody = EntityUtils.toString(response.getEntity());
                nevarisPLZCode = objectMapper.readValue(responseBody, NevarisPLZCode.class);
            } else {
                url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/PLZCodes?$filter=Code eq '" + geschaeftspartner.adresse().plz() + "'");
                uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                httpGet = new HttpGet(uri.toASCIIString());
                response = httpClient.execute(httpGet);
                responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() == 200) {
                    NevarisPLZCodeList nevarisPLZCodeList = objectMapper.readValue(responseBody, NevarisPLZCodeList.class);
                    if (nevarisPLZCodeList.values().size() == 1) {
                        nevarisPLZCode = nevarisPLZCodeList.values().get(0);
                    } else {
                        throw new Exception("Failed to get PLZ code from Nevaris. PLZ: '" + geschaeftspartner.adresse().plz() + "', Ort: '" + geschaeftspartner.adresse().ort() + "'");
                    }
                } else {
                    throw new Exception("Failed to get PLZ code from Nevaris. Response: " + responseBody + " Status " + response.getStatusLine().getStatusCode());
                }
            }

            // Adresse aktualisieren
            url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressuebersicht('" + nevarisAdressNr + "')");
            uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            httpGet = new HttpGet(uri.toASCIIString());
            response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == 404) {
                url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressuebersicht");
                uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                httpPost = new HttpPost(uri.toASCIIString());
                httpPost.setEntity(new StringEntity(objectMapper.writeValueAsString(this.adresseToNevarisAdressuebersicht(adresse, nevarisAdressNr, geschaeftspartner.unsereKundennummerDort(), nevarisPLZCode)), ContentType.APPLICATION_JSON));
                System.out.println("POST Adressübersicht Payload: " + objectMapper.writeValueAsString(this.adresseToNevarisAdressuebersicht(adresse, nevarisAdressNr, geschaeftspartner.unsereKundennummerDort(), nevarisPLZCode)));
                response = httpClient.execute(httpPost);
                responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() >= 300 || response.getStatusLine().getStatusCode() < 200) {
                    GenericNevarisExceptionWrapper genericNevarisExceptionWrapper = objectMapper.readValue(responseBody, GenericNevarisExceptionWrapper.class);
                    if (genericNevarisExceptionWrapper.isTableLockException()) {
                        throw new TableLockException(responseBody);
                    }
                    throw new Exception("Error while creating Adressübersicht: " + responseBody + " Status: " + response.getStatusLine().getStatusCode());
                }
                System.out.println("Adressübersicht POST: " + responseBody);
            } else {
                if (!debitorExists) {
                    responseBody = EntityUtils.toString(response.getEntity());
                    NevarisAdressuebersicht nevarisAdressuebersicht = objectMapper.readValue(responseBody, NevarisAdressuebersicht.class);
                    url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressuebersicht('" + nevarisAdressNr + "')");
                    uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                    httpPatch = new HttpPatch(uri.toASCIIString());
                    httpPatch.setHeader("If-Match", nevarisAdressuebersicht.odataEtag());
                    httpPatch.setEntity(new StringEntity(objectMapper.writeValueAsString(this.adresseToNevarisAdressuebersicht(adresse, nevarisAdressNr, geschaeftspartner.unsereKundennummerDort(), nevarisPLZCode)), ContentType.APPLICATION_JSON));
                    response = httpClient.execute(httpPatch);
                    responseBody = EntityUtils.toString(response.getEntity());
                    if (response.getStatusLine().getStatusCode() >= 300 || response.getStatusLine().getStatusCode() < 200) {
                        throw new Exception("Error while updating Adressübersicht: " + responseBody + " Status: " + response.getStatusLine().getStatusCode());
                    }
                    System.out.println("Adressübersicht PATCH: " + responseBody);
                }
            }


            // Adressaten aktualisieren
            url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht?$filter=Adressnr eq '" + nevarisAdressNr + "'");
            uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
            httpGet = new HttpGet(uri.toASCIIString());
            response = httpClient.execute(httpGet);
            responseBody = EntityUtils.toString(response.getEntity());
            System.out.println("Adressaten: " + responseBody);
            NevarisAdressatenuebersichtListe nevarisAdressatenuebersichtListe = objectMapper.readValue(responseBody, NevarisAdressatenuebersichtListe.class);

            for (Mitarbeiter mitarbeiter : mitarbeiterList) {
                String personalnr = truncate(mitarbeiter.personalnr(), 10).toUpperCase();
                boolean found = false;
                for (int k = 0; k < nevarisAdressatenuebersichtListe.values().size(); k++) {
                    if (personalnr.equals(nevarisAdressatenuebersichtListe.values().get(k).adressat())) {
                        found = true;
                        break;
                    }
                }
                if (found) {
                    url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht('" + nevarisAdressNr + "', '" + personalnr + "')");
                    uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                    httpGet = new HttpGet(uri.toASCIIString());
                    response = httpClient.execute(httpGet);
                    responseBody = EntityUtils.toString(response.getEntity());
                    NevarisAdressatenuebersicht nevarisAdressatenuebersicht = objectMapper.readValue(responseBody, NevarisAdressatenuebersicht.class);
                    NevarisAdressatenuebersicht adressatenuebersicht = this.mitarbeiterToNevarisAdressatenuebersicht(mitarbeiter, nevarisAdressNr);
                    url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht('" + nevarisAdressNr + "', '" + nevarisAdressatenuebersicht.adressat() + "')");
                    uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                    httpPatch = new HttpPatch(uri.toASCIIString());
                    httpPatch.setHeader("If-Match", nevarisAdressatenuebersicht.odataEtag());
                    httpPatch.setEntity(new StringEntity(objectMapper.writeValueAsString(adressatenuebersicht), ContentType.APPLICATION_JSON));
                    response = httpClient.execute(httpPatch);
                    responseBody = EntityUtils.toString(response.getEntity());
                    if (response.getStatusLine().getStatusCode() >= 300 || response.getStatusLine().getStatusCode() < 200) {
                        throw new Exception("Error while updating Adressatenuebersicht: " + responseBody + " Status: " + response.getStatusLine().getStatusCode());
                    }
                } else {
                    NevarisAdressatenuebersicht adressatenuebersicht = this.mitarbeiterToNevarisAdressatenuebersicht(mitarbeiter, nevarisAdressNr);
                    url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht");
                    uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                    httpPost = new HttpPost(uri.toASCIIString());
                    httpPost.setEntity(new StringEntity(objectMapper.writeValueAsString(adressatenuebersicht), ContentType.APPLICATION_JSON));
                    response = httpClient.execute(httpPost);
                    responseBody = EntityUtils.toString(response.getEntity());
                    if (response.getStatusLine().getStatusCode() >= 300 || response.getStatusLine().getStatusCode() < 200) {
                        throw new Exception("Error while creating Adressatenuebersicht: " + responseBody + " Status " + response.getStatusLine().getStatusCode());
                    }
                }
                System.out.println("Adressat PATCH/POST: " + responseBody);
            }

            for (NevarisAdressatenuebersicht nevarisAdressatenuebersicht : nevarisAdressatenuebersichtListe.values()) {
                boolean found = false;
                for (Mitarbeiter mitarbeiter : mitarbeiterList) {
                    String personalnr = truncate(mitarbeiter.personalnr(), 10).toUpperCase();
                    if (nevarisAdressatenuebersicht.adressat().equals(personalnr)) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht('" + nevarisAdressNr + "', '" + nevarisAdressatenuebersicht.adressat() + "')");
                    uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                    httpGet = new HttpGet(uri.toASCIIString());
                    response = httpClient.execute(httpGet);
                    responseBody = EntityUtils.toString(response.getEntity());
                    NevarisAdressatenuebersicht nevarisAdressatenuebersicht1 = objectMapper.readValue(responseBody, NevarisAdressatenuebersicht.class);
                    url = new URL("http://" + nevarisHost + nevarisBasePath + "/Company('" + nevarisMandant + "')/adressatenuebersicht('" + nevarisAdressNr + "', '" + nevarisAdressatenuebersicht.adressat() + "')");
                    uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
                    httpDelete = new HttpDelete(uri.toASCIIString());
                    httpDelete.setHeader("If-Match", nevarisAdressatenuebersicht1.odataEtag());
                    response = httpClient.execute(httpDelete);
                    responseBody = EntityUtils.toString(response.getEntity());
                    if (response.getStatusLine().getStatusCode() >= 300 || response.getStatusLine().getStatusCode() < 200) {
                        throw new Exception("Error while deleting Adressatenuebersicht: " + responseBody + " Status " + response.getStatusLine().getStatusCode());
                    }
                    System.out.println("Adressat DELETE: " + nevarisAdressatenuebersicht1.adressat() + " " + nevarisAdressatenuebersicht1.adressnr());
                }
            }
        }


        exchange.getIn().setBody(geschaeftspartner);
    }

    private NevarisAdressatenuebersicht mitarbeiterToNevarisAdressatenuebersicht(Mitarbeiter mitarbeiter, String adressNummer) {
        return new NevarisAdressatenuebersicht(
                null,
                null,
                truncate(adressNummer, 10),
                truncate(mitarbeiter.personalnr(), 10),
                truncate("", 70),
                truncate(mitarbeiter.anrede(), 10),
                truncate(mitarbeiter.vorname(), 70),
                truncate(mitarbeiter.name(), 70),
                truncate(mitarbeiter.titel(), 20),
                truncate(mitarbeiter.titelImAnschreiben(), 50),
                truncate(mitarbeiter.durchwahl(), 20),
                truncate(mitarbeiter.fax(), 20),
                truncate(mitarbeiter.telefonnrKompl(), 100),
                truncate(mitarbeiter.faxnrKompl(), 100),
                truncate(mitarbeiter.mobiltelefon(), 20),
                truncate(mitarbeiter.zielFunktion(), 10),
                truncate(mitarbeiter.abteilung(), 10),
                this.truncate(mitarbeiter.mail(), 50),
                truncate(mitarbeiter.wwwAdresse(), 250),
                truncate(mitarbeiter.adressnrPrivat(), 10),
                this.parseDate(mitarbeiter.geburtsdatum()),
                truncate(mitarbeiter.raum(), 100),
                truncate(mitarbeiter.externeAdressnr(), 20),
                mitarbeiter.inaktiv(),
                this.parseDate(mitarbeiter.austrittsdatum()),
                truncate(mitarbeiter.sprache(), 10),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
    }

    private NevarisAdressuebersicht adresseToNevarisAdressuebersicht(Adresse adresse, String adressNummer, String unsereKundennummerDort, NevarisPLZCode nevarisPLZCode) {
        return new NevarisAdressuebersicht(
                null,
                null,
                truncate(adressNummer, 10),
                truncate(adresse.name(), 70),
                truncate(adresse.adresse(), 70),
                truncate(adresse.adresse2(), 70),
                truncate(adresse.adresse3(), 70),
                truncate(adresse.strasse(), 70),
                null,
                null,
                null,
                null,
                null,
                truncate(nevarisPLZCode.land(), 10),
                truncate(nevarisPLZCode.code(), 20),
                truncate(nevarisPLZCode.city(), 70),
                truncate(adresse.name(), 30),
                null,
                truncate(adresse.plzPostfach(), 20),
                truncate(adresse.ortPostfach(), 70),
                adresse.postfachNutzen(),
                truncate(adresse.postfach(), 10),
                truncate(adresse.sprache(), 10),
                truncate(nevarisPLZCode.bundesland(), 10),
                truncate(nevarisPLZCode.landkreis(), 10),
                truncate(adresse.auslandsvorwahl(), 20),
                truncate(adresse.ortskennzahl(), 20),
                truncate(adresse.hauptanschlussnr(), 30),
                truncate(adresse.durchwahlZentrale(), 10),
                truncate(adresse.durchwahlFax(), 10),
                truncate(adresse.durchwahlModem(), 10),
                truncate(adresse.telefonnrKompl(), 100),
                truncate(adresse.faxNrKompl(), 100),
                truncate(adresse.modemNrKompl(), 100),
                truncate(adresse.mail(), 50),
                truncate(adresse.wwwAdresse(), 250),
                null,
                null,
                null,
                null,
                null,
                null,
                truncate(unsereKundennummerDort, 20),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                this.parseBoolean(adresse.gesperrt()),
                null,
                this.parseDate(adresse.gueltigAb()),
                this.parseDate(adresse.gueltigBis()),
                truncate(adresse.handelsregister(), 80),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                truncate(adresse.uStIdNr(), 20),
                truncate(adresse.steuernummerGesellschaft(), 20),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
    }

    private Adresse trimAdresse(Adresse adresse) {
        return new Adresse(
                adresse.mandant().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse.sourceSystemName().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.sourceSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.destinationSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.name(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.suchbegriff(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.adresse(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.adresse2(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.adresse3(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.strasse(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.laendercode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.plz(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.ort(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.plzPostfach(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse.postfachNutzen(),
                Objects.toString(adresse.postfach(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.ortPostfach(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.bundesland(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.sprache(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.auslandsvorwahl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.ortskennzahl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.hauptanschlussnr(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.durchwahlZentrale(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.durchwahlFax(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.durchwahlModem(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.telefonnrKompl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.faxNrKompl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.modemNrKompl(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.mail(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.wwwAdresse(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.landkreis(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.handelsregister(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.uStIdNr(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(adresse.steuernummerGesellschaft(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse.gesperrt(),
                adresse.gueltigAb(),
                adresse.gueltigBis(),
                adresse.adressaten()
        );
    }

    private Geschaeftspartner trimGeschaeftspartner(Geschaeftspartner geschaeftspartner, Adresse adresse) {
        return new Geschaeftspartner(
                geschaeftspartner.mandant().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                geschaeftspartner.sourceSystemName().trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.sourceSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.unsereKundennummerDort(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.destinationSystemKey(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                geschaeftspartner.geschaeftspartnerArt(),
                Objects.toString(geschaeftspartner.zahlungsbedingungsCode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.zahlungsmethodeCode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.zahlungserinnerungCode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.debitorenbuchungsgruppe(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.mahnmethodencode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                Objects.toString(geschaeftspartner.zinskonditionencode(), "").trim().replaceAll("(\\s|\\\\r\\\\n)+", " "),
                adresse
        );
    }

    private String truncate(String value, int length) {
        if (value == null)
            return null;

        return value.substring(0, Math.min(value.length(), length));
    }

    private String parseDate(LocalDate datum) {
        if (datum == null) {
            return NevarisAdressatenuebersicht.NULL_DATE;
        } else {
            return datum.format(DATE_FORMATTER);
        }
    }

    private String parseBoolean(Boolean boolValue) {
        if (boolValue != null && boolValue) {
            return "Ja";
        } else {
            return "Nein";
        }
    }
}
