package de.systeex.cip.nevaris.adressen.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisAdresskarte(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonIgnore
        String odataEtag,
        @JsonProperty("Adressnr")
        @Nonnull
        String adressnr, //<Property Name="Adressnr" Type="Edm.String" Nullable="false" MaxLength="10">
        @JsonProperty("Name_lang")
        String nameLang, //<Property Name="Name_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Name")
        @JsonIgnore
        String name, //<Property Name="Name" Type="Edm.String" MaxLength="30">
        @JsonProperty("Suchbegriff")
        String suchbegriff, //<Property Name="Suchbegriff" Type="Edm.String" MaxLength="30">
        @JsonProperty("Adresse_lang")
        String adresseLang, //<Property Name="Adresse_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse")
        @JsonIgnore
        String adresse, //<Property Name="Adresse" Type="Edm.String" MaxLength="30">
        @JsonProperty("Adresse_2_lang")
        String adresse2Lang, //<Property Name="Adresse_2_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse2")
        @JsonIgnore
        String adresse2, //<Property Name="Adresse2" Type="Edm.String" MaxLength="30">
        @JsonProperty("Adresse_3_lang")
        String adresse3Lang, //<Property Name="Adresse_3_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse3")
        @JsonIgnore
        String adresse3, //<Property Name="Adresse3" Type="Edm.String" MaxLength="30">
        @JsonProperty("Straße_lang")
        String strasseLang, //<Property Name="Straße_lang" Type="Edm.String" MaxLength="70">
        @JsonProperty("Straße")
        @JsonIgnore
        String strasse, //<Property Name="Straße" Type="Edm.String" MaxLength="30">
        @JsonProperty("PLZ")
        String plz, //<Property Name="PLZ" Type="Edm.String" MaxLength="20">
        @JsonProperty("Ort")
        String ort, //<Property Name="Ort" Type="Edm.String" MaxLength="70">
        @JsonProperty("Ländercode")
        String laendercode, //<Property Name="Ländercode" Type="Edm.String" MaxLength="10">
        @JsonProperty("PLZ_Postfach")
        String plzPostfach, //<Property Name="PLZ_Postfach" Type="Edm.String" MaxLength="20">
        @JsonProperty("Ort_Postfach")
        String ortPostfach, //<Property Name="Ort_Postfach" Type="Edm.String" MaxLength="70">
        @JsonProperty("Postfach")
        String postfach, //<Property Name="Postfach" Type="Edm.String" MaxLength="10">
        @JsonProperty("Postfach_nutzen")
        Boolean postfachNutzen, //<Property Name="Postfach_nutzen" Type="Edm.Boolean">
        @JsonProperty("Standard_Adressat")
        String standardAdressat, //<Property Name="Standard_Adressat" Type="Edm.String" MaxLength="10">
        @JsonProperty("Standard_Adressat_Name")
        @JsonIgnore
        String standardAdressatName, //<Property Name="Standard_Adressat_Name" Type="Edm.String" MaxLength="70">
        @JsonProperty("Adressnr_Primärer_Betreuer")
        String adressnrPrimaererBetreuer,//<Property Name="Adressnr_Primärer_Betreuer" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressat_Primärer_Betreuer")
        String adressatPrimaererBetreuer,//<Property Name="Adressat_Primärer_Betreuer" Type="Edm.String" MaxLength="10">
        @JsonProperty("Vorname")
        String vorname, //<Property Name="Vorname" Type="Edm.String" MaxLength="100">
        @JsonProperty("Nachname")
        String nachname, //<Property Name="Nachname" Type="Edm.String" MaxLength="70">
        @JsonProperty("Namenskürzel")
        String namenskuerzel, //<Property Name="Namenskürzel" Type="Edm.String" MaxLength="20">
        @JsonProperty("Anrede")
        String anrede, //<Property Name="Anrede" Type="Edm.String" MaxLength="10">
        @JsonProperty("Titel")
        String titel, //<Property Name="Titel" Type="Edm.String" MaxLength="20">
        @JsonProperty("Titel_im_Anschreiben")
        String titelImAnschreiben, //<Property Name="Titel_im_Anschreiben" Type="Edm.String" MaxLength="50">
        @JsonProperty("Geburtsdatum")
        String geburtsdatum, //<Property Name="Geburtsdatum" Type="Edm.Date">
        @JsonProperty("Personenadresse")
        Boolean personenadresse, //<Property Name="Personenadresse" Type="Edm.Boolean">
        @JsonProperty("TmpGesamtbeurteilung")
        @JsonIgnore
        BigDecimal tmpGesamtbeurteilung, //<Property Name="TmpGesamtbeurteilung" Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("Ortskennzahl")
        String ortsKennzahl, //<Property Name="Ortskennzahl" Type="Edm.String" MaxLength="20">
        @JsonProperty("Hauptanschlußnr")
        String hauptanschlussnr, //<Property Name="Hauptanschlußnr" Type="Edm.String" MaxLength="30">
        @JsonProperty("Durchwahl_Zentrale")
        String durchwahlZentrale, //<Property Name="Durchwahl_Zentrale" Type="Edm.String" MaxLength="10">
        @JsonProperty("Telefonnr_kompl")
        String telefonnrKompl, //<Property Name="Telefonnr_kompl" Type="Edm.String" MaxLength="100">
        @JsonProperty("Durchwahl_Fax")
        String durchwahlFax, //<Property Name="Durchwahl_Fax" Type="Edm.String" MaxLength="10">
        @JsonProperty("Faxnr_kompl")
        String faxnrKompl, //<Property Name="Faxnr_kompl" Type="Edm.String" MaxLength="100">
        @JsonProperty("Durchwahl_Modem")
        String durchwahlModem, //<Property Name="Durchwahl_Modem" Type="Edm.String" MaxLength="10">
        @JsonProperty("Modemnr_kompl")
        String modemnrKompl, //<Property Name="Modemnr_kompl" Type="Edm.String" MaxLength="100">
        @JsonProperty("Auslandsvorwahl")
        String auslandsvorwahl, //<Property Name="Auslandsvorwahl" Type="Edm.String" MaxLength="20">
        @JsonProperty("E_Mail")
        String eMail, //<Property Name="E_Mail" Type="Edm.String" MaxLength="50">
        @JsonProperty("WWW_Adresse_URL")
        String wwwAdresseUrl, //<Property Name="WWW_Adresse_URL" Type="Edm.String" MaxLength="250">
        @JsonProperty("Adressnr_der_Zentrale")
        String adressnrDerZentrale, //<Property Name="Adressnr_der_Zentrale" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressnr_der_Organschaft")
        String adressnrDerOrganschaft, //<Property Name="Adressnr_der_Organschaft" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gruppe")
        String gruppe, //<Property Name="Gruppe" Type="Edm.String" MaxLength="10">
        @JsonProperty("Datenquelle")
        String datenquelle, //<Property Name="Datenquelle" Type="Edm.String" MaxLength="10">
        @JsonProperty("Handelsregister")
        String handelsregister, //<Property Name="Handelsregister" Type="Edm.String" MaxLength="80">
        @JsonProperty("Unsere_Kundennr_dort")
        String unsereKundennrDort, //<Property Name="Unsere_Kundennr_dort" Type="Edm.String" MaxLength="20">
        @JsonProperty("Externe_Adressnr")
        String externeAdressnr, //<Property Name="Externe_Adressnr" Type="Edm.String" MaxLength="20">
        @JsonProperty("Ursprungsnr")
        String ursprungsnr, //<Property Name="Ursprungsnr" Type="Edm.String" MaxLength="20">
        @JsonProperty("Verweis_auf_Adressnr")
        String verweisAufAdressnr, //<Property Name="Verweis_auf_Adressnr" Type="Edm.String" MaxLength="10">
        @JsonProperty("Duplikat")
        Boolean duplikat, //<Property Name="Duplikat" Type="Edm.Boolean">
        @JsonProperty("Debitor_vorhanden")
        @JsonIgnore
        Boolean debitorVorhanden, //<Property Name="Debitor_vorhanden" Type="Edm.Boolean">
        @JsonProperty("Kreditor_vorhanden")
        @JsonIgnore
        Boolean kreditorVorhanden, //<Property Name="Kreditor_vorhanden" Type="Edm.Boolean">
        @JsonProperty("Lieferant_vorhanden")
        @JsonIgnore
        Boolean lieferantVorhanden, //<Property Name="Lieferant_vorhanden" Type="Edm.Boolean">
        @JsonProperty("USt_IdNr")
        String uStIdNr, //<Property Name="USt_IdNr" Type="Edm.String" MaxLength="20">
        @JsonProperty("ToolsActions_ReturnString_TXTUIDAbfrage")
        @JsonIgnore
        String toolsActionsReturnStringTxtuidAbfrage, //<Property Name="ToolsActions_ReturnString_TXTUIDAbfrage" Type="Edm.String">
        @JsonProperty("Steuernummer_Gesellschaft")
        String steuernummerGesellschaft, //<Property Name="Steuernummer_Gesellschaft" Type="Edm.String" MaxLength="20">
        @JsonProperty("Bundesland")
        String bundesland, //<Property Name="Bundesland" Type="Edm.String" MaxLength="10">
        @JsonProperty("Landkreis")
        String landkreis, //<Property Name="Landkreis" Type="Edm.String" MaxLength="10">
        @JsonProperty("Sprache")
        String sprache, //<Property Name="Sprache" Type="Edm.String" MaxLength="10">
        @JsonProperty("LgU_Firmencode")
        String lgUFirmencode, //<Property Name="LgU_Firmencode" Type="Edm.String" MaxLength="36">
        @JsonProperty("Gesperrt")
        String gesperrt, //<Property Name="Gesperrt" Type="Edm.String">
        @JsonProperty("Sperrhinweis")
        String sperrhinweis, //<Property Name="Sperrhinweis" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gültig_ab")
        String gueltigAb, //<Property Name="Gültig_ab" Type="Edm.Date">
        @JsonProperty("Gültig_bis")
        String gueltigBis, //<Property Name="Gültig_bis" Type="Edm.Date">
        @JsonProperty("Zertifikat")
        String zertifikat, //<Property Name="Zertifikat" Type="Edm.String">
        @JsonProperty("Zertifizierung")
        String zertifizierung, //<Property Name="Zertifizierung" Type="Edm.Date">
        @JsonProperty("Gültigkeit_des_Zertifikats")
        String gueltigkeitDesZertifikats, //<Property Name="Gültigkeit_des_Zertifikats" Type="Edm.String">
        @JsonProperty("Neuzertifizierung")
        String neuzertifizierung, //<Property Name="Neuzertifizierung" Type="Edm.Date">
        @JsonProperty("Interne_Prüfung")
        String internePruefung, //<Property Name="Interne_Prüfung" Type="Edm.Date">
        @JsonProperty("QM_Sachbearbeiter")
        String qmSachbearbeiter, //<Property Name="QM_Sachbearbeiter" Type="Edm.String" MaxLength="10">
        @JsonProperty("QM_Sachbearbeiter_Name")
        @JsonIgnore
        String qmSachbearbeiterName, //<Property Name="QM_Sachbearbeiter_Name" Type="Edm.String" MaxLength="70">
        @JsonProperty("Mit_Inform_nicht_synchr")
        Boolean mitInformNichtSynchr //<Property Name="Mit_Inform_nicht_synchr" Type="Edm.Boolean">

//        <NavigationProperty Name="PLZ_Link" Type="Collection(NAV.PLZCodes)" ContainsTarget="true">
//        <ReferentialConstraint Property="PLZ" ReferencedProperty="Code" />
//        </NavigationProperty>
//        <NavigationProperty Name="Ländercode_Link" Type="Collection(NAV.Laender)" ContainsTarget="true">
//        <ReferentialConstraint Property="Ländercode" ReferencedProperty="Code" />
//        </NavigationProperty>
//        <NavigationProperty Name="PLZ_Postfach_Link" Type="Collection(NAV.PLZCodes)" ContainsTarget="true">
//        <ReferentialConstraint Property="PLZ_Postfach" ReferencedProperty="Code" />
//        </NavigationProperty>
//        <NavigationProperty Name="Standard_Adressat_Link" Type="Collection(NAV.adressatenuebersicht)" Partner="adresskarte" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="Adressat_Primärer_Betreuer_Link" Type="Collection(NAV.adressatenuebersicht)" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressat_Primärer_Betreuer" ReferencedProperty="Adressat" />
//        <ReferentialConstraint Property="Adressnr_Primärer_Betreuer" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="Bundesland_Link" Type="Collection(NAV.Bundeslaender)" ContainsTarget="true">
//        <ReferentialConstraint Property="Bundesland" ReferencedProperty="Code" />
//        </NavigationProperty>
//        <NavigationProperty Name="Landkreis_Link" Type="Collection(NAV.Landkreise)" ContainsTarget="true">
//        <ReferentialConstraint Property="Landkreis" ReferencedProperty="Landkreis" />
//        </NavigationProperty>
//        <NavigationProperty Name="Sprache_Link" Type="Collection(NAV.Sprachen)" ContainsTarget="true">
//        <ReferentialConstraint Property="Sprache" ReferencedProperty="Code" />
//        </NavigationProperty>
//        <NavigationProperty Name="QM_Sachbearbeiter_Link" Type="Collection(NAV.adressatenuebersicht)" ContainsTarget="true">
//        <ReferentialConstraint Property="QM_Sachbearbeiter" ReferencedProperty="Adressat" />
//        <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="adresskarteAdressatensubform" Type="Collection(NAV.adresskarteAdressatensubform)" Partner="adresskarte" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="adresskarteSubformAdresstyp" Type="Collection(NAV.adresskarteSubformAdresstyp)" Partner="adresskarte" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="adresskarteSubformZertifikat" Type="Collection(NAV.adresskarteSubformZertifikat)" Partner="adresskarte" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="adresskarteSubformBranche" Type="Collection(NAV.adresskarteSubformBranche)" Partner="adresskarte" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <NavigationProperty Name="adresskarteSubformGewerk" Type="Collection(NAV.adresskarteSubformGewerk)" Partner="adresskarte" ContainsTarget="true">
//        <ReferentialConstraint Property="Adressnr" ReferencedProperty="Adressnr" />
//        </NavigationProperty>
//        <Annotation Term="NAV.LabelId" String="adresskarte" />
//        </EntityType>
) {
}
