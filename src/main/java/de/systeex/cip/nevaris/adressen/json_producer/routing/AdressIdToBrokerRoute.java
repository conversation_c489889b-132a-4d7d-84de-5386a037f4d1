package de.systeex.cip.nevaris.adressen.json_producer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.adressen.json_producer.infrastructure.NevarisAdressIdToBrokerErrorMessageTransformer;
import de.systeex.cip.types.AdresseDebitorNrPayload;
import org.apache.camel.Predicate;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.List;
import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class AdressIdToBrokerRoute extends ErrorResistantRouteBuilder {

    private final Predicate addressFound = simple("${headers.ADDRESS_FOUND} == 'true'");

    public void configure() throws Exception {
        super.configure();
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat jacksonDataFormat = new JacksonDataFormat(mapper, AdresseDebitorNrPayload.class);

        from("direct:sendAdressNrBackToBroker")
                .routeId("sendAdressNrBackToBroker")
                .enrich("sql:SELECT adress_key, source_system_name, mandant, source_system_adress_id, source_system_debitor_id, debitor_key FROM mapping.adressen WHERE id = :#EXTERNE_ADRESS_NR" , (oldExchange, newExchange) -> {
                    List<Map<String, Object>> list = newExchange.getIn().getBody(List.class);
                    if (list.isEmpty()) {
                        oldExchange.getIn().setHeader("ADDRESS_FOUND", constant(false));
                    } else {
                        oldExchange.getIn().setHeader("ADDRESS_FOUND", constant(true));
                        oldExchange.getIn().setHeader("NEVARIS_ADRESS_NR", (list.get(0).get("adress_key")));
                        oldExchange.getIn().setHeader("SOURCE_SYSTEM_NAME", (list.get(0).get("source_system_name")));
                        oldExchange.getIn().setHeader("SOURCE_SYSTEM_ADRESS_ID",(list.get(0).get("source_system_adress_id")));
                        oldExchange.getIn().setHeader("SOURCE_SYSTEM_DEBITOR_ID", (list.get(0).get("source_system_debitor_id")));
                        oldExchange.getIn().setHeader("MANDANT", (list.get(0).get("mandant")));
                        oldExchange.getIn().setHeader("DEBITOR_KEY", (list.get(0).get("debitor_key")));
                    }
                    return oldExchange;
                })
                .choice()
                    .when(addressFound)
                        .to("direct:adressNrToBrokerRoute")
                    .otherwise()
                        .log("address with id ${headers.EXTERNE_ADRESS_NR} not found")
                    .endChoice()
                .end();

        from("direct:adressNrToBrokerRoute")
            .routeId("adressNrToBrokerRoute")
            .removeHeaders("Camel*", "MESSAGE_ID|DELIVERY_MODE")
            .process(exchange -> {
                String adressKey = exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class);
                String sourceSystemAdressId = exchange.getIn().getHeader("SOURCE_SYSTEM_ADRESS_ID", String.class);
                String sourceSystemDebitorId = exchange.getIn().getHeader("SOURCE_SYSTEM_DEBITOR_ID", String.class);
                String mandant = exchange.getIn().getHeader("MANDANT", String.class);
                String debitorKey = exchange.getIn().getHeader("DEBITOR_KEY", String.class);
                AdresseDebitorNrPayload adressNrPayload = new AdresseDebitorNrPayload(mandant, adressKey, debitorKey, sourceSystemAdressId, sourceSystemDebitorId);
                exchange.getIn().setBody(adressNrPayload);
            })
            .marshal(jacksonDataFormat)
            .log("send adressNr ${headers.NEVARIS_ADRESS_NR} to routingKey ${headers.MANDANT}, body: ${body}")
                .setHeader(DELIVERY_MODE, constant(2))
            .toD("rabbitmq:adressnr?exchangeType=topic&routingKey=${headers.MANDANT}&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisAdressIdToBrokerErrorMessageTransformer("AdressIdToBroker", "NEVARIS", "sendAdressNrBackToBroker", Map.of());
    }
}
