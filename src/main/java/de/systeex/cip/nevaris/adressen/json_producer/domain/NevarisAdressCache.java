package de.systeex.cip.nevaris.adressen.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatCache;

import javax.annotation.Nullable;
import java.util.List;

public record NevarisAdressCache(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonIgnore
        String odataEtag,
        @JsonProperty("Lfd_Nr")
        @JsonIgnore
        Integer lfdNr,
        @JsonProperty("Debitor")
        Boolean debitor,
        @JsonProperty("CompanyName")
        String companyName,
        @JsonProperty("Externe_Adress_Nr")
        Integer externeAdressNr,
        @JsonProperty("Interne_Adress_Nr")
        String interneAdressNr,
        @JsonProperty("DebitorNr")
        String debitorNr,
        @JsonProperty("Importiert")
        Boolean importiert,
        @JsonProperty("CallBackURL")
        String callBackURL,
        @JsonProperty("Name")
        String name,
        @JsonProperty("Suchbegriff")
        String suchbegriff,
        @JsonProperty("Adresse")
        String adresse,
        @JsonProperty("Adresse2")
        String adresse2,
        @JsonProperty("Adresse3")
        String adresse3,
        @JsonProperty("Strasse")
        String strasse,
        @JsonProperty("Laendercode")
        String laendercode,
        @JsonProperty("PLZ")
        String plz,
        @JsonProperty("Ort")
        String ort,
        @JsonProperty("PLZ_Postfach")
        String plzPostfach,
        @JsonProperty("Postfach_nutzen")
        Boolean postfachNutzen,
        @JsonProperty("Postfach")
        String postfach,
        @JsonProperty("Ort_Postfach")
        String ortPostfach,
        @JsonProperty("Bundesland")
        String bundesland,
        @JsonProperty("Sprache")
        String sprache,
        @JsonProperty("Auslandsvorwahl")
        String auslandsvorwahl,
        @JsonProperty("Ortskennzahl")
        String ortskennzahl,
        @JsonProperty("Hauptanschlussnr")
        String hauptanschlussnr,
        @JsonProperty("Durchwahl_Zentrale")
        String durchwahlZentrale,
        @JsonProperty("Durchwahl_Fax")
        String durchwahlFax,
        @JsonProperty("Durchwahl_Modem")
        String durchwahlModem,
        @JsonProperty("Telefonnr_kompl")
        String telefonnrKompl,
        @JsonProperty("Faxnr_kompl")
        String faxnrKompl,
        @JsonProperty("Modemnr_kompl")
        String modemnrKompl,
        @JsonProperty("Mail")
        String mail,
        @JsonProperty("WWW_Adresse")
        String wwwAdresse,
        @JsonProperty("Landkreis")
        String landkreis,
        @JsonProperty("Handelsregister")
        String handelsregister,
        @JsonProperty("USt_IdNr")
        String ustIdNr,
        @JsonProperty("Steuernummer_Gesellschaft")
        String steuernummerGesellschaft,
        @JsonProperty("Gesperrt")
        String gesperrt,
        @JsonProperty("Gueltig_ab")
        String gueltigAb,
        @JsonProperty("SystemId")
        @JsonIgnore
        String systemId,
        @JsonProperty("Debitorenbuchungsgruppe")
        String debitorenbuchungsgruppe,
        @JsonProperty("Mahnmethodencode")
        String mahnmethodencode,
        @JsonProperty("Zinskonditionencode")
        String zinskonditionencode,
        @JsonProperty("adressatCacheLp")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @Nullable
        List<NevarisAdressatCache> nevarisAdressatCaches
) {
    public static final String NULL_DATE = "0001-01-01";
}
