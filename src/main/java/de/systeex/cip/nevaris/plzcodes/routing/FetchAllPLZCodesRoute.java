package de.systeex.cip.nevaris.plzcodes.routing;

import de.systeex.cip.nevaris.infrastructure.NevarisConsumerAlreadySeenProcessor;
import de.systeex.cip.nevaris.infrastructure.NevarisPqSQLTimeStampProcessor;
import de.systeex.cip.nevaris.plzcodes.infrastructure.NevarisFetchAllPLZCodesErrorMessageTransformer;
import de.systeex.cip.nevaris.plzcodes.infrastructure.PollNevarisPLZCodesProcessor;
import de.systeex.cip.nevaris.plzcodes.infrastructure.TransformNevarisPLZCodeToPLZCodeProcessor;
import de.systeex.cip.nevaris.application.NevarisService;
import de.systeex.cip.types.PLZCode;
import org.apache.camel.ExchangePattern;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.apache.camel.spi.DataFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class FetchAllPLZCodesRoute extends ErrorResistantRouteBuilder {

    @Value("${poll.cron.plzcodes}")
    private String cronExpression;
    @Value("${nevaris.plzcodes.mandant}")
    private String mandant;

    private final Predicate notAlreadySeen = PredicateBuilder.isEqualTo(header("ALREADY_SEEN"), constant("false"));

    private final NevarisService nevarisService;

    @Autowired
    public FetchAllPLZCodesRoute(NevarisService nevarisService) {
        this.nevarisService = nevarisService;
    }

    @Override
    public void configure() throws Exception {
        super.configure();
        DataFormat plzCodeJacksonDataFormat = new JacksonDataFormat(PLZCode.class);
        NevarisConsumerAlreadySeenProcessor nevarisConsumerAlreadySeenProcessor = new NevarisConsumerAlreadySeenProcessor();

        from("quartz://nevarisPLZCodes?cron=" + cronExpression.replace(' ', '+'))
                .routeId("FetchAllPLZCodesRoute")
                .log("Fetching all PLZ codes")
                .setHeader("MANDANT", constant(""))
                .setHeader("NEVARIS_MANDANT", constant(""))
                .setHeader("NEVARIS_ENTITY", constant("PLZCode"))
                .to("direct:getLastTimestampFromDatabase")
                .process( new NevarisPqSQLTimeStampProcessor())
                .process(new PollNevarisPLZCodesProcessor(mandant, nevarisService))
                .split(body())
                    .enrich("direct:PlzRestCall",(oldExchange, newExchange) -> oldExchange )
                    .process(nevarisConsumerAlreadySeenProcessor)
                    .to("direct:saveLastTimestamp")
                    .log(LoggingLevel.INFO,"PLZCode: processed ${headers} ${body}")
                .end()
        ;


        from("direct:PlzRestCall")
                .id("direct:PlzRestCall")
                .process(new TransformNevarisPLZCodeToPLZCodeProcessor())
                .marshal(plzCodeJacksonDataFormat)
                .removeHeaders("*", "MESSAGE_ID|DELIVERY_MODE")
                .setExchangePattern(ExchangePattern.InOnly)
                .setHeader(DELIVERY_MODE, constant(2))
                .to("rabbitmq://plz-codes?exchangeType=topic&routingKey=" + mandant + "&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig")
        ;
    }
    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisFetchAllPLZCodesErrorMessageTransformer("PLZCode", "NEVARIS", "FetchAllPLZCodesRoute", Map.of());
    }

}
