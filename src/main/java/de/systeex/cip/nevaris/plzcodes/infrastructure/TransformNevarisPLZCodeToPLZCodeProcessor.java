package de.systeex.cip.nevaris.plzcodes.infrastructure;

import de.systeex.cip.nevaris.domain.odata.NevarisPLZCode;
import de.systeex.cip.types.PLZCode;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

public class TransformNevarisPLZCodeToPLZCodeProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        NevarisPLZCode nevarisPLZCode = exchange.getIn().getBody(NevarisPLZCode.class);

        PLZCode plzCode = new PLZCode(
                nevarisPLZCode.code(),
                nevarisPLZCode.city(),
                nevarisPLZCode.bundesland(),
                nevarisPLZCode.land(),
                nevarisPLZCode.ortLang()
        );

        exchange.getIn().setBody(plzCode);
    }
}
