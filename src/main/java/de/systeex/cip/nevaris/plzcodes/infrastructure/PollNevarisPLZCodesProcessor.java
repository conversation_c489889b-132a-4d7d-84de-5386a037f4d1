package de.systeex.cip.nevaris.plzcodes.infrastructure;

import de.systeex.cip.nevaris.domain.odata.NevarisPLZCode;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCodeList;
import de.systeex.cip.nevaris.application.NevarisService;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class PollNevarisPLZCodesProcessor implements Processor {

    private final String nevarisMandant;
    private final NevarisService nevarisService;

    public PollNevarisPLZCodesProcessor(String nevarisMandant, NevarisService nevarisService) {
        this.nevarisMandant = nevarisMandant;
        this.nevarisService = nevarisService;
    }

    @Override
    public void process(Exchange exchange) throws Exception {

        String queryString = "?$filter=Änderungsdatum ge " + exchange .getIn().getHeader("DATE_PART") + "&$orderby=Änderungsdatum asc,Änderungszeit asc,Code asc,City asc";
        NevarisPLZCodeList nevarisPLZCodeList = nevarisService.getPLZCodes(nevarisMandant, queryString);

        List<NevarisPLZCode> nevarisPLZCodes = nevarisPLZCodeList.values();

        while (nevarisPLZCodeList.oDataNextLink() != null) {
            queryString = URLDecoder.decode(nevarisPLZCodeList.oDataNextLink().substring(nevarisPLZCodeList.oDataNextLink().indexOf("?")), StandardCharsets.UTF_8);
            nevarisPLZCodeList = nevarisService.getPLZCodes(nevarisMandant, queryString);
            if(nevarisPLZCodeList != null){
                nevarisPLZCodes.addAll(nevarisPLZCodeList.values());
            }else {
                throw new Exception("Nevaris PLZ Code list is null: Query String: " + queryString);
            }
        }

        exchange.getIn().setBody(nevarisPLZCodes);
    }
}
