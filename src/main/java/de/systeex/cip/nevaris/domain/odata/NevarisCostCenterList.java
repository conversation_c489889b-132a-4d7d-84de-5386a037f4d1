package de.systeex.cip.nevaris.domain.odata;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

public class NevarisCostCenterList implements Serializable {
    @JsonProperty("@odata.context")
    private String oDataContext;
    @JsonProperty("@odata.nextLink")
    private String oDataNextLink;
    @JsonProperty("value")
    private List<NevarisCostCenter> values;

    public NevarisCostCenterList() {
    }

    public String getoDataContext() {
        return oDataContext;
    }


    public List<NevarisCostCenter> getValues() {
        return values;
    }

    public String getoDataNextLink() {
        return oDataNextLink;
    }
}
