package de.systeex.cip.nevaris.domain.odata;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.Objects;

public final class NevarisPLZCode extends NevarisOdataTimedClass {
    @JsonProperty("@odata.context")
    @JsonIgnore
    private final String odataContext;
    @JsonProperty("@odata.etag")
    @JsonIgnore
    private final String odataEtag;
    @JsonProperty("Code")
    @Nonnull
    private final String code;
    @JsonProperty("City")
    @Nonnull
    private final String city;
    @JsonProperty("Markierung")
    @Nullable
    private final Boolean markierung;
    @JsonProperty("Bundesland")
    @Nullable
    private final String bundesland;
    @JsonProperty("Land")
    @Nullable
    private final String land;
    @JsonProperty("Landkreis")
    @Nullable
    private final String landkreis;
    @JsonProperty("Ortsvorwahl")
    @Nullable
    private final String ortsvorwahl;
    @JsonProperty("Latitude")
    @Nullable
    private final BigDecimal latitude;
    @JsonProperty("Longitude")
    @Nullable
    private final BigDecimal longitude;
    @JsonProperty("Ort_lang")
    @Nullable
    private final String ortLang;
    @JsonProperty("Neuanlagesystem")
    @Nullable
    @JsonIgnore
    private final String neuanlagesystem;
    @JsonProperty("Neuanlagebenutzer")
    @Nullable
    @JsonIgnore
    private final String neuanlagebenutzer;
    @JsonProperty("Neuanlagedatum")
    @Nullable
    @JsonIgnore
    private final String neuanlagedatum;
    @JsonProperty("Neuanlagezeit")
    @Nullable
    @JsonIgnore
    private final String neuanlagezeit;
    @JsonProperty("Änderungssystem")
    @Nullable
    @JsonIgnore
    private final String aenderungssystem;
    @JsonProperty("Änderungsbenutzer")
    @Nullable
    @JsonIgnore
    private final String aenderungsbenutzer;
    @JsonProperty("Änderungsdatum")
    @Nullable
    @JsonIgnore
    private final String aenderungsdatum;
    @JsonProperty("Änderungszeit")
    @Nullable
    @JsonIgnore
    private final String aenderungszeit;

    public NevarisPLZCode(
//        <EntityType Name="PLZCodes">
//        <Key>
//        <PropertyRef Name="Code"/>
//        <PropertyRef Name="City"/>
//        </Key>
            @JsonProperty("@odata.context")
            String odataContext,
            @JsonProperty("@odata.etag")
            String odataEtag,
            @JsonProperty("Code")
            @Nonnull
            String code, //Type="Edm.String" Nullable="false" MaxLength="20">
            @JsonProperty("City")
            @Nonnull
            String city, //Type="Edm.String" Nullable="false" MaxLength="30">
            @JsonProperty("Markierung")
            @Nullable
            Boolean markierung, //Type="Edm.Boolean">
            @JsonProperty("Bundesland")
            @Nullable
            String bundesland, //Type="Edm.String" MaxLength="10">
            @JsonProperty("Land")
            @Nullable
            String land, //Type="Edm.String" MaxLength="10">
            @JsonProperty("Landkreis")
            @Nullable
            String landkreis, //Type="Edm.String" MaxLength="10">
            @JsonProperty("Ortsvorwahl")
            @Nullable
            String ortsvorwahl, //Type="Edm.String" MaxLength="20">
            @JsonProperty("Latitude")
            @Nullable
            BigDecimal latitude, //Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Longitude")
            @Nullable
            BigDecimal longitude, //Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Ort_lang")
            @Nullable
            String ortLang, //Type="Edm.String" MaxLength="70">
            @JsonProperty("Neuanlagesystem")
            @Nullable
            String neuanlagesystem, //Type="Edm.String" MaxLength="10">
            @JsonProperty("Neuanlagebenutzer")
            @Nullable
            String neuanlagebenutzer, //Type="Edm.String" MaxLength="50">
            @JsonProperty("Neuanlagedatum")
            @Nullable
            String neuanlagedatum, //Type="Edm.Date">
            @JsonProperty("Neuanlagezeit")
            @Nullable
            String neuanlagezeit, //Type="Edm.String">
            @JsonProperty("Änderungssystem")
            @Nullable
            String aenderungssystem, //Type="Edm.String" MaxLength="10">
            @JsonProperty("Änderungsbenutzer")
            @Nullable
            String aenderungsbenutzer, //Type="Edm.String" MaxLength="50">
            @JsonProperty("Änderungsdatum")
            @Nullable
            String aenderungsdatum, //Type="Edm.Date">
            @JsonProperty("Änderungszeit")
            @Nullable
            String aenderungszeit //Type="Edm.String">
//        <NavigationProperty Name="Bundesland_Link" Type="Collection(NAV.Bundeslaender)" ContainsTarget="true">
//        <ReferentialConstraint Property="Bundesland" ReferencedProperty="Code"/>
//        </NavigationProperty>
//        <NavigationProperty Name="Land_Link" Type="Collection(NAV.Laender)" ContainsTarget="true">
//        <ReferentialConstraint Property="Land" ReferencedProperty="Code"/>
//        </NavigationProperty>
//        <NavigationProperty Name="Landkreis_Link" Type="Collection(NAV.Landkreise)" ContainsTarget="true">
//        <ReferentialConstraint Property="Landkreis" ReferencedProperty="Landkreis"/>
//        </NavigationProperty>
//        <Annotation Term="NAV.LabelId" String="PLZCodes"/>
//        </EntityType>
    ) {
        this.odataContext = odataContext;
        this.odataEtag = odataEtag;
        this.code = code;
        this.city = city;
        this.markierung = markierung;
        this.bundesland = bundesland;
        this.land = land;
        this.landkreis = landkreis;
        this.ortsvorwahl = ortsvorwahl;
        this.latitude = latitude;
        this.longitude = longitude;
        this.ortLang = ortLang;
        this.neuanlagesystem = neuanlagesystem;
        this.neuanlagebenutzer = neuanlagebenutzer;
        this.neuanlagedatum = neuanlagedatum;
        this.neuanlagezeit = neuanlagezeit;
        this.aenderungssystem = aenderungssystem;
        this.aenderungsbenutzer = aenderungsbenutzer;
        this.aenderungsdatum = aenderungsdatum;
        this.aenderungszeit = aenderungszeit;
    }

    @JsonProperty("@odata.context")
    @JsonIgnore
    public String odataContext() {
        return odataContext;
    }

    @JsonProperty("@odata.etag")
    @JsonIgnore
    public String odataEtag() {
        return odataEtag;
    }

    @JsonProperty("Code")
    @Nonnull
    public String code() {
        return code;
    }

    @JsonProperty("City")
    @Nonnull
    public String city() {
        return city;
    }

    @JsonProperty("Markierung")
    @Nullable
    public Boolean markierung() {
        return markierung;
    }

    @JsonProperty("Bundesland")
    @Nullable
    public String bundesland() {
        return bundesland;
    }

    @JsonProperty("Land")
    @Nullable
    public String land() {
        return land;
    }

    @JsonProperty("Landkreis")
    @Nullable
    public String landkreis() {
        return landkreis;
    }

    @JsonProperty("Ortsvorwahl")
    @Nullable
    public String ortsvorwahl() {
        return ortsvorwahl;
    }

    @JsonProperty("Latitude")
    @Nullable
    public BigDecimal latitude() {
        return latitude;
    }

    @JsonProperty("Longitude")
    @Nullable
    public BigDecimal longitude() {
        return longitude;
    }

    @JsonProperty("Ort_lang")
    @Nullable
    public String ortLang() {
        return ortLang;
    }

    @JsonProperty("Neuanlagesystem")
    @Nullable
    @JsonIgnore
    public String neuanlagesystem() {
        return neuanlagesystem;
    }

    @JsonProperty("Neuanlagebenutzer")
    @Nullable
    @JsonIgnore
    public String neuanlagebenutzer() {
        return neuanlagebenutzer;
    }

    @JsonProperty("Neuanlagedatum")
    @Nullable
    @JsonIgnore
    public String neuanlagedatum() {
        return neuanlagedatum;
    }

    @JsonProperty("Neuanlagezeit")
    @Nullable
    @JsonIgnore
    public String neuanlagezeit() {
        return neuanlagezeit;
    }

    @JsonProperty("Änderungssystem")
    @Nullable
    @JsonIgnore
    public String aenderungssystem() {
        return aenderungssystem;
    }

    @JsonProperty("Änderungsbenutzer")
    @Nullable
    @JsonIgnore
    public String aenderungsbenutzer() {
        return aenderungsbenutzer;
    }

    @JsonProperty("Änderungsdatum")
    @Nullable
    @JsonIgnore
    public String aenderungsdatum() {
        return aenderungsdatum;
    }

    @JsonProperty("Änderungszeit")
    @Nullable
    @JsonIgnore
    public String aenderungszeit() {
        return aenderungszeit;
    }

    @Override
    @JsonIgnore
    public String getUUID() {
        return this.code + this.city;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (NevarisPLZCode) obj;
        return Objects.equals(this.odataContext, that.odataContext) &&
                Objects.equals(this.odataEtag, that.odataEtag) &&
                Objects.equals(this.code, that.code) &&
                Objects.equals(this.city, that.city) &&
                Objects.equals(this.markierung, that.markierung) &&
                Objects.equals(this.bundesland, that.bundesland) &&
                Objects.equals(this.land, that.land) &&
                Objects.equals(this.landkreis, that.landkreis) &&
                Objects.equals(this.ortsvorwahl, that.ortsvorwahl) &&
                Objects.equals(this.latitude, that.latitude) &&
                Objects.equals(this.longitude, that.longitude) &&
                Objects.equals(this.ortLang, that.ortLang) &&
                Objects.equals(this.neuanlagesystem, that.neuanlagesystem) &&
                Objects.equals(this.neuanlagebenutzer, that.neuanlagebenutzer) &&
                Objects.equals(this.neuanlagedatum, that.neuanlagedatum) &&
                Objects.equals(this.neuanlagezeit, that.neuanlagezeit) &&
                Objects.equals(this.aenderungssystem, that.aenderungssystem) &&
                Objects.equals(this.aenderungsbenutzer, that.aenderungsbenutzer) &&
                Objects.equals(this.aenderungsdatum, that.aenderungsdatum) &&
                Objects.equals(this.aenderungszeit, that.aenderungszeit);
    }

    @Override
    public int hashCode() {
        return Objects.hash(odataContext, odataEtag, code, city, markierung, bundesland, land, landkreis, ortsvorwahl, latitude, longitude, ortLang, neuanlagesystem, neuanlagebenutzer, neuanlagedatum, neuanlagezeit, aenderungssystem, aenderungsbenutzer, aenderungsdatum, aenderungszeit);
    }

    @Override
    public String toString() {
        return "NevarisPLZCode[" +
                "odataContext=" + odataContext + ", " +
                "odataEtag=" + odataEtag + ", " +
                "code=" + code + ", " +
                "city=" + city + ", " +
                "markierung=" + markierung + ", " +
                "bundesland=" + bundesland + ", " +
                "land=" + land + ", " +
                "landkreis=" + landkreis + ", " +
                "ortsvorwahl=" + ortsvorwahl + ", " +
                "latitude=" + latitude + ", " +
                "longitude=" + longitude + ", " +
                "ortLang=" + ortLang + ", " +
                "neuanlagesystem=" + neuanlagesystem + ", " +
                "neuanlagebenutzer=" + neuanlagebenutzer + ", " +
                "neuanlagedatum=" + neuanlagedatum + ", " +
                "neuanlagezeit=" + neuanlagezeit + ", " +
                "aenderungssystem=" + aenderungssystem + ", " +
                "aenderungsbenutzer=" + aenderungsbenutzer + ", " +
                "aenderungsdatum=" + aenderungsdatum + ", " +
                "aenderungszeit=" + aenderungszeit + ']';
    }

}
