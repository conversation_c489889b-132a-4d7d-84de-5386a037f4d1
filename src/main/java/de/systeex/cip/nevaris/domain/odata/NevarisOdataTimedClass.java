package de.systeex.cip.nevaris.domain.odata;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nullable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class NevarisOdataTimedClass {
    @JsonProperty("Änderungsdatum")
    @Nullable
    private final String aenderungsdatum;
    @JsonProperty("Änderungszeit")
    @Nullable
    private final String aenderungszeit;

    public NevarisOdataTimedClass(
            String aenderungsdatum, //Type=	"Edm.Date">
            String aenderungszeit //Type=	"Edm.String">
    ) {
        this.aenderungsdatum = aenderungsdatum;
        this.aenderungszeit = aenderungszeit;
    }

    public NevarisOdataTimedClass() {
        this.aenderungsdatum = null;
        this.aenderungszeit = null;
    }

    public String aenderungsdatum() {
        return aenderungsdatum;
    }

    public String aenderungszeit() {
        return aenderungszeit;
    }

    public abstract String getUUID();
}
