package de.systeex.cip.nevaris.domain.odata;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Objects;

public final class NevarisPLZCodeList{
    @JsonProperty("@odata.context")
    @JsonIgnore
    private final String oDataContext;
    @JsonProperty("@odata.nextLink")
    @JsonIgnore
    private final String oDataNextLink;
    @JsonProperty("value")
    private final List<NevarisPLZCode> values;

    public NevarisPLZCodeList(
            @JsonProperty("@odata.context")
            String oDataContext,

            @JsonProperty("@odata.nextLink")
            String oDataNextLink,

            @JsonProperty("value")
            List<NevarisPLZCode> values
    ) {
        this.oDataContext = oDataContext;
        this.oDataNextLink = oDataNextLink;
        this.values = values;
    }

    @JsonProperty("@odata.context")
    @JsonIgnore
    public String oDataContext() {
        return oDataContext;
    }

    @JsonProperty("@odata.nextLink")
    @JsonIgnore
    public String oDataNextLink() {
        return oDataNextLink;
    }

    @JsonProperty("value")
    public List<NevarisPLZCode> values() {
        return values;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (NevarisPLZCodeList) obj;
        return Objects.equals(this.oDataContext, that.oDataContext) &&
                Objects.equals(this.oDataNextLink, that.oDataNextLink) &&
                Objects.equals(this.values, that.values);
    }

    @Override
    public int hashCode() {
        return Objects.hash(oDataContext, oDataNextLink, values);
    }

    @Override
    public String toString() {
        return "NevarisPLZCodeList[" +
                "oDataContext=" + oDataContext + ", " +
                "oDataNextLink=" + oDataNextLink + ", " +
                "values=" + values + ']';
    }
}
