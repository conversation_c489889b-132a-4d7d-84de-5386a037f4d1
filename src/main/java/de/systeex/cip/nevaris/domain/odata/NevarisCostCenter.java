package de.systeex.cip.nevaris.domain.odata;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Locale;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT, pattern = "#,##")
public final class NevarisCostCenter extends NevarisOdataTimedClass {

    public static String NULL_DATE = "0001-01-01";
    @JsonProperty("@odata.context")
    @JsonIgnore
    private final String odataContext;
    @JsonProperty("@odata.etag")
    @JsonIgnore
    private final String odataEtag;
    @JsonProperty("Control2")
    @Nonnull
    private final String control2;
    @JsonProperty("Name")
    @Nullable
    private final String name;
    @JsonProperty("Bezeichnung2")
    @Nullable
    private final String bezeichnung2;
    @JsonProperty("TmpVorschauID")
    @Nullable
    @JsonIgnore
    private final String tmpVorschauID;
    @JsonProperty("Bemerkung")
    @Nullable
    @JsonIgnore
    private final Boolean bemerkung;
    @JsonProperty("Suchbegriff")
    @Nullable
    private final String suchbegriff;
    @JsonProperty("Auftragswert")
    @Nullable
    private final Double auftragswert;
    @JsonProperty("Nachtragswert")
    @Nullable
    @JsonIgnore
    private final String nachtragswert;
    @JsonProperty("Konsolidierungscode")
    @Nullable
    private final String konsolidierungscode;
    @JsonProperty("Gemeindeschlüssel_AGS")
    @Nullable
    @JsonIgnore
    private final String gemeindeschluesselAGS;
    @JsonProperty("Bezeichnung_AGS")
    @Nullable
    private final String bezeichnungAGS;
    @JsonProperty("Ist_Baustelle")
    @Nullable
    private final Boolean istBaustelle;
    @JsonProperty("Adresse_KSt_Ort")
    @Nullable
    @JsonIgnore
    private final String adresseKStOrt;
    @JsonProperty("Adresse_KSt_PLZ")
    @Nullable
    @JsonIgnore
    private final String adresseKStPLZ;
    @JsonProperty("Adresse_KSt_Bundesland")
    @Nullable
    @JsonIgnore
    private final String adresseKStBundesland;
    @JsonProperty("Adresse_KSt_Ländercode")
    @Nullable
    @JsonIgnore
    private final String adresseKStLaendercode;
    @JsonProperty("Text_Bundesland")
    @Nullable
    @JsonIgnore
    private final String textBundesland;
    @JsonProperty("BAB_Zeilendefinition")
    @Nullable
    private final String babZeilendefinition;
    @JsonProperty("Angebotsprojekt")
    @Nullable
    private final String angebotsprojekt;
    @JsonProperty("Auftragsprojekt")
    @Nullable
    private final String auftragsprojekt;
    @JsonProperty("Auftragseingang")
    @Nullable
    private final String auftragseingang;
    @JsonProperty("Adressnr_der_Kostenstelle")
    @Nullable
    private final String adressnrDerKostenstelle;
    @JsonProperty("Adressnr_des_Auftraggebers")
    @Nullable
    private final String adressnrDesAuftraggebers;
    @JsonProperty("Debitor")
    @Nullable
    private final String debitor;
    @JsonProperty("Kundennr_beim_Auftraggeber")
    @Nullable
    private final String kundennrBeimAuftraggeber;
    @JsonProperty("Empfangsstelle")
    @Nullable
    private final String empfangsstelle;
    @JsonProperty("Bauleiter")
    @Nullable
    private final String bauleiter;
    @JsonProperty("Bauleitergruppe")
    @Nullable
    private final String bauleitergruppe;
    @JsonProperty("Oberbauleiter")
    @Nullable
    private final String oberbauleiter;
    @JsonProperty("Adressnr_Bauleiter_Bauherrn")
    @Nullable
    private final String adressnrBauleiterBauherrn;
    @JsonProperty("Adressat_Bauleiter_Bauherrn")
    @Nullable
    private final String adressatBauleiterBauherrn;
    @JsonProperty("Adressnr_des_Bauherrn")
    @Nullable
    private final String adressnrDesBauherrn;
    @JsonProperty("Adressat_des_Bauherrn")
    @Nullable
    private final String adressatDesBauherrn;
    @JsonProperty("Adressnr_des_Architekten")
    @Nullable
    private final String adressnrDesArchitekten;
    @JsonProperty("Adressat_des_Architekten")
    @Nullable
    private final String adressatDesArchitekten;
    @JsonProperty("Adressnr_Polier")
    @Nullable
    private final String adressnrPolier;
    @JsonProperty("Adressat_Polier")
    @Nullable
    private final String adressatPolier;
    @JsonProperty("Vertragsart")
    @Nullable
    private final String vertragsart;
    @JsonProperty("Gewährleistung_in_Monaten")
    @Nullable
    private final Integer gewaehrleistungInMonaten;
    @JsonProperty("Bürgschaftsbetrag")
    @Nullable
    private final String buergschaftsbetrag;
    @JsonProperty("Vertragsbed_Vorspann")
    @Nullable
    private final Integer vertragsbedVorspann;
    @JsonProperty("Vertragsbed_Nachspann")
    @Nullable
    private final Integer vertragsbedNachspann;
    @JsonProperty("Vertragsbed_Erläuterung")
    @Nullable
    private final Integer vertragsbedErlaeuterung;
    @JsonProperty("Zahlungsbedingung")
    @Nullable
    private final String zahlungsbedingung;
    @JsonProperty("Nachlässe")
    @Nullable
    private final String nachlaesse;
    @JsonProperty("Niederlassung")
    @Nullable
    private final String niederlassung;
    @JsonProperty("Ergebniszuordnung")
    @Nullable
    private final String ergebniszuordnung;
    @JsonProperty("Sparte")
    @Nullable
    private final String sparte;
    @JsonProperty("Typ")
    @Nullable
    private final String typ;
    @JsonProperty("Gruppe")
    @Nullable
    private final String gruppe;
    @JsonProperty("Gruppenstufe")
    @Nullable
    private final String gruppenstufe;
    @JsonProperty("Gruppenstufe2")
    @Nullable
    private final String gruppenstufe2;
    @JsonProperty("Gruppenstufe3")
    @Nullable
    private final String gruppenstufe3;
    @JsonProperty("Gruppenstufe4")
    @Nullable
    private final String gruppenstufe4;
    @JsonProperty("Gruppenstufe5")
    @Nullable
    private final String gruppenstufe5;
    @JsonProperty("Statistikgruppe")
    @Nullable
    private final String statistikgruppe;
    @JsonProperty("U_Bahn_Steuerpflicht_AT")
    @Nullable
    private final Boolean uBahnSteuerpflichtAT;
    @JsonProperty("Gemeindecode_Kommunalsteuer_AT")
    @Nullable
    private final String gemeindecodeKommunalsteuerAT;
    @JsonProperty("Stammkostenstelle_für")
    @Nullable
    private final String stammkostenstelleFuer;
    @JsonProperty("Zusammenzählungsart")
    @Nullable
    private final String zusammenzaehlungsart;
    @JsonProperty("Zusammenzählung")
    @Nullable
    private final String zusammenzaehlung;
    @JsonProperty("MIS_ID")
    @Nullable
    private final String misId;
    @JsonProperty("Baubeginndatum")
    @Nullable
    private final String baubeginndatum;
    @JsonProperty("Vorauss_Bauendedatum")
    @Nullable
    private final String voraussBauendedatum;
    @JsonProperty("Bauendedatum")
    @Nullable
    private final String bauendedatum;
    @JsonProperty("Bauabnahmedatum")
    @Nullable
    private final String bauabnahmedatum;
    @JsonProperty("Bauzeit_in_Monaten")
    @Nullable
    private final Integer bauzeitInMonaten;
    @JsonProperty("Drucken")
    @Nullable
    private final Boolean drucken;
    @JsonProperty("Drucken_nur_wenn_Bewegung")
    @Nullable
    private final Boolean druckenNurWennBewegung;
    @JsonProperty("Drucken_nach_Bauende")
    @Nullable
    private final Boolean druckenNachBauende;
    @JsonProperty("Auf_Ergebnislisten_drucken")
    @Nullable
    private final Boolean aufErgebnislistenDrucken;
    @JsonProperty("Vorjahreswert_ermitteln")
    @Nullable
    private final Boolean vorjahreswertErmitteln;
    @JsonProperty("Gesamtwert_ermitteln")
    @Nullable
    private final Boolean gesamtwertErmitteln;
    @JsonProperty("Umlage_nach_Bauende")
    @Nullable
    private final Boolean umlageNachBauende;
    @JsonProperty("Ist_Bauträger")
    @Nullable
    private final Boolean istBautraeger;
    @JsonProperty("Schlußgerechnet")
    @Nullable
    private final Boolean schlussgerechnet;
    @JsonProperty("Datum_Schlußgerechnet")
    @Nullable
    private final String datumSchlussgerechnet;
    @JsonProperty("Abgerechnet_Percent")
    @Nullable
    private final String abgerechnetPercent;
    @JsonProperty("Fertigstellung_Percent")
    @Nullable
    private final String fertigstellungPercent;
    @JsonProperty("Datum_Fertigstellung")
    @Nullable
    private final String datumFertigstellung;
    @JsonProperty("Fertig")
    @Nullable
    private final Boolean fertig;
    @JsonProperty("MwSt_Satz")
    @Nullable
    private final String mwStSatz;
    @JsonProperty("Buchen_mit_BAS")
    @Nullable
    private final String buchenMitBAS;
    @JsonProperty("Buchen_mit_Gerät")
    @Nullable
    private final String buchenMitGeraet;
    @JsonProperty("Gerätekondition_intern")
    @Nullable
    private final String geraetekonditionIntern;
    @JsonProperty("Gerätekondition_extern")
    @Nullable
    private final String geraetekonditionExtern;
    @JsonProperty("Gerätekondition_int_Leistung")
    @Nullable
    private final String geraetekonditionIntLeistung;
    @JsonProperty("Gerätekondition_ext_Leistung")
    @Nullable
    private final String geraetekonditionExtLeistung;
    @JsonProperty("BAL_alt_intern")
    @Nullable
    private final Boolean balAltIntern;
    @JsonProperty("BAL_alt_extern")
    @Nullable
    private final Boolean balAltExtern;
    @JsonProperty("BAL_Percent_neu_intern")
    @Nullable
    private final String balPercentNeuIntern;
    @JsonProperty("BAL_Percent_neu_extern")
    @Nullable
    private final String balPercentNeuExtern;
    @JsonProperty("BAL_Percent_neu_Rücknahme_intern")
    @Nullable
    private final String balPercentNeuRuecknahmeIntern;
    @JsonProperty("BAL_Percent_neu_Rücknahme_extern")
    @Nullable
    private final String balPercentNeuRuecknahmeExtern;
    @JsonProperty("BAL_Percent_gebraucht_intern")
    @Nullable
    private final String balPercentGebrauchtIntern;
    @JsonProperty("BAL_Percent_gebraucht_extern")
    @Nullable
    private final String balPercentGebrauchtExtern;
    @JsonProperty("BAL_Percent_gebraucht_Rückn_intern")
    @Nullable
    private final String balPercentGebrauchtRuecknIntern;
    @JsonProperty("BAL_Percent_gebraucht_Rückn_extern")
    @Nullable
    private final String balPercentGebrauchtRuecknExtern;
    @JsonProperty("BAL_Percent_Ladepauschale_intern")
    @Nullable
    private final String balPercentLadepauschaleIntern;
    @JsonProperty("BAL_Percent_Ladepauschale_extern")
    @Nullable
    private final String balPercentLadepauschaleExtern;
    @JsonProperty("ARGE_Kostenstelle")
    @Nullable
    private final String argeKostenstelle;
    @JsonProperty("Fremdunternehmen")
    @Nullable
    private final String fremdunternehmen;
    @JsonProperty("Bauhof")
    @Nullable
    private final String bauhof;
    @JsonProperty("Gerätebestand")
    @Nullable
    private final String geraetebestand;
    @JsonProperty("Höhenmeter")
    @Nullable
    private final String hoehenmeter;
    @JsonProperty("CUP")
    @Nullable
    private final String cup;
    @JsonProperty("CIG")
    @Nullable
    private final String cig;
    @JsonProperty("Kostenart_mit_Menge")
    @Nullable
    private final String kostenartMitMenge;
    @JsonProperty("DB_III_Percent_Prognose")
    @Nullable
    private final String dbIIIPercentPrognose;
    @JsonProperty("Einfache_Fahrtzeit")
    @Nullable
    private final String einfacheFahrtzeit;
    @JsonProperty("ARG_pflichtig")
    @Nullable
    private final String argPflichtig;
    @JsonProperty("WWW_Adresse_URL")
    @Nullable
    private final String wwwAdresseURL;
    @JsonProperty("EasyRapport")
    @Nullable
    private final String easyRapport;
    @JsonProperty("Externe_Geräteergebnisse")
    @Nullable
    private final Boolean externeGeraeteergebnisse;
    @JsonProperty("ZHS_Loggerbezeichnung_1")
    @Nullable
    private final String zhsLoggerbezeichnung1;
    @JsonProperty("ZHS_Loggerbezeichnung_2")
    @Nullable
    private final String zhsLoggerbezeichnung2;
    @JsonProperty("ZHS_Aktiv_auf_Logger")
    @Nullable
    private final String zhsAktivAufLogger;
    @JsonProperty("ZHS_Kostenstellengruppe")
    @Nullable
    private final String zhsKostenstellengruppe;
    @JsonProperty("ZHS_Unterkostenstellen")
    @Nullable
    private final String zhsUnterkostenstellen;
    @JsonProperty("ZHS_Lohnarten")
    @Nullable
    private final String zhsLohnarten;
    @JsonProperty("ZHS_GEO_Status")
    @Nullable
    private final String zhsGEO_Status;
    @JsonProperty("ZHS_GEO_Position")
    @Nullable
    private final String zhsGEOPosition;
    @JsonProperty("ZHS_StdUKST")
    @Nullable
    private final String zhsStdUKST;
    @JsonProperty("Gesperrt")
    @Nullable
    private final String gesperrt;
    @JsonProperty("Sperrhinweis")
    @Nullable
    private final String sperrhinweis;
    @JsonProperty("Gültig_ab")
    @Nullable
    private final String gueltigAb;
    @JsonProperty("Gültig_bis")
    @Nullable
    private final String gueltigBis;
    @JsonProperty("Neuanlagesystem")
    @Nullable
    @JsonIgnore
    private final String neuanlagesystem;
    @JsonProperty("Neuanlagebenutzer")
    @Nullable
    @JsonIgnore
    private final String neuanlagebenutzer;
    @JsonProperty("Neuanlagedatum")
    @Nullable
    @JsonIgnore
    private final String neuanlagedatum;
    @JsonProperty("Neuanlagezeit")
    @Nullable
    @JsonIgnore
    private final String neuanlagezeit;
    @JsonProperty("Änderungssystem")
    @Nullable
    @JsonIgnore
    private final String aenderungssystem;
    @JsonProperty("Änderungsbenutzer")
    @Nullable
    @JsonIgnore
    private final String aenderungsbenutzer;
    @JsonProperty("Änderungsdatum")
    @Nullable
    @JsonIgnore
    private final String aenderungsdatum;
    @JsonProperty("Änderungszeit")
    @Nullable
    @JsonIgnore
    private final String aenderungszeit;
    @JsonProperty("Mandantenfilter")
    @Nullable
    private final String mandantenfilter;
    @JsonProperty("Auftragsperiodenfilter")
    @Nullable
    private final String auftragsperiodenfilter;

    public NevarisCostCenter(
//        <Key>
//        <PropertyRef Name="Control2"/>
//        </Key>
            @JsonProperty("@odata.context")
            String odataContext, //
            @JsonProperty("@odata.etag")
            String odataEtag,
            @JsonProperty("Control2")
            @Nonnull
            String control2, //  Type="Edm.String" Nullable="false" MaxLength="20">!!!
            @JsonProperty("Name")
            @Nullable
            String name, //  Type="Edm.String" MaxLength="50">
            @JsonProperty("Bezeichnung2")
            @Nullable
            String bezeichnung2, //  Type="Edm.String" MaxLength="50">
            @JsonProperty("TmpVorschauID")
            @Nullable
            String tmpVorschauID, //  Type="Edm.String">
            @JsonProperty("Bemerkung")
            @Nullable
            Boolean bemerkung, //  Type="Edm.Boolean">
            @JsonProperty("Suchbegriff")
            @Nullable
            String suchbegriff, //  Type="Edm.String" MaxLength="30">
            @JsonProperty("Auftragswert")
            @Nullable
            Double auftragswert, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Nachtragswert")
            @Nullable
            String nachtragswert, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Konsolidierungscode")
            @Nullable
            String konsolidierungscode, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gemeindeschlüssel_AGS")
            @Nullable
            String gemeindeschluesselAGS, //  Type="Edm.String" MaxLength="30">
            @JsonProperty("Bezeichnung_AGS")
            @Nullable
            String bezeichnungAGS, //  Type="Edm.String">
            @JsonProperty("Ist_Baustelle")
            @Nullable
            Boolean istBaustelle, //  Type="Edm.Boolean">
            @JsonProperty("Adresse_KSt_Ort")
            @Nullable
            String adresseKStOrt, //  Type="Edm.String" MaxLength="70">
            @JsonProperty("Adresse_KSt_PLZ")
            @Nullable
            String adresseKStPLZ, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Adresse_KSt_Bundesland")
            @Nullable
            String adresseKStBundesland, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adresse_KSt_Ländercode")
            @Nullable
            String adresseKStLaendercode, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Text_Bundesland")
            @Nullable
            String textBundesland, //  Type="Edm.String" MaxLength="50">
            @JsonProperty("BAB_Zeilendefinition")
            @Nullable
            String babZeilendefinition, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Angebotsprojekt")
            @Nullable
            String angebotsprojekt, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Auftragsprojekt")
            @Nullable
            String auftragsprojekt, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Auftragseingang")
            @Nullable
            String auftragseingang, //  Type="Edm.Date">
            @JsonProperty("Adressnr_der_Kostenstelle")
            @Nullable
            String adressnrDerKostenstelle, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressnr_des_Auftraggebers")
            @Nullable
            String adressnrDesAuftraggebers, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Debitor")
            @Nullable
            String debitor, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Kundennr_beim_Auftraggeber")
            @Nullable
            String kundennrBeimAuftraggeber, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Empfangsstelle")
            @Nullable
            String empfangsstelle, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Bauleiter")
            @Nullable
            String bauleiter, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Bauleitergruppe")
            @Nullable
            String bauleitergruppe, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Oberbauleiter")
            @Nullable
            String oberbauleiter, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressnr_Bauleiter_Bauherrn")
            @Nullable
            String adressnrBauleiterBauherrn, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressat_Bauleiter_Bauherrn")
            @Nullable
            String adressatBauleiterBauherrn, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressnr_des_Bauherrn")
            @Nullable
            String adressnrDesBauherrn, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressat_des_Bauherrn")
            @Nullable
            String adressatDesBauherrn, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressnr_des_Architekten")
            @Nullable
            String adressnrDesArchitekten, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressat_des_Architekten")
            @Nullable
            String adressatDesArchitekten, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressnr_Polier")
            @Nullable
            String adressnrPolier, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Adressat_Polier")
            @Nullable
            String adressatPolier, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Vertragsart")
            @Nullable
            String vertragsart, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gewährleistung_in_Monaten")
            @Nullable
            Integer gewaehrleistungInMonaten, //  Type="Edm.Int32">
            @JsonProperty("Bürgschaftsbetrag")
            @Nullable
            String buergschaftsbetrag, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Vertragsbed_Vorspann")
            @Nullable
            Integer vertragsbedVorspann, //  Type="Edm.Int32">
            @JsonProperty("Vertragsbed_Nachspann")
            @Nullable
            Integer vertragsbedNachspann, //  Type="Edm.Int32">
            @JsonProperty("Vertragsbed_Erläuterung")
            @Nullable
            Integer vertragsbedErlaeuterung, //  Type="Edm.Int32">
            @JsonProperty("Zahlungsbedingung")
            @Nullable
            String zahlungsbedingung, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Nachlässe")
            @Nullable
            String nachlaesse, //  Type="Edm.Boolean">
            @JsonProperty("Niederlassung")
            @Nullable
            String niederlassung, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Ergebniszuordnung")
            @Nullable
            String ergebniszuordnung, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Sparte")
            @Nullable
            String sparte, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Typ")
            @Nullable
            String typ, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gruppe")
            @Nullable
            String gruppe, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe")
            @Nullable
            String gruppenstufe, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe2")
            @Nullable
            String gruppenstufe2, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe3")
            @Nullable
            String gruppenstufe3, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe4")
            @Nullable
            String gruppenstufe4, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe5")
            @Nullable
            String gruppenstufe5, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Statistikgruppe")
            @Nullable
            String statistikgruppe, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("U_Bahn_Steuerpflicht_AT")
            @Nullable
            Boolean uBahnSteuerpflichtAT, //  Type="Edm.Boolean">
            @JsonProperty("Gemeindecode_Kommunalsteuer_AT")
            @Nullable
            String gemeindecodeKommunalsteuerAT, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Stammkostenstelle_für")
            @Nullable
            String stammkostenstelleFuer, //  Type="Edm.String">
            @JsonProperty("Zusammenzählungsart")
            @Nullable
            String zusammenzaehlungsart, //  Type="Edm.String">
            @JsonProperty("Zusammenzählung")
            @Nullable
            String zusammenzaehlung, //  Type="Edm.String" MaxLength="200">
            @JsonProperty("MIS_ID")
            @Nullable
            String misId, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Baubeginndatum")
            @Nullable
            String baubeginndatum, //  Type="Edm.Date">
            @JsonProperty("Vorauss_Bauendedatum")
            @Nullable
            String voraussBauendedatum, //  Type="Edm.Date">
            @JsonProperty("Bauendedatum")
            @Nullable
            String bauendedatum, //  Type="Edm.Date">
            @JsonProperty("Bauabnahmedatum")
            @Nullable
            String bauabnahmedatum, //  Type="Edm.Date">
            @JsonProperty("Bauzeit_in_Monaten")
            @Nullable
            Integer bauzeitInMonaten, //  Type="Edm.Int32">
            @JsonProperty("Drucken")
            @Nullable
            Boolean drucken, //  Type="Edm.Boolean">
            @JsonProperty("Drucken_nur_wenn_Bewegung")
            @Nullable
            Boolean druckenNurWennBewegung, //  Type="Edm.Boolean">
            @JsonProperty("Drucken_nach_Bauende")
            @Nullable
            Boolean druckenNachBauende, //  Type="Edm.Boolean">
            @JsonProperty("Auf_Ergebnislisten_drucken")
            @Nullable
            Boolean aufErgebnislistenDrucken, //  Type="Edm.Boolean">
            @JsonProperty("Vorjahreswert_ermitteln")
            @Nullable
            Boolean vorjahreswertErmitteln, //  Type="Edm.Boolean">
            @JsonProperty("Gesamtwert_ermitteln")
            @Nullable
            Boolean gesamtwertErmitteln, //  Type="Edm.Boolean">
            @JsonProperty("Umlage_nach_Bauende")
            @Nullable
            Boolean umlageNachBauende, //  Type="Edm.Boolean">
            @JsonProperty("Ist_Bauträger")
            @Nullable
            Boolean istBautraeger, //  Type="Edm.Boolean">
            @JsonProperty("Schlußgerechnet")
            @Nullable
            Boolean schlussgerechnet, //  Type="Edm.Boolean">
            @JsonProperty("Datum_Schlußgerechnet")
            @Nullable
            String datumSchlussgerechnet, //  Type="Edm.Date">
            @JsonProperty("Abgerechnet_Percent")
            @Nullable
            String abgerechnetPercent, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Fertigstellung_Percent")
            @Nullable
            String fertigstellungPercent, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Datum_Fertigstellung")
            @Nullable
            String datumFertigstellung, //  Type="Edm.Date">
            @JsonProperty("Fertig")
            @Nullable
            Boolean fertig, //  Type="Edm.Boolean">
            @JsonProperty("MwSt_Satz")
            @Nullable
            String mwStSatz, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Buchen_mit_BAS")
            @Nullable
            String buchenMitBAS, //  Type="Edm.String">
            @JsonProperty("Buchen_mit_Gerät")
            @Nullable
            String buchenMitGeraet, //  Type="Edm.String">
            @JsonProperty("Gerätekondition_intern")
            @Nullable
            String geraetekonditionIntern, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Gerätekondition_extern")
            @Nullable
            String geraetekonditionExtern, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Gerätekondition_int_Leistung")
            @Nullable
            String geraetekonditionIntLeistung, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Gerätekondition_ext_Leistung")
            @Nullable
            String geraetekonditionExtLeistung, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("BAL_alt_intern")
            @Nullable
            Boolean balAltIntern, //  Type="Edm.Boolean">
            @JsonProperty("BAL_alt_extern")
            @Nullable
            Boolean balAltExtern, //  Type="Edm.Boolean">
            @JsonProperty("BAL_Percent_neu_intern")
            @Nullable
            String balPercentNeuIntern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_neu_extern")
            @Nullable
            String balPercentNeuExtern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_neu_Rücknahme_intern")
            @Nullable
            String balPercentNeuRuecknahmeIntern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_neu_Rücknahme_extern")
            @Nullable
            String balPercentNeuRuecknahmeExtern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_gebraucht_intern")
            @Nullable
            String balPercentGebrauchtIntern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_gebraucht_extern")
            @Nullable
            String balPercentGebrauchtExtern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_gebraucht_Rückn_intern")
            @Nullable
            String balPercentGebrauchtRuecknIntern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_gebraucht_Rückn_extern")
            @Nullable
            String balPercentGebrauchtRuecknExtern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_Ladepauschale_intern")
            @Nullable
            String balPercentLadepauschaleIntern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("BAL_Percent_Ladepauschale_extern")
            @Nullable
            String balPercentLadepauschaleExtern, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("ARGE_Kostenstelle")
            @Nullable
            String argeKostenstelle, //  Type="Edm.Boolean">
            @JsonProperty("Fremdunternehmen")
            @Nullable
            String fremdunternehmen, //  Type="Edm.Boolean">
            @JsonProperty("Bauhof")
            @Nullable
            String bauhof, //  Type="Edm.Boolean">
            @JsonProperty("Gerätebestand")
            @Nullable
            String geraetebestand, //  Type="Edm.Boolean">
            @JsonProperty("Höhenmeter")
            @Nullable
            String hoehenmeter, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("CUP")
            @Nullable
            String cup, //  Type="Edm.String" MaxLength="15">
            @JsonProperty("CIG")
            @Nullable
            String cig, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Kostenart_mit_Menge")
            @Nullable
            String kostenartMitMenge, //  Type="Edm.String">
            @JsonProperty("DB_III_Percent_Prognose")
            @Nullable
            String dbIIIPercentPrognose, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Einfache_Fahrtzeit")
            @Nullable
            String einfacheFahrtzeit, //  Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("ARG_pflichtig")
            @Nullable
            String argPflichtig, //  Type="Edm.Boolean">
            @JsonProperty("WWW_Adresse_URL")
            @Nullable
            String wwwAdresseURL, //  Type="Edm.String" MaxLength="250">
            @JsonProperty("EasyRapport")
            @Nullable
            String easyRapport, //  Type="Edm.String">
            @JsonProperty("Externe_Geräteergebnisse")
            @Nullable
            Boolean externeGeraeteergebnisse, //  Type="Edm.Boolean">
            @JsonProperty("ZHS_Loggerbezeichnung_1")
            @Nullable
            String zhsLoggerbezeichnung1, //  Type="Edm.String" MaxLength="12">
            @JsonProperty("ZHS_Loggerbezeichnung_2")
            @Nullable
            String zhsLoggerbezeichnung2, //  Type="Edm.String" MaxLength="12">
            @JsonProperty("ZHS_Aktiv_auf_Logger")
            @Nullable
            String zhsAktivAufLogger, //  Type="Edm.Boolean">
            @JsonProperty("ZHS_Kostenstellengruppe")
            @Nullable
            String zhsKostenstellengruppe, //  Type="Edm.String" MaxLength="12">
            @JsonProperty("ZHS_Unterkostenstellen")
            @Nullable
            String zhsUnterkostenstellen, //  Type="Edm.String">
            @JsonProperty("ZHS_Lohnarten")
            @Nullable
            String zhsLohnarten, //  Type="Edm.String">
            @JsonProperty("ZHS_GEO_Status")
            @Nullable
            String zhsGEO_Status, //  Type="Edm.String">
            @JsonProperty("ZHS_GEO_Position")
            @Nullable
            String zhsGEOPosition, //  Type="Edm.String" MaxLength="30">
            @JsonProperty("ZHS_StdUKST")
            @Nullable
            String zhsStdUKST, //  Type="Edm.String" MaxLength="12">
            @JsonProperty("Gesperrt")
            @Nullable
            String gesperrt, //  Type="Edm.String">
            @JsonProperty("Sperrhinweis")
            @Nullable
            String sperrhinweis, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Gültig_ab")
            @Nullable
            String gueltigAb, //  Type="Edm.Date">
            @JsonProperty("Gültig_bis")
            @Nullable
            String gueltigBis, //  Type="Edm.Date">
            @JsonProperty("Neuanlagesystem")
            @Nullable
            String neuanlagesystem, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Neuanlagebenutzer")
            @Nullable
            String neuanlagebenutzer, //  Type="Edm.String" MaxLength="50">
            @JsonProperty("Neuanlagedatum")
            @Nullable
            String neuanlagedatum, //  Type="Edm.Date">
            @JsonProperty("Neuanlagezeit")
            @Nullable
            String neuanlagezeit, //  Type="Edm.String">
            @JsonProperty("Änderungssystem")
            @Nullable
            String aenderungssystem, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Änderungsbenutzer")
            @Nullable
            String aenderungsbenutzer, //  Type="Edm.String" MaxLength="50">
            @JsonProperty("Änderungsdatum")
            @Nullable
            String aenderungsdatum, //  Type="Edm.Date">
            @JsonProperty("Änderungszeit")
            @Nullable
            String aenderungszeit, //  Type="Edm.String">
            @JsonProperty("Mandantenfilter")
            @Nullable
            String mandantenfilter, //  Type="Edm.String" MaxLength="30">
            @JsonProperty("Auftragsperiodenfilter")
            @Nullable
            String auftragsperiodenfilter //  Type="Edm.String">

//    <NavigationProperty Name="Adressnr_der_Kostenstelle_Link" Type="Collection(NAV.adressuebersicht)"  Type="Edm.String">
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressnr_der_Kostenstelle" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressnr_des_Auftraggebers_Link" Type="Collection(NAV.adressuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressnr_des_Auftraggebers" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Debitor_Link" Type="Collection(NAV.debitorenuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Debitor" ReferencedProperty="No"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressnr_Bauleiter_Bauherrn_Link" Type="Collection(NAV.adressuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressnr_Bauleiter_Bauherrn" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressat_Bauleiter_Bauherrn_Link" Type="Collection(NAV.adressatenuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressat_Bauleiter_Bauherrn" ReferencedProperty="Adressat"/>
//            <ReferentialConstraint Property="Adressnr_Bauleiter_Bauherrn" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressnr_des_Bauherrn_Link" Type="Collection(NAV.adressuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressnr_des_Bauherrn" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressat_des_Bauherrn_Link" Type="Collection(NAV.adressatenuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressat_des_Bauherrn" ReferencedProperty="Adressat"/>
//            <ReferentialConstraint Property="Adressnr_des_Bauherrn" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressnr_des_Architekten_Link" Type="Collection(NAV.adressuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressnr_des_Architekten" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressat_des_Architekten_Link" Type="Collection(NAV.adressatenuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressat_des_Architekten" ReferencedProperty="Adressat"/>
//            <ReferentialConstraint Property="Adressnr_des_Architekten" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressnr_Polier_Link" Type="Collection(NAV.adressuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressnr_Polier" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <NavigationProperty Name="Adressat_Polier_Link" Type="Collection(NAV.adressatenuebersicht)"
//            ContainsTarget="true">
//            <ReferentialConstraint Property="Adressat_Polier" ReferencedProperty="Adressat"/>
//            <ReferentialConstraint Property="Adressnr_Polier" ReferencedProperty="Adressnr"/>
//            </NavigationProperty>
//            <Annotation Term="NAV.LabelId" String="Kostenstellenliste"/>
//            </EntityType>
    ) {
        this.odataContext = odataContext;
        this.odataEtag = odataEtag;
        this.control2 = control2;
        this.name = name;
        this.bezeichnung2 = bezeichnung2;
        this.tmpVorschauID = tmpVorschauID;
        this.bemerkung = bemerkung;
        this.suchbegriff = suchbegriff;
        this.auftragswert = auftragswert;
        this.nachtragswert = nachtragswert;
        this.konsolidierungscode = konsolidierungscode;
        this.gemeindeschluesselAGS = gemeindeschluesselAGS;
        this.bezeichnungAGS = bezeichnungAGS;
        this.istBaustelle = istBaustelle;
        this.adresseKStOrt = adresseKStOrt;
        this.adresseKStPLZ = adresseKStPLZ;
        this.adresseKStBundesland = adresseKStBundesland;
        this.adresseKStLaendercode = adresseKStLaendercode;
        this.textBundesland = textBundesland;
        this.babZeilendefinition = babZeilendefinition;
        this.angebotsprojekt = angebotsprojekt;
        this.auftragsprojekt = auftragsprojekt;
        this.auftragseingang = auftragseingang;
        this.adressnrDerKostenstelle = adressnrDerKostenstelle;
        this.adressnrDesAuftraggebers = adressnrDesAuftraggebers;
        this.debitor = debitor;
        this.kundennrBeimAuftraggeber = kundennrBeimAuftraggeber;
        this.empfangsstelle = empfangsstelle;
        this.bauleiter = bauleiter;
        this.bauleitergruppe = bauleitergruppe;
        this.oberbauleiter = oberbauleiter;
        this.adressnrBauleiterBauherrn = adressnrBauleiterBauherrn;
        this.adressatBauleiterBauherrn = adressatBauleiterBauherrn;
        this.adressnrDesBauherrn = adressnrDesBauherrn;
        this.adressatDesBauherrn = adressatDesBauherrn;
        this.adressnrDesArchitekten = adressnrDesArchitekten;
        this.adressatDesArchitekten = adressatDesArchitekten;
        this.adressnrPolier = adressnrPolier;
        this.adressatPolier = adressatPolier;
        this.vertragsart = vertragsart;
        this.gewaehrleistungInMonaten = gewaehrleistungInMonaten;
        this.buergschaftsbetrag = buergschaftsbetrag;
        this.vertragsbedVorspann = vertragsbedVorspann;
        this.vertragsbedNachspann = vertragsbedNachspann;
        this.vertragsbedErlaeuterung = vertragsbedErlaeuterung;
        this.zahlungsbedingung = zahlungsbedingung;
        this.nachlaesse = nachlaesse;
        this.niederlassung = niederlassung;
        this.ergebniszuordnung = ergebniszuordnung;
        this.sparte = sparte;
        this.typ = typ;
        this.gruppe = gruppe;
        this.gruppenstufe = gruppenstufe;
        this.gruppenstufe2 = gruppenstufe2;
        this.gruppenstufe3 = gruppenstufe3;
        this.gruppenstufe4 = gruppenstufe4;
        this.gruppenstufe5 = gruppenstufe5;
        this.statistikgruppe = statistikgruppe;
        this.uBahnSteuerpflichtAT = uBahnSteuerpflichtAT;
        this.gemeindecodeKommunalsteuerAT = gemeindecodeKommunalsteuerAT;
        this.stammkostenstelleFuer = stammkostenstelleFuer;
        this.zusammenzaehlungsart = zusammenzaehlungsart;
        this.zusammenzaehlung = zusammenzaehlung;
        this.misId = misId;
        this.baubeginndatum = baubeginndatum;
        this.voraussBauendedatum = voraussBauendedatum;
        this.bauendedatum = bauendedatum;
        this.bauabnahmedatum = bauabnahmedatum;
        this.bauzeitInMonaten = bauzeitInMonaten;
        this.drucken = drucken;
        this.druckenNurWennBewegung = druckenNurWennBewegung;
        this.druckenNachBauende = druckenNachBauende;
        this.aufErgebnislistenDrucken = aufErgebnislistenDrucken;
        this.vorjahreswertErmitteln = vorjahreswertErmitteln;
        this.gesamtwertErmitteln = gesamtwertErmitteln;
        this.umlageNachBauende = umlageNachBauende;
        this.istBautraeger = istBautraeger;
        this.schlussgerechnet = schlussgerechnet;
        this.datumSchlussgerechnet = datumSchlussgerechnet;
        this.abgerechnetPercent = abgerechnetPercent;
        this.fertigstellungPercent = fertigstellungPercent;
        this.datumFertigstellung = datumFertigstellung;
        this.fertig = fertig;
        this.mwStSatz = mwStSatz;
        this.buchenMitBAS = buchenMitBAS;
        this.buchenMitGeraet = buchenMitGeraet;
        this.geraetekonditionIntern = geraetekonditionIntern;
        this.geraetekonditionExtern = geraetekonditionExtern;
        this.geraetekonditionIntLeistung = geraetekonditionIntLeistung;
        this.geraetekonditionExtLeistung = geraetekonditionExtLeistung;
        this.balAltIntern = balAltIntern;
        this.balAltExtern = balAltExtern;
        this.balPercentNeuIntern = balPercentNeuIntern;
        this.balPercentNeuExtern = balPercentNeuExtern;
        this.balPercentNeuRuecknahmeIntern = balPercentNeuRuecknahmeIntern;
        this.balPercentNeuRuecknahmeExtern = balPercentNeuRuecknahmeExtern;
        this.balPercentGebrauchtIntern = balPercentGebrauchtIntern;
        this.balPercentGebrauchtExtern = balPercentGebrauchtExtern;
        this.balPercentGebrauchtRuecknIntern = balPercentGebrauchtRuecknIntern;
        this.balPercentGebrauchtRuecknExtern = balPercentGebrauchtRuecknExtern;
        this.balPercentLadepauschaleIntern = balPercentLadepauschaleIntern;
        this.balPercentLadepauschaleExtern = balPercentLadepauschaleExtern;
        this.argeKostenstelle = argeKostenstelle;
        this.fremdunternehmen = fremdunternehmen;
        this.bauhof = bauhof;
        this.geraetebestand = geraetebestand;
        this.hoehenmeter = hoehenmeter;
        this.cup = cup;
        this.cig = cig;
        this.kostenartMitMenge = kostenartMitMenge;
        this.dbIIIPercentPrognose = dbIIIPercentPrognose;
        this.einfacheFahrtzeit = einfacheFahrtzeit;
        this.argPflichtig = argPflichtig;
        this.wwwAdresseURL = wwwAdresseURL;
        this.easyRapport = easyRapport;
        this.externeGeraeteergebnisse = externeGeraeteergebnisse;
        this.zhsLoggerbezeichnung1 = zhsLoggerbezeichnung1;
        this.zhsLoggerbezeichnung2 = zhsLoggerbezeichnung2;
        this.zhsAktivAufLogger = zhsAktivAufLogger;
        this.zhsKostenstellengruppe = zhsKostenstellengruppe;
        this.zhsUnterkostenstellen = zhsUnterkostenstellen;
        this.zhsLohnarten = zhsLohnarten;
        this.zhsGEO_Status = zhsGEO_Status;
        this.zhsGEOPosition = zhsGEOPosition;
        this.zhsStdUKST = zhsStdUKST;
        this.gesperrt = gesperrt;
        this.sperrhinweis = sperrhinweis;
        this.gueltigAb = gueltigAb;
        this.gueltigBis = gueltigBis;
        this.neuanlagesystem = neuanlagesystem;
        this.neuanlagebenutzer = neuanlagebenutzer;
        this.neuanlagedatum = neuanlagedatum;
        this.neuanlagezeit = neuanlagezeit;
        this.aenderungssystem = aenderungssystem;
        this.aenderungsbenutzer = aenderungsbenutzer;
        this.aenderungsdatum = aenderungsdatum;
        this.aenderungszeit = aenderungszeit;
        this.mandantenfilter = mandantenfilter;
        this.auftragsperiodenfilter = auftragsperiodenfilter;
    }

    @JsonProperty("@odata.context")
    @JsonIgnore
    public String odataContext() {
        return odataContext;
    }

    @JsonProperty("@odata.etag")
    @JsonIgnore
    public String odataEtag() {
        return odataEtag;
    }

    @JsonProperty("Control2")
    @Nonnull
    public String control2() {
        return control2;
    }

    @JsonProperty("Name")
    @Nullable
    public String name() {
        return name;
    }

    @JsonProperty("Bezeichnung2")
    @Nullable
    public String bezeichnung2() {
        return bezeichnung2;
    }

    @JsonProperty("TmpVorschauID")
    @Nullable
    @JsonIgnore
    public String tmpVorschauID() {
        return tmpVorschauID;
    }

    @JsonProperty("Bemerkung")
    @Nullable
    @JsonIgnore
    public Boolean bemerkung() {
        return bemerkung;
    }

    @JsonProperty("Suchbegriff")
    @Nullable
    public String suchbegriff() {
        return suchbegriff;
    }

    @JsonProperty("Auftragswert")
    @Nullable
    public Double auftragswert() {
        return auftragswert;
    }

    @JsonProperty("Nachtragswert")
    @Nullable
    @JsonIgnore
    public String nachtragswert() {
        return nachtragswert;
    }

    @JsonProperty("Konsolidierungscode")
    @Nullable
    public String konsolidierungscode() {
        return konsolidierungscode;
    }

    @JsonProperty("Gemeindeschlüssel_AGS")
    @Nullable
    @JsonIgnore
    public String gemeindeschluesselAGS() {
        return gemeindeschluesselAGS;
    }

    @JsonProperty("Bezeichnung_AGS")
    @Nullable
    public String bezeichnungAGS() {
        return bezeichnungAGS;
    }

    @JsonProperty("Ist_Baustelle")
    @Nullable
    public Boolean istBaustelle() {
        return istBaustelle;
    }

    @JsonProperty("Adresse_KSt_Ort")
    @Nullable
    @JsonIgnore
    public String adresseKStOrt() {
        return adresseKStOrt;
    }

    @JsonProperty("Adresse_KSt_PLZ")
    @Nullable
    @JsonIgnore
    public String adresseKStPLZ() {
        return adresseKStPLZ;
    }

    @JsonProperty("Adresse_KSt_Bundesland")
    @Nullable
    @JsonIgnore
    public String adresseKStBundesland() {
        return adresseKStBundesland;
    }

    @JsonProperty("Adresse_KSt_Ländercode")
    @Nullable
    @JsonIgnore
    public String adresseKStLaendercode() {
        return adresseKStLaendercode;
    }

    @JsonProperty("Text_Bundesland")
    @Nullable
    @JsonIgnore
    public String textBundesland() {
        return textBundesland;
    }

    @JsonProperty("BAB_Zeilendefinition")
    @Nullable
    public String babZeilendefinition() {
        return babZeilendefinition;
    }

    @JsonProperty("Angebotsprojekt")
    @Nullable
    public String angebotsprojekt() {
        return angebotsprojekt;
    }

    @JsonProperty("Auftragsprojekt")
    @Nullable
    public String auftragsprojekt() {
        return auftragsprojekt;
    }

    @JsonProperty("Auftragseingang")
    @Nullable
    public String auftragseingang() {
        return auftragseingang;
    }

    @JsonProperty("Adressnr_der_Kostenstelle")
    @Nullable
    public String adressnrDerKostenstelle() {
        return adressnrDerKostenstelle;
    }

    @JsonProperty("Adressnr_des_Auftraggebers")
    @Nullable
    public String adressnrDesAuftraggebers() {
        return adressnrDesAuftraggebers;
    }

    @JsonProperty("Debitor")
    @Nullable
    public String debitor() {
        return debitor;
    }

    @JsonProperty("Kundennr_beim_Auftraggeber")
    @Nullable
    public String kundennrBeimAuftraggeber() {
        return kundennrBeimAuftraggeber;
    }

    @JsonProperty("Empfangsstelle")
    @Nullable
    public String empfangsstelle() {
        return empfangsstelle;
    }

    @JsonProperty("Bauleiter")
    @Nullable
    public String bauleiter() {
        return bauleiter;
    }

    @JsonProperty("Bauleitergruppe")
    @Nullable
    public String bauleitergruppe() {
        return bauleitergruppe;
    }

    @JsonProperty("Oberbauleiter")
    @Nullable
    public String oberbauleiter() {
        return oberbauleiter;
    }

    @JsonProperty("Adressnr_Bauleiter_Bauherrn")
    @Nullable
    public String adressnrBauleiterBauherrn() {
        return adressnrBauleiterBauherrn;
    }

    @JsonProperty("Adressat_Bauleiter_Bauherrn")
    @Nullable
    public String adressatBauleiterBauherrn() {
        return adressatBauleiterBauherrn;
    }

    @JsonProperty("Adressnr_des_Bauherrn")
    @Nullable
    public String adressnrDesBauherrn() {
        return adressnrDesBauherrn;
    }

    @JsonProperty("Adressat_des_Bauherrn")
    @Nullable
    public String adressatDesBauherrn() {
        return adressatDesBauherrn;
    }

    @JsonProperty("Adressnr_des_Architekten")
    @Nullable
    public String adressnrDesArchitekten() {
        return adressnrDesArchitekten;
    }

    @JsonProperty("Adressat_des_Architekten")
    @Nullable
    public String adressatDesArchitekten() {
        return adressatDesArchitekten;
    }

    @JsonProperty("Adressnr_Polier")
    @Nullable
    public String adressnrPolier() {
        return adressnrPolier;
    }

    @JsonProperty("Adressat_Polier")
    @Nullable
    public String adressatPolier() {
        return adressatPolier;
    }

    @JsonProperty("Vertragsart")
    @Nullable
    public String vertragsart() {
        return vertragsart;
    }

    @JsonProperty("Gewährleistung_in_Monaten")
    @Nullable
    public Integer gewaehrleistungInMonaten() {
        return gewaehrleistungInMonaten;
    }

    @JsonProperty("Bürgschaftsbetrag")
    @Nullable
    public String buergschaftsbetrag() {
        return buergschaftsbetrag;
    }

    @JsonProperty("Vertragsbed_Vorspann")
    @Nullable
    public Integer vertragsbedVorspann() {
        return vertragsbedVorspann;
    }

    @JsonProperty("Vertragsbed_Nachspann")
    @Nullable
    public Integer vertragsbedNachspann() {
        return vertragsbedNachspann;
    }

    @JsonProperty("Vertragsbed_Erläuterung")
    @Nullable
    public Integer vertragsbedErlaeuterung() {
        return vertragsbedErlaeuterung;
    }

    @JsonProperty("Zahlungsbedingung")
    @Nullable
    public String zahlungsbedingung() {
        return zahlungsbedingung;
    }

    @JsonProperty("Nachlässe")
    @Nullable
    public String nachlaesse() {
        return nachlaesse;
    }

    @JsonProperty("Niederlassung")
    @Nullable
    public String niederlassung() {
        return niederlassung;
    }

    @JsonProperty("Ergebniszuordnung")
    @Nullable
    public String ergebniszuordnung() {
        return ergebniszuordnung;
    }

    @JsonProperty("Sparte")
    @Nullable
    public String sparte() {
        return sparte;
    }

    @JsonProperty("Typ")
    @Nullable
    public String typ() {
        return typ;
    }

    @JsonProperty("Gruppe")
    @Nullable
    public String gruppe() {
        return gruppe;
    }

    @JsonProperty("Gruppenstufe")
    @Nullable
    public String gruppenstufe() {
        return gruppenstufe;
    }

    @JsonProperty("Gruppenstufe2")
    @Nullable
    public String gruppenstufe2() {
        return gruppenstufe2;
    }

    @JsonProperty("Gruppenstufe3")
    @Nullable
    public String gruppenstufe3() {
        return gruppenstufe3;
    }

    @JsonProperty("Gruppenstufe4")
    @Nullable
    public String gruppenstufe4() {
        return gruppenstufe4;
    }

    @JsonProperty("Gruppenstufe5")
    @Nullable
    public String gruppenstufe5() {
        return gruppenstufe5;
    }

    @JsonProperty("Statistikgruppe")
    @Nullable
    public String statistikgruppe() {
        return statistikgruppe;
    }

    @JsonProperty("U_Bahn_Steuerpflicht_AT")
    @Nullable
    public Boolean uBahnSteuerpflichtAT() {
        return uBahnSteuerpflichtAT;
    }

    @JsonProperty("Gemeindecode_Kommunalsteuer_AT")
    @Nullable
    public String gemeindecodeKommunalsteuerAT() {
        return gemeindecodeKommunalsteuerAT;
    }

    @JsonProperty("Stammkostenstelle_für")
    @Nullable
    public String stammkostenstelleFuer() {
        return stammkostenstelleFuer;
    }

    @JsonProperty("Zusammenzählungsart")
    @Nullable
    public String zusammenzaehlungsart() {
        return zusammenzaehlungsart;
    }

    @JsonProperty("Zusammenzählung")
    @Nullable
    public String zusammenzaehlung() {
        return zusammenzaehlung;
    }

    @JsonProperty("MIS_ID")
    @Nullable
    public String misId() {
        return misId;
    }

    @JsonProperty("Baubeginndatum")
    @Nullable
    public String baubeginndatum() {
        return baubeginndatum;
    }

    @JsonProperty("Vorauss_Bauendedatum")
    @Nullable
    public String voraussBauendedatum() {
        return voraussBauendedatum;
    }

    @JsonProperty("Bauendedatum")
    @Nullable
    public String bauendedatum() {
        return bauendedatum;
    }

    @JsonProperty("Bauabnahmedatum")
    @Nullable
    public String bauabnahmedatum() {
        return bauabnahmedatum;
    }

    @JsonProperty("Bauzeit_in_Monaten")
    @Nullable
    public Integer bauzeitInMonaten() {
        return bauzeitInMonaten;
    }

    @JsonProperty("Drucken")
    @Nullable
    public Boolean drucken() {
        return drucken;
    }

    @JsonProperty("Drucken_nur_wenn_Bewegung")
    @Nullable
    public Boolean druckenNurWennBewegung() {
        return druckenNurWennBewegung;
    }

    @JsonProperty("Drucken_nach_Bauende")
    @Nullable
    public Boolean druckenNachBauende() {
        return druckenNachBauende;
    }

    @JsonProperty("Auf_Ergebnislisten_drucken")
    @Nullable
    public Boolean aufErgebnislistenDrucken() {
        return aufErgebnislistenDrucken;
    }

    @JsonProperty("Vorjahreswert_ermitteln")
    @Nullable
    public Boolean vorjahreswertErmitteln() {
        return vorjahreswertErmitteln;
    }

    @JsonProperty("Gesamtwert_ermitteln")
    @Nullable
    public Boolean gesamtwertErmitteln() {
        return gesamtwertErmitteln;
    }

    @JsonProperty("Umlage_nach_Bauende")
    @Nullable
    public Boolean umlageNachBauende() {
        return umlageNachBauende;
    }

    @JsonProperty("Ist_Bauträger")
    @Nullable
    public Boolean istBautraeger() {
        return istBautraeger;
    }

    @JsonProperty("Schlußgerechnet")
    @Nullable
    public Boolean schlussgerechnet() {
        return schlussgerechnet;
    }

    @JsonProperty("Datum_Schlußgerechnet")
    @Nullable
    public String datumSchlussgerechnet() {
        return datumSchlussgerechnet;
    }

    @JsonProperty("Abgerechnet_Percent")
    @Nullable
    public String abgerechnetPercent() {
        return abgerechnetPercent;
    }

    @JsonProperty("Fertigstellung_Percent")
    @Nullable
    public String fertigstellungPercent() {
        return fertigstellungPercent;
    }

    @JsonProperty("Datum_Fertigstellung")
    @Nullable
    public String datumFertigstellung() {
        return datumFertigstellung;
    }

    @JsonProperty("Fertig")
    @Nullable
    public Boolean fertig() {
        return fertig;
    }

    @JsonProperty("MwSt_Satz")
    @Nullable
    public String mwStSatz() {
        return mwStSatz;
    }

    @JsonProperty("Buchen_mit_BAS")
    @Nullable
    public String buchenMitBAS() {
        return buchenMitBAS;
    }

    @JsonProperty("Buchen_mit_Gerät")
    @Nullable
    public String buchenMitGeraet() {
        return buchenMitGeraet;
    }

    @JsonProperty("Gerätekondition_intern")
    @Nullable
    public String geraetekonditionIntern() {
        return geraetekonditionIntern;
    }

    @JsonProperty("Gerätekondition_extern")
    @Nullable
    public String geraetekonditionExtern() {
        return geraetekonditionExtern;
    }

    @JsonProperty("Gerätekondition_int_Leistung")
    @Nullable
    public String geraetekonditionIntLeistung() {
        return geraetekonditionIntLeistung;
    }

    @JsonProperty("Gerätekondition_ext_Leistung")
    @Nullable
    public String geraetekonditionExtLeistung() {
        return geraetekonditionExtLeistung;
    }

    @JsonProperty("BAL_alt_intern")
    @Nullable
    public Boolean balAltIntern() {
        return balAltIntern;
    }

    @JsonProperty("BAL_alt_extern")
    @Nullable
    public Boolean balAltExtern() {
        return balAltExtern;
    }

    @JsonProperty("BAL_Percent_neu_intern")
    @Nullable
    public String balPercentNeuIntern() {
        return balPercentNeuIntern;
    }

    @JsonProperty("BAL_Percent_neu_extern")
    @Nullable
    public String balPercentNeuExtern() {
        return balPercentNeuExtern;
    }

    @JsonProperty("BAL_Percent_neu_Rücknahme_intern")
    @Nullable
    public String balPercentNeuRuecknahmeIntern() {
        return balPercentNeuRuecknahmeIntern;
    }

    @JsonProperty("BAL_Percent_neu_Rücknahme_extern")
    @Nullable
    public String balPercentNeuRuecknahmeExtern() {
        return balPercentNeuRuecknahmeExtern;
    }

    @JsonProperty("BAL_Percent_gebraucht_intern")
    @Nullable
    public String balPercentGebrauchtIntern() {
        return balPercentGebrauchtIntern;
    }

    @JsonProperty("BAL_Percent_gebraucht_extern")
    @Nullable
    public String balPercentGebrauchtExtern() {
        return balPercentGebrauchtExtern;
    }

    @JsonProperty("BAL_Percent_gebraucht_Rückn_intern")
    @Nullable
    public String balPercentGebrauchtRuecknIntern() {
        return balPercentGebrauchtRuecknIntern;
    }

    @JsonProperty("BAL_Percent_gebraucht_Rückn_extern")
    @Nullable
    public String balPercentGebrauchtRuecknExtern() {
        return balPercentGebrauchtRuecknExtern;
    }

    @JsonProperty("BAL_Percent_Ladepauschale_intern")
    @Nullable
    public String balPercentLadepauschaleIntern() {
        return balPercentLadepauschaleIntern;
    }

    @JsonProperty("BAL_Percent_Ladepauschale_extern")
    @Nullable
    public String balPercentLadepauschaleExtern() {
        return balPercentLadepauschaleExtern;
    }

    @JsonProperty("ARGE_Kostenstelle")
    @Nullable
    public String argeKostenstelle() {
        return argeKostenstelle;
    }

    @JsonProperty("Fremdunternehmen")
    @Nullable
    public String fremdunternehmen() {
        return fremdunternehmen;
    }

    @JsonProperty("Bauhof")
    @Nullable
    public String bauhof() {
        return bauhof;
    }

    @JsonProperty("Gerätebestand")
    @Nullable
    public String geraetebestand() {
        return geraetebestand;
    }

    @JsonProperty("Höhenmeter")
    @Nullable
    public String hoehenmeter() {
        return hoehenmeter;
    }

    @JsonProperty("CUP")
    @Nullable
    public String cup() {
        return cup;
    }

    @JsonProperty("CIG")
    @Nullable
    public String cig() {
        return cig;
    }

    @JsonProperty("Kostenart_mit_Menge")
    @Nullable
    public String kostenartMitMenge() {
        return kostenartMitMenge;
    }

    @JsonProperty("DB_III_Percent_Prognose")
    @Nullable
    public String dbIIIPercentPrognose() {
        return dbIIIPercentPrognose;
    }

    @JsonProperty("Einfache_Fahrtzeit")
    @Nullable
    public String einfacheFahrtzeit() {
        return einfacheFahrtzeit;
    }

    @JsonProperty("ARG_pflichtig")
    @Nullable
    public String argPflichtig() {
        return argPflichtig;
    }

    @JsonProperty("WWW_Adresse_URL")
    @Nullable
    public String wwwAdresseURL() {
        return wwwAdresseURL;
    }

    @JsonProperty("EasyRapport")
    @Nullable
    public String easyRapport() {
        return easyRapport;
    }

    @JsonProperty("Externe_Geräteergebnisse")
    @Nullable
    public Boolean externeGeraeteergebnisse() {
        return externeGeraeteergebnisse;
    }

    @JsonProperty("ZHS_Loggerbezeichnung_1")
    @Nullable
    public String zhsLoggerbezeichnung1() {
        return zhsLoggerbezeichnung1;
    }

    @JsonProperty("ZHS_Loggerbezeichnung_2")
    @Nullable
    public String zhsLoggerbezeichnung2() {
        return zhsLoggerbezeichnung2;
    }

    @JsonProperty("ZHS_Aktiv_auf_Logger")
    @Nullable
    public String zhsAktivAufLogger() {
        return zhsAktivAufLogger;
    }

    @JsonProperty("ZHS_Kostenstellengruppe")
    @Nullable
    public String zhsKostenstellengruppe() {
        return zhsKostenstellengruppe;
    }

    @JsonProperty("ZHS_Unterkostenstellen")
    @Nullable
    public String zhsUnterkostenstellen() {
        return zhsUnterkostenstellen;
    }

    @JsonProperty("ZHS_Lohnarten")
    @Nullable
    public String zhsLohnarten() {
        return zhsLohnarten;
    }

    @JsonProperty("ZHS_GEO_Status")
    @Nullable
    public String zhsGEO_Status() {
        return zhsGEO_Status;
    }

    @JsonProperty("ZHS_GEO_Position")
    @Nullable
    public String zhsGEOPosition() {
        return zhsGEOPosition;
    }

    @JsonProperty("ZHS_StdUKST")
    @Nullable
    public String zhsStdUKST() {
        return zhsStdUKST;
    }

    @JsonProperty("Gesperrt")
    @Nullable
    public String gesperrt() {
        return gesperrt;
    }

    @JsonProperty("Sperrhinweis")
    @Nullable
    public String sperrhinweis() {
        return sperrhinweis;
    }

    @JsonProperty("Gültig_ab")
    @Nullable
    public String gueltigAb() {
        return gueltigAb;
    }

    @JsonProperty("Gültig_bis")
    @Nullable
    public String gueltigBis() {
        return gueltigBis;
    }

    @JsonProperty("Neuanlagesystem")
    @Nullable
    @JsonIgnore
    public String neuanlagesystem() {
        return neuanlagesystem;
    }

    @JsonProperty("Neuanlagebenutzer")
    @Nullable
    @JsonIgnore
    public String neuanlagebenutzer() {
        return neuanlagebenutzer;
    }

    @JsonProperty("Neuanlagedatum")
    @Nullable
    @JsonIgnore
    public String neuanlagedatum() {
        return neuanlagedatum;
    }

    @JsonProperty("Neuanlagezeit")
    @Nullable
    @JsonIgnore
    public String neuanlagezeit() {
        return neuanlagezeit;
    }

    @JsonProperty("Änderungssystem")
    @Nullable
    @JsonIgnore
    public String aenderungssystem() {
        return aenderungssystem;
    }

    @JsonProperty("Änderungsbenutzer")
    @Nullable
    @JsonIgnore
    public String aenderungsbenutzer() {
        return aenderungsbenutzer;
    }

    @JsonProperty("Änderungsdatum")
    @Nullable
    @JsonIgnore
    public String aenderungsdatum() {
        return aenderungsdatum;
    }

    @JsonProperty("Änderungszeit")
    @Nullable
    @JsonIgnore
    public String aenderungszeit() {
        return aenderungszeit;
    }

    @Override
    @JsonIgnore
    public String getUUID() {
        return this.control2;
    }

    @JsonProperty("Mandantenfilter")
    @Nullable
    public String mandantenfilter() {
        return mandantenfilter;
    }

    @JsonProperty("Auftragsperiodenfilter")
    @Nullable
    public String auftragsperiodenfilter() {
        return auftragsperiodenfilter;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (NevarisCostCenter) obj;
        return Objects.equals(this.odataContext, that.odataContext) &&
                Objects.equals(this.odataEtag, that.odataEtag) &&
                Objects.equals(this.control2, that.control2) &&
                Objects.equals(this.name, that.name) &&
                Objects.equals(this.bezeichnung2, that.bezeichnung2) &&
                Objects.equals(this.tmpVorschauID, that.tmpVorschauID) &&
                Objects.equals(this.bemerkung, that.bemerkung) &&
                Objects.equals(this.suchbegriff, that.suchbegriff) &&
                Objects.equals(this.auftragswert, that.auftragswert) &&
                Objects.equals(this.nachtragswert, that.nachtragswert) &&
                Objects.equals(this.konsolidierungscode, that.konsolidierungscode) &&
                Objects.equals(this.gemeindeschluesselAGS, that.gemeindeschluesselAGS) &&
                Objects.equals(this.bezeichnungAGS, that.bezeichnungAGS) &&
                Objects.equals(this.istBaustelle, that.istBaustelle) &&
                Objects.equals(this.adresseKStOrt, that.adresseKStOrt) &&
                Objects.equals(this.adresseKStPLZ, that.adresseKStPLZ) &&
                Objects.equals(this.adresseKStBundesland, that.adresseKStBundesland) &&
                Objects.equals(this.adresseKStLaendercode, that.adresseKStLaendercode) &&
                Objects.equals(this.textBundesland, that.textBundesland) &&
                Objects.equals(this.babZeilendefinition, that.babZeilendefinition) &&
                Objects.equals(this.angebotsprojekt, that.angebotsprojekt) &&
                Objects.equals(this.auftragsprojekt, that.auftragsprojekt) &&
                Objects.equals(this.auftragseingang, that.auftragseingang) &&
                Objects.equals(this.adressnrDerKostenstelle, that.adressnrDerKostenstelle) &&
                Objects.equals(this.adressnrDesAuftraggebers, that.adressnrDesAuftraggebers) &&
                Objects.equals(this.debitor, that.debitor) &&
                Objects.equals(this.kundennrBeimAuftraggeber, that.kundennrBeimAuftraggeber) &&
                Objects.equals(this.empfangsstelle, that.empfangsstelle) &&
                Objects.equals(this.bauleiter, that.bauleiter) &&
                Objects.equals(this.bauleitergruppe, that.bauleitergruppe) &&
                Objects.equals(this.oberbauleiter, that.oberbauleiter) &&
                Objects.equals(this.adressnrBauleiterBauherrn, that.adressnrBauleiterBauherrn) &&
                Objects.equals(this.adressatBauleiterBauherrn, that.adressatBauleiterBauherrn) &&
                Objects.equals(this.adressnrDesBauherrn, that.adressnrDesBauherrn) &&
                Objects.equals(this.adressatDesBauherrn, that.adressatDesBauherrn) &&
                Objects.equals(this.adressnrDesArchitekten, that.adressnrDesArchitekten) &&
                Objects.equals(this.adressatDesArchitekten, that.adressatDesArchitekten) &&
                Objects.equals(this.adressnrPolier, that.adressnrPolier) &&
                Objects.equals(this.adressatPolier, that.adressatPolier) &&
                Objects.equals(this.vertragsart, that.vertragsart) &&
                Objects.equals(this.gewaehrleistungInMonaten, that.gewaehrleistungInMonaten) &&
                Objects.equals(this.buergschaftsbetrag, that.buergschaftsbetrag) &&
                Objects.equals(this.vertragsbedVorspann, that.vertragsbedVorspann) &&
                Objects.equals(this.vertragsbedNachspann, that.vertragsbedNachspann) &&
                Objects.equals(this.vertragsbedErlaeuterung, that.vertragsbedErlaeuterung) &&
                Objects.equals(this.zahlungsbedingung, that.zahlungsbedingung) &&
                Objects.equals(this.nachlaesse, that.nachlaesse) &&
                Objects.equals(this.niederlassung, that.niederlassung) &&
                Objects.equals(this.ergebniszuordnung, that.ergebniszuordnung) &&
                Objects.equals(this.sparte, that.sparte) &&
                Objects.equals(this.typ, that.typ) &&
                Objects.equals(this.gruppe, that.gruppe) &&
                Objects.equals(this.gruppenstufe, that.gruppenstufe) &&
                Objects.equals(this.gruppenstufe2, that.gruppenstufe2) &&
                Objects.equals(this.gruppenstufe3, that.gruppenstufe3) &&
                Objects.equals(this.gruppenstufe4, that.gruppenstufe4) &&
                Objects.equals(this.gruppenstufe5, that.gruppenstufe5) &&
                Objects.equals(this.statistikgruppe, that.statistikgruppe) &&
                Objects.equals(this.uBahnSteuerpflichtAT, that.uBahnSteuerpflichtAT) &&
                Objects.equals(this.gemeindecodeKommunalsteuerAT, that.gemeindecodeKommunalsteuerAT) &&
                Objects.equals(this.stammkostenstelleFuer, that.stammkostenstelleFuer) &&
                Objects.equals(this.zusammenzaehlungsart, that.zusammenzaehlungsart) &&
                Objects.equals(this.zusammenzaehlung, that.zusammenzaehlung) &&
                Objects.equals(this.misId, that.misId) &&
                Objects.equals(this.baubeginndatum, that.baubeginndatum) &&
                Objects.equals(this.voraussBauendedatum, that.voraussBauendedatum) &&
                Objects.equals(this.bauendedatum, that.bauendedatum) &&
                Objects.equals(this.bauabnahmedatum, that.bauabnahmedatum) &&
                Objects.equals(this.bauzeitInMonaten, that.bauzeitInMonaten) &&
                Objects.equals(this.drucken, that.drucken) &&
                Objects.equals(this.druckenNurWennBewegung, that.druckenNurWennBewegung) &&
                Objects.equals(this.druckenNachBauende, that.druckenNachBauende) &&
                Objects.equals(this.aufErgebnislistenDrucken, that.aufErgebnislistenDrucken) &&
                Objects.equals(this.vorjahreswertErmitteln, that.vorjahreswertErmitteln) &&
                Objects.equals(this.gesamtwertErmitteln, that.gesamtwertErmitteln) &&
                Objects.equals(this.umlageNachBauende, that.umlageNachBauende) &&
                Objects.equals(this.istBautraeger, that.istBautraeger) &&
                Objects.equals(this.schlussgerechnet, that.schlussgerechnet) &&
                Objects.equals(this.datumSchlussgerechnet, that.datumSchlussgerechnet) &&
                Objects.equals(this.abgerechnetPercent, that.abgerechnetPercent) &&
                Objects.equals(this.fertigstellungPercent, that.fertigstellungPercent) &&
                Objects.equals(this.datumFertigstellung, that.datumFertigstellung) &&
                Objects.equals(this.fertig, that.fertig) &&
                Objects.equals(this.mwStSatz, that.mwStSatz) &&
                Objects.equals(this.buchenMitBAS, that.buchenMitBAS) &&
                Objects.equals(this.buchenMitGeraet, that.buchenMitGeraet) &&
                Objects.equals(this.geraetekonditionIntern, that.geraetekonditionIntern) &&
                Objects.equals(this.geraetekonditionExtern, that.geraetekonditionExtern) &&
                Objects.equals(this.geraetekonditionIntLeistung, that.geraetekonditionIntLeistung) &&
                Objects.equals(this.geraetekonditionExtLeistung, that.geraetekonditionExtLeistung) &&
                Objects.equals(this.balAltIntern, that.balAltIntern) &&
                Objects.equals(this.balAltExtern, that.balAltExtern) &&
                Objects.equals(this.balPercentNeuIntern, that.balPercentNeuIntern) &&
                Objects.equals(this.balPercentNeuExtern, that.balPercentNeuExtern) &&
                Objects.equals(this.balPercentNeuRuecknahmeIntern, that.balPercentNeuRuecknahmeIntern) &&
                Objects.equals(this.balPercentNeuRuecknahmeExtern, that.balPercentNeuRuecknahmeExtern) &&
                Objects.equals(this.balPercentGebrauchtIntern, that.balPercentGebrauchtIntern) &&
                Objects.equals(this.balPercentGebrauchtExtern, that.balPercentGebrauchtExtern) &&
                Objects.equals(this.balPercentGebrauchtRuecknIntern, that.balPercentGebrauchtRuecknIntern) &&
                Objects.equals(this.balPercentGebrauchtRuecknExtern, that.balPercentGebrauchtRuecknExtern) &&
                Objects.equals(this.balPercentLadepauschaleIntern, that.balPercentLadepauschaleIntern) &&
                Objects.equals(this.balPercentLadepauschaleExtern, that.balPercentLadepauschaleExtern) &&
                Objects.equals(this.argeKostenstelle, that.argeKostenstelle) &&
                Objects.equals(this.fremdunternehmen, that.fremdunternehmen) &&
                Objects.equals(this.bauhof, that.bauhof) &&
                Objects.equals(this.geraetebestand, that.geraetebestand) &&
                Objects.equals(this.hoehenmeter, that.hoehenmeter) &&
                Objects.equals(this.cup, that.cup) &&
                Objects.equals(this.cig, that.cig) &&
                Objects.equals(this.kostenartMitMenge, that.kostenartMitMenge) &&
                Objects.equals(this.dbIIIPercentPrognose, that.dbIIIPercentPrognose) &&
                Objects.equals(this.einfacheFahrtzeit, that.einfacheFahrtzeit) &&
                Objects.equals(this.argPflichtig, that.argPflichtig) &&
                Objects.equals(this.wwwAdresseURL, that.wwwAdresseURL) &&
                Objects.equals(this.easyRapport, that.easyRapport) &&
                Objects.equals(this.externeGeraeteergebnisse, that.externeGeraeteergebnisse) &&
                Objects.equals(this.zhsLoggerbezeichnung1, that.zhsLoggerbezeichnung1) &&
                Objects.equals(this.zhsLoggerbezeichnung2, that.zhsLoggerbezeichnung2) &&
                Objects.equals(this.zhsAktivAufLogger, that.zhsAktivAufLogger) &&
                Objects.equals(this.zhsKostenstellengruppe, that.zhsKostenstellengruppe) &&
                Objects.equals(this.zhsUnterkostenstellen, that.zhsUnterkostenstellen) &&
                Objects.equals(this.zhsLohnarten, that.zhsLohnarten) &&
                Objects.equals(this.zhsGEO_Status, that.zhsGEO_Status) &&
                Objects.equals(this.zhsGEOPosition, that.zhsGEOPosition) &&
                Objects.equals(this.zhsStdUKST, that.zhsStdUKST) &&
                Objects.equals(this.gesperrt, that.gesperrt) &&
                Objects.equals(this.sperrhinweis, that.sperrhinweis) &&
                Objects.equals(this.gueltigAb, that.gueltigAb) &&
                Objects.equals(this.gueltigBis, that.gueltigBis) &&
                Objects.equals(this.neuanlagesystem, that.neuanlagesystem) &&
                Objects.equals(this.neuanlagebenutzer, that.neuanlagebenutzer) &&
                Objects.equals(this.neuanlagedatum, that.neuanlagedatum) &&
                Objects.equals(this.neuanlagezeit, that.neuanlagezeit) &&
                Objects.equals(this.aenderungssystem, that.aenderungssystem) &&
                Objects.equals(this.aenderungsbenutzer, that.aenderungsbenutzer) &&
                Objects.equals(this.aenderungsdatum, that.aenderungsdatum) &&
                Objects.equals(this.aenderungszeit, that.aenderungszeit) &&
                Objects.equals(this.mandantenfilter, that.mandantenfilter) &&
                Objects.equals(this.auftragsperiodenfilter, that.auftragsperiodenfilter);
    }

    @Override
    public int hashCode() {
        return Objects.hash(odataContext, odataEtag, control2, name, bezeichnung2, tmpVorschauID, bemerkung, suchbegriff, auftragswert, nachtragswert, konsolidierungscode, gemeindeschluesselAGS, bezeichnungAGS, istBaustelle, adresseKStOrt, adresseKStPLZ, adresseKStBundesland, adresseKStLaendercode, textBundesland, babZeilendefinition, angebotsprojekt, auftragsprojekt, auftragseingang, adressnrDerKostenstelle, adressnrDesAuftraggebers, debitor, kundennrBeimAuftraggeber, empfangsstelle, bauleiter, bauleitergruppe, oberbauleiter, adressnrBauleiterBauherrn, adressatBauleiterBauherrn, adressnrDesBauherrn, adressatDesBauherrn, adressnrDesArchitekten, adressatDesArchitekten, adressnrPolier, adressatPolier, vertragsart, gewaehrleistungInMonaten, buergschaftsbetrag, vertragsbedVorspann, vertragsbedNachspann, vertragsbedErlaeuterung, zahlungsbedingung, nachlaesse, niederlassung, ergebniszuordnung, sparte, typ, gruppe, gruppenstufe, gruppenstufe2, gruppenstufe3, gruppenstufe4, gruppenstufe5, statistikgruppe, uBahnSteuerpflichtAT, gemeindecodeKommunalsteuerAT, stammkostenstelleFuer, zusammenzaehlungsart, zusammenzaehlung, misId, baubeginndatum, voraussBauendedatum, bauendedatum, bauabnahmedatum, bauzeitInMonaten, drucken, druckenNurWennBewegung, druckenNachBauende, aufErgebnislistenDrucken, vorjahreswertErmitteln, gesamtwertErmitteln, umlageNachBauende, istBautraeger, schlussgerechnet, datumSchlussgerechnet, abgerechnetPercent, fertigstellungPercent, datumFertigstellung, fertig, mwStSatz, buchenMitBAS, buchenMitGeraet, geraetekonditionIntern, geraetekonditionExtern, geraetekonditionIntLeistung, geraetekonditionExtLeistung, balAltIntern, balAltExtern, balPercentNeuIntern, balPercentNeuExtern, balPercentNeuRuecknahmeIntern, balPercentNeuRuecknahmeExtern, balPercentGebrauchtIntern, balPercentGebrauchtExtern, balPercentGebrauchtRuecknIntern, balPercentGebrauchtRuecknExtern, balPercentLadepauschaleIntern, balPercentLadepauschaleExtern, argeKostenstelle, fremdunternehmen, bauhof, geraetebestand, hoehenmeter, cup, cig, kostenartMitMenge, dbIIIPercentPrognose, einfacheFahrtzeit, argPflichtig, wwwAdresseURL, easyRapport, externeGeraeteergebnisse, zhsLoggerbezeichnung1, zhsLoggerbezeichnung2, zhsAktivAufLogger, zhsKostenstellengruppe, zhsUnterkostenstellen, zhsLohnarten, zhsGEO_Status, zhsGEOPosition, zhsStdUKST, gesperrt, sperrhinweis, gueltigAb, gueltigBis, neuanlagesystem, neuanlagebenutzer, neuanlagedatum, neuanlagezeit, aenderungssystem, aenderungsbenutzer, aenderungsdatum, aenderungszeit, mandantenfilter, auftragsperiodenfilter);
    }

    @Override
    public String toString() {
        return "NevarisCostCenter[" +
                "odataContext=" + odataContext + ", " +
                "odataEtag=" + odataEtag + ", " +
                "control2=" + control2 + ", " +
                "name=" + name + ", " +
                "bezeichnung2=" + bezeichnung2 + ", " +
                "tmpVorschauID=" + tmpVorschauID + ", " +
                "bemerkung=" + bemerkung + ", " +
                "suchbegriff=" + suchbegriff + ", " +
                "auftragswert=" + String.format(Locale.GERMAN, "%,.2f", auftragswert).replace(".","") + ", " +
                "nachtragswert=" + nachtragswert + ", " +
                "konsolidierungscode=" + konsolidierungscode + ", " +
                "gemeindeschluesselAGS=" + gemeindeschluesselAGS + ", " +
                "bezeichnungAGS=" + bezeichnungAGS + ", " +
                "istBaustelle=" + istBaustelle + ", " +
                "adresseKStOrt=" + adresseKStOrt + ", " +
                "adresseKStPLZ=" + adresseKStPLZ + ", " +
                "adresseKStBundesland=" + adresseKStBundesland + ", " +
                "adresseKStLaendercode=" + adresseKStLaendercode + ", " +
                "textBundesland=" + textBundesland + ", " +
                "babZeilendefinition=" + babZeilendefinition + ", " +
                "angebotsprojekt=" + angebotsprojekt + ", " +
                "auftragsprojekt=" + auftragsprojekt + ", " +
                "auftragseingang=" + auftragseingang + ", " +
                "adressnrDerKostenstelle=" + adressnrDerKostenstelle + ", " +
                "adressnrDesAuftraggebers=" + adressnrDesAuftraggebers + ", " +
                "debitor=" + debitor + ", " +
                "kundennrBeimAuftraggeber=" + kundennrBeimAuftraggeber + ", " +
                "empfangsstelle=" + empfangsstelle + ", " +
                "bauleiter=" + bauleiter + ", " +
                "bauleitergruppe=" + bauleitergruppe + ", " +
                "oberbauleiter=" + oberbauleiter + ", " +
                "adressnrBauleiterBauherrn=" + adressnrBauleiterBauherrn + ", " +
                "adressatBauleiterBauherrn=" + adressatBauleiterBauherrn + ", " +
                "adressnrDesBauherrn=" + adressnrDesBauherrn + ", " +
                "adressatDesBauherrn=" + adressatDesBauherrn + ", " +
                "adressnrDesArchitekten=" + adressnrDesArchitekten + ", " +
                "adressatDesArchitekten=" + adressatDesArchitekten + ", " +
                "adressnrPolier=" + adressnrPolier + ", " +
                "adressatPolier=" + adressatPolier + ", " +
                "vertragsart=" + vertragsart + ", " +
                "gewaehrleistungInMonaten=" + gewaehrleistungInMonaten + ", " +
                "buergschaftsbetrag=" + buergschaftsbetrag + ", " +
                "vertragsbedVorspann=" + vertragsbedVorspann + ", " +
                "vertragsbedNachspann=" + vertragsbedNachspann + ", " +
                "vertragsbedErlaeuterung=" + vertragsbedErlaeuterung + ", " +
                "zahlungsbedingung=" + zahlungsbedingung + ", " +
                "nachlaesse=" + nachlaesse + ", " +
                "niederlassung=" + niederlassung + ", " +
                "ergebniszuordnung=" + ergebniszuordnung + ", " +
                "sparte=" + sparte + ", " +
                "typ=" + typ + ", " +
                "gruppe=" + gruppe + ", " +
                "gruppenstufe=" + gruppenstufe + ", " +
                "gruppenstufe2=" + gruppenstufe2 + ", " +
                "gruppenstufe3=" + gruppenstufe3 + ", " +
                "gruppenstufe4=" + gruppenstufe4 + ", " +
                "gruppenstufe5=" + gruppenstufe5 + ", " +
                "statistikgruppe=" + statistikgruppe + ", " +
                "uBahnSteuerpflichtAT=" + uBahnSteuerpflichtAT + ", " +
                "gemeindecodeKommunalsteuerAT=" + gemeindecodeKommunalsteuerAT + ", " +
                "stammkostenstelleFuer=" + stammkostenstelleFuer + ", " +
                "zusammenzaehlungsart=" + zusammenzaehlungsart + ", " +
                "zusammenzaehlung=" + zusammenzaehlung + ", " +
                "misId=" + misId + ", " +
                "baubeginndatum=" + baubeginndatum + ", " +
                "voraussBauendedatum=" + voraussBauendedatum + ", " +
                "bauendedatum=" + bauendedatum + ", " +
                "bauabnahmedatum=" + bauabnahmedatum + ", " +
                "bauzeitInMonaten=" + bauzeitInMonaten + ", " +
                "drucken=" + drucken + ", " +
                "druckenNurWennBewegung=" + druckenNurWennBewegung + ", " +
                "druckenNachBauende=" + druckenNachBauende + ", " +
                "aufErgebnislistenDrucken=" + aufErgebnislistenDrucken + ", " +
                "vorjahreswertErmitteln=" + vorjahreswertErmitteln + ", " +
                "gesamtwertErmitteln=" + gesamtwertErmitteln + ", " +
                "umlageNachBauende=" + umlageNachBauende + ", " +
                "istBautraeger=" + istBautraeger + ", " +
                "schlussgerechnet=" + schlussgerechnet + ", " +
                "datumSchlussgerechnet=" + datumSchlussgerechnet + ", " +
                "abgerechnetPercent=" + abgerechnetPercent + ", " +
                "fertigstellungPercent=" + fertigstellungPercent + ", " +
                "datumFertigstellung=" + datumFertigstellung + ", " +
                "fertig=" + fertig + ", " +
                "mwStSatz=" + mwStSatz + ", " +
                "buchenMitBAS=" + buchenMitBAS + ", " +
                "buchenMitGeraet=" + buchenMitGeraet + ", " +
                "geraetekonditionIntern=" + geraetekonditionIntern + ", " +
                "geraetekonditionExtern=" + geraetekonditionExtern + ", " +
                "geraetekonditionIntLeistung=" + geraetekonditionIntLeistung + ", " +
                "geraetekonditionExtLeistung=" + geraetekonditionExtLeistung + ", " +
                "balAltIntern=" + balAltIntern + ", " +
                "balAltExtern=" + balAltExtern + ", " +
                "balPercentNeuIntern=" + balPercentNeuIntern + ", " +
                "balPercentNeuExtern=" + balPercentNeuExtern + ", " +
                "balPercentNeuRuecknahmeIntern=" + balPercentNeuRuecknahmeIntern + ", " +
                "balPercentNeuRuecknahmeExtern=" + balPercentNeuRuecknahmeExtern + ", " +
                "balPercentGebrauchtIntern=" + balPercentGebrauchtIntern + ", " +
                "balPercentGebrauchtExtern=" + balPercentGebrauchtExtern + ", " +
                "balPercentGebrauchtRuecknIntern=" + balPercentGebrauchtRuecknIntern + ", " +
                "balPercentGebrauchtRuecknExtern=" + balPercentGebrauchtRuecknExtern + ", " +
                "balPercentLadepauschaleIntern=" + balPercentLadepauschaleIntern + ", " +
                "balPercentLadepauschaleExtern=" + balPercentLadepauschaleExtern + ", " +
                "argeKostenstelle=" + argeKostenstelle + ", " +
                "fremdunternehmen=" + fremdunternehmen + ", " +
                "bauhof=" + bauhof + ", " +
                "geraetebestand=" + geraetebestand + ", " +
                "hoehenmeter=" + hoehenmeter + ", " +
                "cup=" + cup + ", " +
                "cig=" + cig + ", " +
                "kostenartMitMenge=" + kostenartMitMenge + ", " +
                "dbIIIPercentPrognose=" + dbIIIPercentPrognose + ", " +
                "einfacheFahrtzeit=" + einfacheFahrtzeit + ", " +
                "argPflichtig=" + argPflichtig + ", " +
                "wwwAdresseURL=" + wwwAdresseURL + ", " +
                "easyRapport=" + easyRapport + ", " +
                "externeGeraeteergebnisse=" + externeGeraeteergebnisse + ", " +
                "zhsLoggerbezeichnung1=" + zhsLoggerbezeichnung1 + ", " +
                "zhsLoggerbezeichnung2=" + zhsLoggerbezeichnung2 + ", " +
                "zhsAktivAufLogger=" + zhsAktivAufLogger + ", " +
                "zhsKostenstellengruppe=" + zhsKostenstellengruppe + ", " +
                "zhsUnterkostenstellen=" + zhsUnterkostenstellen + ", " +
                "zhsLohnarten=" + zhsLohnarten + ", " +
                "zhsGEO_Status=" + zhsGEO_Status + ", " +
                "zhsGEOPosition=" + zhsGEOPosition + ", " +
                "zhsStdUKST=" + zhsStdUKST + ", " +
                "gesperrt=" + gesperrt + ", " +
                "sperrhinweis=" + sperrhinweis + ", " +
                "gueltigAb=" + gueltigAb + ", " +
                "gueltigBis=" + gueltigBis + ", " +
                "neuanlagesystem=" + neuanlagesystem + ", " +
                "neuanlagebenutzer=" + neuanlagebenutzer + ", " +
                "neuanlagedatum=" + neuanlagedatum + ", " +
                "neuanlagezeit=" + neuanlagezeit + ", " +
                "aenderungssystem=" + aenderungssystem + ", " +
                "aenderungsbenutzer=" + aenderungsbenutzer + ", " +
                "aenderungsdatum=" + aenderungsdatum + ", " +
                "aenderungszeit=" + aenderungszeit + ", " +
                "mandantenfilter=" + mandantenfilter + ", " +
                "auftragsperiodenfilter=" + auftragsperiodenfilter + ']';
    }

}
