package de.systeex.cip.nevaris.bba.csv_file_producer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.bba.csv_file_producer.domain.NevarisBBACSVRecord;
import de.systeex.cip.nevaris.bba.csv_file_producer.infrastructure.BBAToNevarisBBAListProcessor;
import de.systeex.cip.nevaris.bba.csv_file_producer.infrastructure.NevarisBBACsvErrorMessageTransformer;
import de.systeex.cip.nevaris.infrastructure.ArrayListMergeAggregationStrategy;
import de.systeex.cip.types.BBARecord;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.apache.camel.component.jackson.ListJacksonDataFormat;
import org.apache.camel.dataformat.bindy.csv.BindyCsvDataFormat;
import org.apache.camel.spi.DataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class NevarisBBACsvImportFileProducer extends ErrorResistantRouteBuilder {

    @Value("${bba.file.output.path}")
    private String outputPath;
    @Value("${bba.file.output.filename}")
    private String outputFilename;
    @Value("#{${mandant.mapping}}")
    private Map<String, String> mandantMapping;

    private final String originQueueName = "nevaris-%s.csv.bba";

    @Override
    public void configure() throws Exception {
        super.configure();
        DataFormat bindyNevarisBBARecord = new BindyCsvDataFormat(NevarisBBACSVRecord.class);
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat jacksonDataFormat = new JacksonDataFormat(mapper, BBARecord.class);
        JacksonDataFormat nevarisBBARecordsList = new ListJacksonDataFormat(mapper, NevarisBBACSVRecord.class);

        mandantMapping.keySet().forEach(mandant -> {
            String queueName = String.format(originQueueName, mandantMapping.get(mandant));
            from("rabbitmq:bba-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=" + queueName + "&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapping.get(mandant) + ".csv.bba.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".csv.bba&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchBBARecordFromBroker" + mandant)
                    .removeHeaders("Camel*", "CamelRabbitmqDeliveryMode|MESSAGE_ID|DELIVERY_MODE")
                    .setHeader("NEVARIS_MANDANT", constant(mandantMapping.get(mandant)))
                    .unmarshal(jacksonDataFormat)
                    .process(new BBAToNevarisBBAListProcessor())
                    .aggregate(new ArrayListMergeAggregationStrategy()).constant(true)
                    .completionTimeout(1500L)
                    .marshal(nevarisBBARecordsList)
                    //.setHeader(DELIVERY_MODE, constant(MessageDeliveryMode.toInt(MessageDeliveryMode.PERSISTENT))) // TODO: MessageDeliveryMode von Update
                    .setHeader(DELIVERY_MODE, constant(2))
                    .to("rabbitmq:bba-anfragen-aggregated?exchangeType=topic&routingKey=" + mandant +"&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");

            from("rabbitmq:bba-anfragen-aggregated?exchangeType=topic&routingKey=" + mandant +"&queue=nevaris-" + mandantMapping.get(mandant) +".csv.bba-aggregated&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapping.get(mandant) + ".csv.bba-aggregated.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".csv.bba-aggregated&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchBBARecordAggregatedFromBroker" + mandant)
                    .unmarshal(nevarisBBARecordsList)
                    .marshal(bindyNevarisBBARecord)
                    .toD("file:" + outputPath + "/${headers.NEVARIS_MANDANT}?filename=${headers.NEVARIS_MANDANT}_${date:now:yyyyMMdd_HHmmss}_" + outputFilename);
        });
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisBBACsvErrorMessageTransformer("BBACsvImport","NEVARIS",originQueueName,mandantMapping);
    }
}
