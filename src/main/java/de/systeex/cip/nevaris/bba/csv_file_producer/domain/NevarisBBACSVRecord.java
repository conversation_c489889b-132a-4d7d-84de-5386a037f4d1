package de.systeex.cip.nevaris.bba.csv_file_producer.domain;

import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

@CsvRecord(separator = ";", generateHeaderColumns = false, quoting = true)
public class NevarisBBACSVRecord {
    @DataField(pos = 1, columnName = "Belegdatum")
    private String belegDatum;
    @DataField(pos = 2, columnName = "Belegnr.")
    private String belegNr;
    @DataField(pos = 3, columnName = "KSt belasten")
    private String KStBelasten;
    @DataField(pos = 4, columnName = "KSt entlasten")
    private String KStEntlasten;
    @DataField(pos = 5, columnName = "Betrag")
    private String betrag;
    @DataField(pos = 6, columnName = "Menge")
    private String menge;
    @DataField(pos = 7, columnName = "Einheit")
    private String einheit;
    @DataField(pos = 8, columnName = "Kostenart belasten")
    private String kostenartBelasten;
    @DataField(pos = 9, columnName = "Kostenart entlasten")
    private String kostenartEntlasten;
    @DataField(pos = 10, columnName = "Storno in Monat")
    private String stornoInMonat;
    @DataField(pos = 11, columnName = "Erweiterte Kontierung")
    private String erweiterteKontierung;
    @DataField(pos = 12, columnName = "Buchungstext")
    private String buchungstext;
    @DataField(pos = 13, columnName = "Positionsnr.")
    private String positionsNr;
    @DataField(pos = 14, columnName = "Vorgang")
    private String vorgang;
    @DataField(pos = 15, columnName = "Belgenr.2 Bedeutung")
    private String belegNr2Bedeutung;
    @DataField(pos = 16, columnName = "Belegnr.2")
    private String belegNr2;
    @DataField(pos = 17, columnName = "Dokument-ID")
    private String dokumentId;
    @DataField(pos = 18, columnName = "Mandant KSt belasten")
    private String mandantKStBelasten;
    @DataField(pos = 19, columnName = "Mandant KSt entlasten")
    private String mandantKStEntlasten;
    @DataField(pos = 20, columnName = "Artikel")
    private String artikel;
    @DataField(pos = 21, columnName = "BAL Art")
    private String balArt;
    @DataField(pos = 22, columnName = "BAL")
    private String bal;
    @DataField(pos = 23, columnName = "Mandant des Wertbezugs")
    private String mandantDesWertbezugs;
    @DataField(pos = 24, columnName = "Gerät")
    private String geraet;
    @DataField(pos = 25, columnName = "Zusatzkennung")
    private String zusatzkennung;
    @DataField(pos = 26, columnName = "Personalnr.")
    private String personalNr;
    @DataField(pos = 27, columnName = "Anlagegut")
    private String anlagegut;
    @DataField(pos = 28, columnName = "Wertbezug")
    private String wertbezug;
    @DataField(pos = 29, columnName = "BAS belasten")
    private String basBelasten;
    @DataField(pos = 30, columnName = "BAS entlasten")
    private String basEntlasten;
    @DataField(pos = 31, columnName = "Mandant KTr belasten")
    private String mandantKtrBelasten;
    @DataField(pos = 32, columnName = "Kostenträger belasten")
    private String kostentraegerBelasten;
    @DataField(pos = 33, columnName = "Mandant KTr entlasten")
    private String mandantKtrEntlasten;
    @DataField(pos = 34, columnName = "Kostenträger entlasten")
    private String kostentraegerEntlasten;
    @DataField(pos = 35, columnName = "Projekt")
    private String projekt;
    @DataField(pos = 36, columnName = "LV-Position")
    private String lvPosition;
    @DataField(pos = 37, columnName = "Debitor/Kreditor")
    private String debitorKreditor;
    @DataField(pos = 38, columnName = "Deb.-/Kred.-Nr")
    private String debKredNr;
    @DataField(pos = 39, columnName = "Mandant der Herkunft")
    private String mandantDerHerkunft;
    @DataField(pos = 40, columnName = "Herkunftsnr.")
    private String herkunftsNr;
    @DataField(pos = 41, columnName = "Transaktionsnr.")
    private String transaktionsNr;
    @DataField(pos = 42, columnName = "Neu")
    private String neu;
    @DataField(pos = 43, columnName = "Rücknahme")
    private String ruecknahme;
    @DataField(pos = 44, columnName = "Abrechnungsmonat")
    private String abrechnungsmonat;

    public NevarisBBACSVRecord() {
    }

    public String getBelegDatum() {
        return belegDatum;
    }

    public void setBelegDatum(String belegDatum) {
        this.belegDatum = belegDatum;
    }

    public String getBelegNr() {
        return belegNr;
    }

    public void setBelegNr(String belegNr) {
        this.belegNr = belegNr;
    }

    public String getKStBelasten() {
        return KStBelasten;
    }

    public void setKStBelasten(String KStBelasten) {
        this.KStBelasten = KStBelasten;
    }

    public String getKStEntlasten() {
        return KStEntlasten;
    }

    public void setKStEntlasten(String KStEntlasten) {
        this.KStEntlasten = KStEntlasten;
    }

    public String getBetrag() {
        return betrag;
    }

    public void setBetrag(String betrag) {
        this.betrag = betrag;
    }

    public String getMenge() {
        return menge;
    }

    public void setMenge(String menge) {
        this.menge = menge;
    }

    public String getEinheit() {
        return einheit;
    }

    public void setEinheit(String einheit) {
        this.einheit = einheit;
    }

    public String getKostenartBelasten() {
        return kostenartBelasten;
    }

    public void setKostenartBelasten(String kostenartBelasten) {
        this.kostenartBelasten = kostenartBelasten;
    }

    public String getKostenartEntlasten() {
        return kostenartEntlasten;
    }

    public void setKostenartEntlasten(String kostenartEntlasten) {
        this.kostenartEntlasten = kostenartEntlasten;
    }

    public String getStornoInMonat() {
        return stornoInMonat;
    }

    public void setStornoInMonat(String stornoInMonat) {
        this.stornoInMonat = stornoInMonat;
    }

    public String getErweiterteKontierung() {
        return erweiterteKontierung;
    }

    public void setErweiterteKontierung(String erweiterteKontierung) {
        this.erweiterteKontierung = erweiterteKontierung;
    }

    public String getBuchungstext() {
        return buchungstext;
    }

    public void setBuchungstext(String buchungstext) {
        this.buchungstext = buchungstext;
    }

    public String getPositionsNr() {
        return positionsNr;
    }

    public void setPositionsNr(String positionsNr) {
        this.positionsNr = positionsNr;
    }

    public String getVorgang() {
        return vorgang;
    }

    public void setVorgang(String vorgang) {
        this.vorgang = vorgang;
    }

    public String getBelegNr2Bedeutung() {
        return belegNr2Bedeutung;
    }

    public void setBelegNr2Bedeutung(String belegNr2Bedeutung) {
        this.belegNr2Bedeutung = belegNr2Bedeutung;
    }

    public String getBelegNr2() {
        return belegNr2;
    }

    public void setBelegNr2(String belegNr2) {
        this.belegNr2 = belegNr2;
    }

    public String getDokumentId() {
        return dokumentId;
    }

    public void setDokumentId(String dokumentId) {
        this.dokumentId = dokumentId;
    }

    public String getMandantKStBelasten() {
        return mandantKStBelasten;
    }

    public void setMandantKStBelasten(String mandantKStBelasten) {
        this.mandantKStBelasten = mandantKStBelasten;
    }

    public String getMandantKStEntlasten() {
        return mandantKStEntlasten;
    }

    public void setMandantKStEntlasten(String mandantKStEntlasten) {
        this.mandantKStEntlasten = mandantKStEntlasten;
    }

    public String getArtikel() {
        return artikel;
    }

    public void setArtikel(String artikel) {
        this.artikel = artikel;
    }

    public String getBalArt() {
        return balArt;
    }

    public void setBalArt(String balArt) {
        this.balArt = balArt;
    }

    public String getBal() {
        return bal;
    }

    public void setBal(String bal) {
        this.bal = bal;
    }

    public String getMandantDesWertbezugs() {
        return mandantDesWertbezugs;
    }

    public void setMandantDesWertbezugs(String mandantDesWertbezugs) {
        this.mandantDesWertbezugs = mandantDesWertbezugs;
    }

    public String getGeraet() {
        return geraet;
    }

    public void setGeraet(String geraet) {
        this.geraet = geraet;
    }

    public String getZusatzkennung() {
        return zusatzkennung;
    }

    public void setZusatzkennung(String zusatzkennung) {
        this.zusatzkennung = zusatzkennung;
    }

    public String getPersonalNr() {
        return personalNr;
    }

    public void setPersonalNr(String personalNr) {
        this.personalNr = personalNr;
    }

    public String getAnlagegut() {
        return anlagegut;
    }

    public void setAnlagegut(String anlagegut) {
        this.anlagegut = anlagegut;
    }

    public String getWertbezug() {
        return wertbezug;
    }

    public void setWertbezug(String wertbezug) {
        this.wertbezug = wertbezug;
    }

    public String getBasBelasten() {
        return basBelasten;
    }

    public void setBasBelasten(String basBelasten) {
        this.basBelasten = basBelasten;
    }

    public String getBasEntlasten() {
        return basEntlasten;
    }

    public void setBasEntlasten(String basEntlasten) {
        this.basEntlasten = basEntlasten;
    }

    public String getMandantKtrBelasten() {
        return mandantKtrBelasten;
    }

    public void setMandantKtrBelasten(String mandantKtrBelasten) {
        this.mandantKtrBelasten = mandantKtrBelasten;
    }

    public String getKostentraegerBelasten() {
        return kostentraegerBelasten;
    }

    public void setKostentraegerBelasten(String kostentraegerBelasten) {
        this.kostentraegerBelasten = kostentraegerBelasten;
    }

    public String getMandantKtrEntlasten() {
        return mandantKtrEntlasten;
    }

    public void setMandantKtrEntlasten(String mandantKtrEntlasten) {
        this.mandantKtrEntlasten = mandantKtrEntlasten;
    }

    public String getKostentraegerEntlasten() {
        return kostentraegerEntlasten;
    }

    public void setKostentraegerEntlasten(String kostentraegerEntlasten) {
        this.kostentraegerEntlasten = kostentraegerEntlasten;
    }

    public String getProjekt() {
        return projekt;
    }

    public void setProjekt(String projekt) {
        this.projekt = projekt;
    }

    public String getLvPosition() {
        return lvPosition;
    }

    public void setLvPosition(String lvPosition) {
        this.lvPosition = lvPosition;
    }

    public String getDebitorKreditor() {
        return debitorKreditor;
    }

    public void setDebitorKreditor(String debitorKreditor) {
        this.debitorKreditor = debitorKreditor;
    }

    public String getDebKredNr() {
        return debKredNr;
    }

    public void setDebKredNr(String debKredNr) {
        this.debKredNr = debKredNr;
    }

    public String getMandantDerHerkunft() {
        return mandantDerHerkunft;
    }

    public void setMandantDerHerkunft(String mandantDerHerkunft) {
        this.mandantDerHerkunft = mandantDerHerkunft;
    }

    public String getHerkunftsNr() {
        return herkunftsNr;
    }

    public void setHerkunftsNr(String herkunftsNr) {
        this.herkunftsNr = herkunftsNr;
    }

    public String getTransaktionsNr() {
        return transaktionsNr;
    }

    public void setTransaktionsNr(String transaktionsNr) {
        this.transaktionsNr = transaktionsNr;
    }

    public String getNeu() {
        return neu;
    }

    public void setNeu(String neu) {
        this.neu = neu;
    }

    public String getRuecknahme() {
        return ruecknahme;
    }

    public void setRuecknahme(String ruecknahme) {
        this.ruecknahme = ruecknahme;
    }

    public String getAbrechnungsmonat() {
        return abrechnungsmonat;
    }

    public void setAbrechnungsmonat(String abrechnungsmonat) {
        this.abrechnungsmonat = abrechnungsmonat;
    }
}
