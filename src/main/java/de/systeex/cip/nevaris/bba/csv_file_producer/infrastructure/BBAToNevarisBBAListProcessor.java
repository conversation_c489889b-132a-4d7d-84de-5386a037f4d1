package de.systeex.cip.nevaris.bba.csv_file_producer.infrastructure;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.time.format.DateTimeFormatter;
import java.util.*;
import de.systeex.cip.types.BBARecord;
import de.systeex.cip.nevaris.bba.csv_file_producer.domain.NevarisBBACSVRecord;

public class BBAToNevarisBBAListProcessor implements Processor {

    private static final Map<String, String> EINHEIT_LOOKUP = new HashMap<>();
    static {
        EINHEIT_LOOKUP.put("Std", "STD");
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        BBARecord logabbaRecordLine = exchange.getIn().getBody(BBARecord.class);
        List<NevarisBBACSVRecord> nevarisBBARecordList = new ArrayList<>();

        DateTimeFormatter nevarisDateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yy");
        DecimalFormat nevarisDecimalFormatter = new DecimalFormat("#,##0.00", new DecimalFormatSymbols(Locale.GERMAN));
        DecimalFormat nevarisMengeDecimalFormatter = new DecimalFormat("#,##0.000", new DecimalFormatSymbols(Locale.GERMAN));

        NevarisBBACSVRecord nevarisBBARecord = new NevarisBBACSVRecord();
        nevarisBBARecord.setBelegDatum(Objects.requireNonNull(logabbaRecordLine.bdatum()).format(nevarisDateTimeFormatter));
        nevarisBBARecord.setBelegNr(logabbaRecordLine.bnr());
        nevarisBBARecord.setKStBelasten(logabbaRecordLine.kst());
        nevarisBBARecord.setKStEntlasten(logabbaRecordLine.gkst());
        nevarisBBARecord.setBetrag(nevarisDecimalFormatter.format(logabbaRecordLine.betrag()));
        nevarisBBARecord.setMenge(nevarisMengeDecimalFormatter.format(logabbaRecordLine.menge()));
        nevarisBBARecord.setEinheit(EINHEIT_LOOKUP.get(logabbaRecordLine.me()));
        nevarisBBARecord.setKostenartBelasten(logabbaRecordLine.koa());
        nevarisBBARecord.setKostenartEntlasten(logabbaRecordLine.koa());
        nevarisBBARecord.setStornoInMonat("");
        nevarisBBARecord.setErweiterteKontierung("");
        nevarisBBARecord.setBuchungstext(logabbaRecordLine.text());
        nevarisBBARecord.setPositionsNr("");
        nevarisBBARecord.setVorgang("");
        nevarisBBARecord.setBelegNr2Bedeutung("");
        nevarisBBARecord.setBelegNr2("");
        nevarisBBARecord.setDokumentId("");
        nevarisBBARecord.setMandantKStBelasten(logabbaRecordLine.nevarisMandantNr());
        nevarisBBARecord.setMandantKStEntlasten(logabbaRecordLine.nevarisMandantNr());
        nevarisBBARecord.setArtikel("");
        nevarisBBARecord.setBalArt("");
        nevarisBBARecord.setBal("");
        nevarisBBARecord.setMandantDesWertbezugs("");
        nevarisBBARecord.setGeraet("");
        nevarisBBARecord.setZusatzkennung("");
        nevarisBBARecord.setPersonalNr("");
        nevarisBBARecord.setAnlagegut("");
        nevarisBBARecord.setWertbezug("");
        nevarisBBARecord.setBasBelasten("");
        nevarisBBARecord.setBasEntlasten("");
        nevarisBBARecord.setMandantKtrBelasten("");
        nevarisBBARecord.setKostentraegerBelasten("");
        nevarisBBARecord.setMandantKtrEntlasten("");
        nevarisBBARecord.setKostentraegerEntlasten("");
        nevarisBBARecord.setProjekt("");
        nevarisBBARecord.setLvPosition("");
        nevarisBBARecord.setDebitorKreditor("");
        nevarisBBARecord.setDebKredNr("");
        nevarisBBARecord.setMandantDerHerkunft("");
        nevarisBBARecord.setHerkunftsNr("");
        nevarisBBARecord.setTransaktionsNr("");
        nevarisBBARecord.setNeu("");
        nevarisBBARecord.setRuecknahme("");
        nevarisBBARecord.setAbrechnungsmonat(Objects.requireNonNull(logabbaRecordLine.monat()).format(nevarisDateTimeFormatter));

        nevarisBBARecordList.add(nevarisBBARecord);

        exchange.getIn().setBody(nevarisBBARecordList);
    }
}
