package de.systeex.cip.nevaris;

import com.rabbitmq.client.ConnectionFactory;
import de.systeex.cip.nevaris.infrastructure.NevarisODataV4Service;
import de.systeex.cip.nevaris.application.NevarisService;
import org.apache.http.HttpEntity;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.entity.BufferedHttpEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class Config {

    @Value("${nevaris.username}")
    private String ntlmAddressUsername;
    @Value("${nevaris.password}")
    private String ntlmAddressPassword;

    @Value("${message.broker.hostname}")
    private String messagebrokerHost;
    @Value("${message.broker.port}")
    private int messagebrokerPort;
    @Value("${message.broker.username}")
    private String messagebrokerUsername;
    @Value("${message.broker.password}")
    private String messagebrokerPassword;

    @Value("${nevaris.host}")
    private String nevarisHost;


    @Value("${version}")
    private String version;

    @Bean(name = "nevarisODataHttpClientBuilder")
    public HttpClientBuilder nevarisODataHttpClientBuilder() {
        CredentialsProvider credsProvider = new BasicCredentialsProvider();
        credsProvider.setCredentials(AuthScope.ANY, new NTCredentials(ntlmAddressUsername, ntlmAddressPassword, "", ""));
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        httpClientBuilder.setDefaultCredentialsProvider(credsProvider);
        httpClientBuilder.addInterceptorFirst((HttpRequestInterceptor) (request, context) -> {
            if (request instanceof HttpEntityEnclosingRequest entityRequest) {
                HttpEntity entity = entityRequest.getEntity();
                if (entity != null && !entity.isRepeatable()) {
                    entityRequest.setEntity(new BufferedHttpEntity(entity));
                }
            }
        });

        return httpClientBuilder;
    }

    @Bean(name = "rabbitConnectionFactoryConfig")
    public ConnectionFactory rabbitConnectionFactoryConfig() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(messagebrokerHost);
        factory.setPort(messagebrokerPort);
        factory.setUsername(messagebrokerUsername);
        factory.setPassword(messagebrokerPassword);
        factory.setClientProperties(Map.of("product", "nevaris", "version", version));
        return factory;
    }

    @Bean(name = "nevarisService")
    public NevarisService nevarisService() {
        return new NevarisODataV4Service(nevarisHost,ntlmAddressUsername, ntlmAddressPassword, "", "");
    }
}
