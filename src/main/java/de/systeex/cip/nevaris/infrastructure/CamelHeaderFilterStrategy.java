package de.systeex.cip.nevaris.infrastructure;

import org.apache.camel.Exchange;
import org.apache.camel.spi.HeaderFilterStrategy;

public class CamelHeaderFilterStrategy implements HeaderFilterStrategy {

    private final String[] exactHeadersToFilter;
    private final String[] startsWithHeadersToFilter;

    public CamelHeaderFilterStrategy(String[] exactHeadersToFilter, String[] startsWithHeadersToFilter) {
        this.exactHeadersToFilter = exactHeadersToFilter;
        this.startsWithHeadersToFilter = startsWithHeadersToFilter;
    }

    @Override
    public boolean applyFilterToCamelHeaders(String headerName, Object headerValue, Exchange exchange) {
        return false;
    }

    @Override
    public boolean applyFilterToExternalHeaders(String headerName, Object headerValue, Exchange exchange) {
        for(String header: exactHeadersToFilter) {
            if (header.equalsIgnoreCase(headerName)) {
                return true;
            }
        }
        for(String header: startsWithHeadersToFilter) {
            if (headerName.toLowerCase().startsWith(header.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
}
