package de.systeex.cip.nevaris.infrastructure;

import org.apache.camel.AggregationStrategy;
import org.apache.camel.Exchange;

import java.util.ArrayList;
import java.util.List;

public class ArrayListMergeAggregationStrategy implements AggregationStrategy {

    public Exchange aggregate(Exchange oldExchange, Exchange newExchange) {
        List<Object> newBody = newExchange.getIn().getBody(List.class);
        ArrayList<Object> list = null;
        if (oldExchange == null) {
            list = new ArrayList<>(newBody);
            newExchange.getIn().setBody(list);
            return newExchange;
        } else {
            list = oldExchange.getIn().getBody(ArrayList.class);
            list.addAll(newBody);
            return oldExchange;
        }
    }
}