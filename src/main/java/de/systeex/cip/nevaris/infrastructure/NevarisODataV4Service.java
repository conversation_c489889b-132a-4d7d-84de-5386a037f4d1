package de.systeex.cip.nevaris.infrastructure;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.systeex.cip.nevaris.adressen.json_producer.NevarisAdressuebersicht;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCodeList;
import de.systeex.cip.nevaris.application.NevarisService;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenterList;

public class NevarisODataV4Service implements NevarisService {

    private final String nevarisHost;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String username;
    private final String password;
    private final String workstation;
    private final String domain;

    public NevarisODataV4Service(String nevarisHost, String username, String password, String workstation, String domain) {
        this.nevarisHost = nevarisHost;
        this.username = username;
        this.password = password;
        this.workstation = workstation;
        this.domain = domain;
    }

    @Override
    public NevarisPLZCodeList getPLZCodes(String mandant, String queryString) {
        HttpClient httpClient = this.getHttpClient();
        try {
            String url = "http://" + nevarisHost + "/INTEGRATION/ODataV4/Company('" + mandant + "')/PLZCodes";
            if (queryString != null && !queryString.isEmpty()) {
                url += queryString;
            }
            String responseBody = httpClient.get(url, null);
            return objectMapper.readValue(responseBody, NevarisPLZCodeList.class);
        } catch (Exception e) {
            System.out.println("Error while getting PLZ codes from Nevaris: " + e.getMessage());
        }
        return null;
    }

    @Override
    public NevarisCostCenterList getKostenstellen(String mandant, String queryString) {
        HttpClient httpClient = this.getHttpClient();
        try {
            String url = "http://" + nevarisHost + "/INTEGRATION/ODataV4/Company('" + mandant + "')/Kostenstellenliste";
            if (queryString != null && !queryString.isEmpty()) {
                url += queryString;
            }
            String responseBody = httpClient.get(url, null);
            return objectMapper.readValue(responseBody, NevarisCostCenterList.class);
        } catch (Exception e) {
            System.out.println("Error while getting Kostenstellen from Nevaris: " + e.getMessage());
        }
        return null;
    }

    @Override
    public NevarisAdressuebersicht getAdressuebersichtByAdressnr(String mandant, String unique) {
        HttpClient httpClient = this.getHttpClient();
        try {
            String url = "http://" + nevarisHost + "/INTEGRATION/ODataV4/Company('" + mandant + "')/adressuebersicht('" + unique + "')";
            String responseBody = httpClient.get(url, null);
            return objectMapper.readValue(responseBody, NevarisAdressuebersicht.class);
        } catch (Exception e) {
            System.out.println("Error while getting Adressuebersicht from Nevaris: " + e.getMessage());
        }
        return null;
    }

    private HttpClient getHttpClient() {
        return new HttpClient(this.username, this.password, this.workstation, this.domain);
    }
}
