package de.systeex.cip.nevaris.infrastructure;

import org.apache.camel.builder.RouteBuilder;
import org.springframework.stereotype.Component;

@Component
public class SaveLastTimestampToDatabaseRoute extends RouteBuilder {

    @Override
    public void configure() throws Exception {
        from("direct:saveLastTimestamp")
                .toD("sql:UPDATE consumer_timestamps SET updated = '${headers.CURRENT_DATE}', timestamp = '${headers.CURRENT_TIMESTAMP}', last_key = '${headers.LAST_KEY}' WHERE Entity = '${headers.NEVARIS_ENTITY}' AND mandant = '${headers.MANDANT}'");
    }
}
