package de.systeex.cip.nevaris.infrastructure;

import de.systeex.cip.types.ErrSrcType;
import systeex.cip.error_handler.ErrorMessageTransformer;

import java.util.Map;

public class NevarisGetLastTimestampFromDatabaseErrorMessageTransformer extends ErrorMessageTransformer {
    public NevarisGetLastTimestampFromDatabaseErrorMessageTransformer(String module, String systemName, String originQueue,  Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.INTERNAL;
    }
}
