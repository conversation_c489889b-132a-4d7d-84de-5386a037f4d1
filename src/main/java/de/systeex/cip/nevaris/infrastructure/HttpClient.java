package de.systeex.cip.nevaris.infrastructure;

import de.systeex.cip.nevaris.domain.exceptions.ResourceNotFoundException;
import org.apache.http.Consts;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class HttpClient {

    private final BasicCredentialsProvider credentialsProvider;

    public HttpClient(String username, String password, String workstation, String domain) {
        Credentials credentials = new NTCredentials(username, password, workstation, domain);
        this.credentialsProvider = new BasicCredentialsProvider();
        this.credentialsProvider.setCredentials(AuthScope.ANY, credentials);
    }

    public String get(String url, Map<String, String> headers) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .build()) {

            URL urlObject = new URL(url);
            URI uri = new URI(urlObject.getProtocol(), urlObject.getUserInfo(), urlObject.getHost(), urlObject.getPort(), urlObject.getPath(), urlObject.getQuery(), urlObject.getRef());
            HttpGet httpGet = new HttpGet(uri.toASCIIString());
            if (headers != null) {
                headers.forEach(httpGet::addHeader);
            }
            CloseableHttpResponse response = httpClient.execute(httpGet);

            switch (response.getStatusLine().getStatusCode()) {
                case 200:
                    return EntityUtils.toString(response.getEntity());
                case 404:
                    throw new ResourceNotFoundException("Resource not found: " + url);
                default:
                    throw new Exception("Unexpected status code: " + response.getStatusLine().getStatusCode() + " - " + EntityUtils.toString(response.getEntity()));
            }
        }
    }

    public String delete(
            String url,
            Map<String, String> headers
    ) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .build()) {

            URL urlObject = new URL(url);
            URI uri = new URI(urlObject.getProtocol(), urlObject.getUserInfo(), urlObject.getHost(), urlObject.getPort(), urlObject.getPath(), urlObject.getQuery(), urlObject.getRef());
            HttpDelete httpDelete = new HttpDelete(uri.toASCIIString());
            if (headers != null) {
                headers.forEach(httpDelete::addHeader);
            }
            CloseableHttpResponse response = httpClient.execute(httpDelete);

            switch (response.getStatusLine().getStatusCode()) {
                case 200:
                    return EntityUtils.toString(response.getEntity());
                case 404:
                    throw new ResourceNotFoundException("Resource not found: " + url);
                default:
                    throw new Exception("Unexpected status code: " + response.getStatusLine().getStatusCode() + " - " + EntityUtils.toString(response.getEntity()));
            }
        }
    }

    public String post(
            String url,
            String payload,
            Map<String, String> headers
    ) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .build()) {

            URL urlObject = new URL(url);
            URI uri = new URI(urlObject.getProtocol(), urlObject.getUserInfo(), urlObject.getHost(), urlObject.getPort(), urlObject.getPath(), urlObject.getQuery(), urlObject.getRef());
            HttpPost httpPost = new HttpPost(uri.toASCIIString());
            if (payload != null) {
                httpPost.setEntity(new StringEntity(payload, Consts.UTF_8));
            }
            if (headers != null) {
                headers.forEach(httpPost::addHeader);
            }
            CloseableHttpResponse response = httpClient.execute(httpPost);

            switch (response.getStatusLine().getStatusCode()) {
                case 200:
                    return EntityUtils.toString(response.getEntity());
                case 404:
                    throw new ResourceNotFoundException("Resource not found: " + url);
                default:
                    throw new Exception("Unexpected status code: " + response.getStatusLine().getStatusCode() + " - " + EntityUtils.toString(response.getEntity()));
            }
        }
    }

    public String put(
            String url,
            String payload,
            Map<String, String> headers
    ) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .setConnectionTimeToLive(15, TimeUnit.SECONDS)
                .build()) {

            URL urlObject = new URL(url);
            URI uri = new URI(urlObject.getProtocol(), urlObject.getUserInfo(), urlObject.getHost(), urlObject.getPort(), urlObject.getPath(), urlObject.getQuery(), urlObject.getRef());
            HttpPut httpPut = new HttpPut(uri.toASCIIString());
            if (payload != null) {
                httpPut.setEntity(new StringEntity(payload, Consts.UTF_8));
            }
            if (headers != null) {
                headers.forEach(httpPut::addHeader);
            }
            CloseableHttpResponse response = httpClient.execute(httpPut);

            switch (response.getStatusLine().getStatusCode()) {
                case 200:
                    return EntityUtils.toString(response.getEntity());
                case 404:
                    throw new ResourceNotFoundException("Resource not found: " + url);
                default:
                    throw new Exception("Unexpected status code: " + response.getStatusLine().getStatusCode() + " - " + EntityUtils.toString(response.getEntity()));
            }
        }
    }
}
