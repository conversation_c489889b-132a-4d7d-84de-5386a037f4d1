package de.systeex.cip.nevaris.infrastructure;

import de.systeex.cip.nevaris.domain.odata.NevarisOdataTimedClass;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class NevarisConsumerAlreadySeenProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        final NevarisDateTimeFormatter nevarisDateTimeFormatter = NevarisDateTimeFormatter.getInstance();
        final DateTimeFormatter nevarisDateFormatter = nevarisDateTimeFormatter.getNevarisDateFormatter();
        final DateTimeFormatter nevarisTimeFormatter = nevarisDateTimeFormatter.getNevarisTimeFormatter();
        final DateTimeFormatter pgSQLDateTimeFormatter = nevarisDateTimeFormatter.getPgSQLDateTimeFormatter();
        final DateTimeFormatter odataDateTimeFormatter = nevarisDateTimeFormatter.getOdataDateTimeFormatter();

        NevarisOdataTimedClass nevarisOdataTimedClass = exchange.getIn().getBody(NevarisOdataTimedClass.class);
        String uuid = nevarisOdataTimedClass.getUUID();

        LocalTime localTime = LocalTime.parse(nevarisOdataTimedClass.aenderungszeit(), nevarisTimeFormatter);
        LocalDate localDate = LocalDate.parse(nevarisOdataTimedClass.aenderungsdatum(), nevarisDateFormatter);
        LocalDate lastLocalDate = LocalDate.parse(String.valueOf(exchange.getIn().getHeader("DATE_PART")), odataDateTimeFormatter);
        //12:13:43.926 -> 44023926
        long milliSeconds = (long) (localTime.getHour() * 3_600_000) + (long) localTime.getMinute() * 60_000 + (long) localTime.getSecond() * 1_000 + (long) (localTime.getNano() / 1_000_000);
        if (localDate.isBefore(lastLocalDate) ||
                (localDate.isEqual(lastLocalDate) && milliSeconds < Integer.parseInt(String.valueOf(exchange.getIn().getHeader("TIME_PART_MILLI_SECONDS")))) ||
                (localDate.isEqual(lastLocalDate) && (milliSeconds == Integer.parseInt(String.valueOf(exchange.getIn().getHeader("TIME_PART_MILLI_SECONDS")))) && uuid.compareTo(Objects.toString(exchange.getIn().getHeader("LAST_KEY"), "")) <= 0)) {
            exchange.getIn().setHeader("ALREADY_SEEN", "true");
            LocalDateTime localDateTime = LocalDateTime.now();
            exchange.getIn().setHeader("CURRENT_DATE", localDateTime.format(pgSQLDateTimeFormatter));
            exchange.getIn().setHeader("LAST_KEY", uuid);
            localDate = LocalDate.parse(nevarisOdataTimedClass.aenderungsdatum(), nevarisDateFormatter);
            localDateTime = LocalDateTime.of(localDate, localTime);
            exchange.getIn().setHeader("CURRENT_TIMESTAMP", localDateTime.format(pgSQLDateTimeFormatter));
        } else {
            exchange.getIn().setHeader("ALREADY_SEEN", "false");
            LocalDateTime localDateTime = LocalDateTime.now();
            exchange.getIn().setHeader("CURRENT_DATE", localDateTime.format(pgSQLDateTimeFormatter));
            exchange.getIn().setHeader("LAST_KEY", uuid);
            localDate = LocalDate.parse(nevarisOdataTimedClass.aenderungsdatum(), nevarisDateFormatter);
            localDateTime = LocalDateTime.of(localDate, localTime);
            exchange.getIn().setHeader("CURRENT_TIMESTAMP", localDateTime.format(pgSQLDateTimeFormatter));
        }
    }
}
