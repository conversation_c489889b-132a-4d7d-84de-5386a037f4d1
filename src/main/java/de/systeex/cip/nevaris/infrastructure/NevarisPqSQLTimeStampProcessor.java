package de.systeex.cip.nevaris.infrastructure;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class NevarisPqSQLTimeStampProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        final NevarisDateTimeFormatter nevarisDateTimeFormatter = NevarisDateTimeFormatter.getInstance();
        final DateTimeFormatter pgSQLDateTimeFormatter = nevarisDateTimeFormatter.getPgSQLDateTimeFormatter();
        final DateTimeFormatter odataDateTimeFormatter = nevarisDateTimeFormatter.getOdataDateTimeFormatter();
        Map<String, Object> map = exchange.getIn().getBody(Map.class);
        LocalDateTime dateTime = LocalDateTime.parse(String.valueOf(map.get("timestamp")), pgSQLDateTimeFormatter);
        long milliSeconds = (long) (dateTime.getHour() * 3_600_000) + (long) dateTime.getMinute() * 60_000 + (long) dateTime.getSecond() * 1_000 + (long) (dateTime.getNano() / 1_000_000);
        LocalDate date = dateTime.toLocalDate();
        exchange.getIn().setHeader("TIMESTAMP", map.get("timestamp"));
        exchange.getIn().setHeader("TIME_PART_MILLI_SECONDS", milliSeconds);
        exchange.getIn().setHeader("LAST_KEY", map.get("last_key"));
        exchange.getIn().setHeader("DATE_PART", date.format(odataDateTimeFormatter));
        exchange.getIn().setBody(null);
    }
}
