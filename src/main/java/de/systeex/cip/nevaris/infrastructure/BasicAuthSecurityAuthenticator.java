package de.systeex.cip.nevaris.infrastructure;

import com.sun.security.auth.UserPrincipal;
import org.apache.camel.component.netty.http.HttpPrincipal;
import org.apache.camel.component.netty.http.SecurityAuthenticator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.security.auth.Subject;
import javax.security.auth.login.LoginException;


public class BasicAuthSecurityAuthenticator implements SecurityAuthenticator {
    private static final Logger LOG = LoggerFactory.getLogger(BasicAuthSecurityAuthenticator.class);

    private String apiUsername;
    private String apiPassword;
    private String name;

    public BasicAuthSecurityAuthenticator() {
    }

    public BasicAuthSecurityAuthenticator(String apiUsername, String apiPassword, String name) {
        this.apiUsername = apiUsername;
        this.apiPassword = apiPassword;
        this.name = name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setRoleClassNames(String names) {

    }

    @Override
    public Subject login(HttpPrincipal principal) throws LoginException {
        String username = principal.getUsername();
        String password = principal.getPassword();

        // Implement your authentication logic here
        if (this.apiUsername.equals(username) && this.apiPassword.equals(password)) {
            // Return an authenticated Subject with user's principal
            return createAuthenticatedSubject(username);
        } else {
            throw new LoginException("Authentication failed");
        }
    }

    @Override
    public void logout(Subject subject) throws LoginException {

    }

    @Override
    public String getUserRoles(Subject subject) {
        return null;
    }

    private Subject createAuthenticatedSubject(String username) {
        // Create a Subject with user's principal
        Subject subject = new Subject();
        subject.getPrincipals().add(new UserPrincipal(username));
        return subject;
    }
}
