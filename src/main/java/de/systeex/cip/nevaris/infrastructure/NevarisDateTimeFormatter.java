package de.systeex.cip.nevaris.infrastructure;

import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;

public final class NevarisDateTimeFormatter {
    private  static NevarisDateTimeFormatter instance;
    private final java.time.format.DateTimeFormatter nevarisDateFormatter;
    private final java.time.format.DateTimeFormatter nevarisTimeFormatter;
    private final java.time.format.DateTimeFormatter pgSQLDateTimeFormatter;
    private final java.time.format.DateTimeFormatter odataDateTimeFormatter;

    private NevarisDateTimeFormatter() {
        nevarisDateFormatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
        nevarisTimeFormatter = new DateTimeFormatterBuilder()
                .appendPattern("HH:mm:ss")
                .appendFraction(ChronoField.MICRO_OF_SECOND, 0, 6, true)
                .toFormatter();
        pgSQLDateTimeFormatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy-MM-dd HH:mm:ss")
                .appendFraction(ChronoField.MICRO_OF_SECOND, 0, 6, true)
                .toFormatter();
        odataDateTimeFormatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
    }

    public static NevarisDateTimeFormatter getInstance() {
        if (instance == null) {
            instance = new NevarisDateTimeFormatter();
        }
        return instance;
    }

    public java.time.format.DateTimeFormatter getNevarisDateFormatter() {
        return nevarisDateFormatter;
    }

    public java.time.format.DateTimeFormatter getNevarisTimeFormatter() {
        return nevarisTimeFormatter;
    }

    public java.time.format.DateTimeFormatter getPgSQLDateTimeFormatter() {
        return pgSQLDateTimeFormatter;
    }

    public java.time.format.DateTimeFormatter getOdataDateTimeFormatter() {
        return odataDateTimeFormatter;
    }

}

