package de.systeex.cip.nevaris.bauleiteruebersicht.infrastructure;

import de.systeex.cip.nevaris.bauleiteruebersicht.domain.Bauleiteruebersicht;
import de.systeex.cip.types.Mitarbeiter;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

public class MitarbeiterToBauleiteruebersichtProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        Mitarbeiter mitarbeiter = exchange.getIn().getBody(Mitarbeiter.class);

        Bauleiteruebersicht bauleiteruebersicht = new Bauleiteruebersicht(
            null,
                null,
                this.truncate(exchange.getIn().getHeader("BAULEITER_KEY", String.class), 10),
                this.truncate(mitarbeiter.vorname() + " " + mitarbeiter.name(), 50),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );

        exchange.getIn().setBody(bauleiteruebersicht);
    }

    private String truncate(String value, int length) {
        if (value == null)
            return null;

        return value.substring(0, Math.min(value.length(), length));
    }
}
