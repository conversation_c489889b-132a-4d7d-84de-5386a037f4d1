package de.systeex.cip.nevaris.bauleiteruebersicht.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.bauleiteruebersicht.domain.Bauleiteruebersicht;
import de.systeex.cip.nevaris.bauleiteruebersicht.infrastructure.MitarbeiterToBauleiteruebersichtProcessor;
import de.systeex.cip.nevaris.bauleiteruebersicht.infrastructure.NevarisBrokerToBauleiteruebersichtErrorMessageTransformer;
import de.systeex.cip.types.Mitarbeiter;
import org.apache.camel.Exchange;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.http.HttpConstants;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.Map;
import java.util.Objects;

@Component
public class BrokerToBauleiteruebersichtRoute extends ErrorResistantRouteBuilder {

    @Value("#{${mandant.mapping}}")
    private Map<String, String> mandantMapToNevaris;

    @Value("${nevaris.host}")
    private String nevarisHost;

    @Value("${nevaris.default.basepath}")
    private String basePath;
    private final String NEVARIS_FUNKTION_OBERBAULEITER = "TL";
    private final String NEVARIS_FUNKTION_BAULEITER = "PL";

    private final Predicate isBauleiteruebersicht = PredicateBuilder.or(
            PredicateBuilder.isEqualTo(simple("${body.zielFunktion}"), constant(NEVARIS_FUNKTION_OBERBAULEITER)),
            PredicateBuilder.isEqualTo(simple("${body.zielFunktion}"), constant(NEVARIS_FUNKTION_BAULEITER)),
            PredicateBuilder.isEqualTo(simple("${body.isBauleiter}"), constant("true"))
    );
    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final Predicate httpStatusNOk = PredicateBuilder.or(PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));

    private final Predicate createBauleiteruebersicht = PredicateBuilder.isNull(header("BAULEITER_E_TAG"));

    private final String originQueueName = "nevaris-%s.bauleiter";

    @Override
    public void configure() throws Exception {
        super.configure();
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat jacksonDataFormatMitarbeiter = new JacksonDataFormat(mapper, Mitarbeiter.class);
        JacksonDataFormat jacksonDataFormatBauleiter = new JacksonDataFormat(mapper, Bauleiteruebersicht.class);
        JacksonDataFormat jacksonDataFormatBauleiteruebersicht = new JacksonDataFormat(mapper, Bauleiteruebersicht.class);

        mandantMapToNevaris.keySet().forEach(mandant -> {
            String queueName = String.format(originQueueName, mandantMapToNevaris.get(mandant));
            from("rabbitmq:mitarbeiterdaten-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=" + queueName + "&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapToNevaris.get(mandant) + ".bauleiter.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".bauleiter&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchBauleiterFromBroker" + mandantMapToNevaris.get(mandant))
                    .unmarshal(jacksonDataFormatMitarbeiter)
                    .choice()
                    .when(isBauleiteruebersicht)
                    .log("Bauleiter fromRabbit: ${body}")
                    .process(exchange -> {
                        Mitarbeiter mitarbeiter = exchange.getIn().getBody(Mitarbeiter.class);
                        exchange.getIn().setHeader("NEVARIS_MANDANT", mandantMapToNevaris.get(mandant));
                        exchange.getIn().setHeader("BAULEITER_KEY", this.getBauleiterKey(mitarbeiter));
                    })
                    .enrich("direct:getNevarisBauleiter", (oldExchange, newExchange) -> {
                        Bauleiteruebersicht bauleiteruebersicht = newExchange.getIn().getBody(Bauleiteruebersicht.class);
                        if (bauleiteruebersicht != null) {
                            oldExchange.getIn().setHeader("BAULEITER_E_TAG", bauleiteruebersicht.odataEtag());
                        }
                        oldExchange.getIn().setHeader("failedBecause", newExchange.getIn().getHeader("failedBecause"));
                        return oldExchange;
                    }, false, true)
                    .filter(header("failedBecause").isNull())
                    .process(new MitarbeiterToBauleiteruebersichtProcessor())
                    .marshal(jacksonDataFormatBauleiter)
                    .convertBodyTo(String.class, "UTF-8")
                    .choice()
                    .when(createBauleiteruebersicht)
                        .enrich("direct:createNevarisBauleiter", (oldExchange, newExchange) -> oldExchange, false, true)
                    .otherwise()
                        .enrich("direct:updateNevarisBauleiter", (oldExchange, newExchange) -> oldExchange, false, true)
                    .endChoice()
                    .end();
        });

        from("direct:getNevarisBauleiter")
                .routeId("getNevarisBauleiter")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .toD("http://" + nevarisHost + basePath + "/Company('${headers.NEVARIS_MANDANT}')/Bauleiterübersicht('${headers.BAULEITER_KEY}')?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=GET&throwExceptionOnFailure=false")
                .streamCaching()
                .to("log:getNevarisBauleiter?showHeaders=true")
                .choice()
                    .when(httpStatusOk)
                        .unmarshal(jacksonDataFormatBauleiteruebersicht)
                .end();

        from("direct:createNevarisBauleiter")
                .routeId("createNevarisBauleiter")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .to("log:createNevarisBauleiter?showHeaders=true")
                .toD("http://" + nevarisHost + basePath + "/Company('${headers.NEVARIS_MANDANT}')/Bauleiterübersicht?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=POST&throwExceptionOnFailure=false")
                .streamCaching()
                .to("log:createNevarisBauleiter?showHeaders=true")
                .choice()
                    .when(httpStatusNOk)
                        .throwException(Exception.class, "Fehler beim Anlegen des Bauleiters: BauleiterKey: ${headers.BAULEITER_KEY}, Headers: ${headers}, Body: ${body}")
                .end()
                .unmarshal(jacksonDataFormatBauleiteruebersicht);

        from("direct:updateNevarisBauleiter")
                .routeId("updateNevarisBauleiter")
                .setHeader("If-Match", header("BAULEITER_E_TAG"))
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .to("log:updateNevarisBauleiter?showHeaders=true")
                .toD("http://" + nevarisHost + basePath + "/Company('${headers.NEVARIS_MANDANT}')/Bauleiterübersicht('${headers.BAULEITER_KEY}')?clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=PATCH&throwExceptionOnFailure=false")
                .convertBodyTo(String.class, "UTF-8")
                .to("log:updateNevarisBauleiter?showHeaders=true")
                .choice()
                    .when(httpStatusNOk)
                        .throwException(Exception.class, "Fehler beim Aktualisieren des Bauleiters: BauleiterKey: ${headers.BAULEITER_KEY}, Headers: ${headers}, Body: ${body}");
    }

    private String getBauleiterKey(Mitarbeiter mitarbeiter) {
        if (mitarbeiter == null) {
            return null;
        }
        String name = Objects.toString(mitarbeiter.name(), "").toUpperCase();
        name = name.replaceAll("Ä", "AE");
        name = name.replaceAll("Ö", "OE");
        name = name.replaceAll("Ü", "UE");
        name = name.replaceAll("ß", "SS");

        String vorname = Objects.toString(mitarbeiter.vorname(), "").toUpperCase();
        vorname = vorname.replaceAll("Ä", "AE");
        vorname = vorname.replaceAll("Ö", "OE");
        vorname = vorname.replaceAll("Ü", "UE");
        vorname = vorname.replaceAll("ß", "SS");

        return name.substring(0,Math.min(7, name.length())) + vorname.substring(0,3);
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisBrokerToBauleiteruebersichtErrorMessageTransformer("Bauleiteruebersicht", "NEVARIS",originQueueName, mandantMapToNevaris);
    }
}
