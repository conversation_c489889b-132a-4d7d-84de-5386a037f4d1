package de.systeex.cip.nevaris.bauleiteruebersicht.infrastructure;
import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public class NevarisBrokerToBauleiteruebersichtErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisBrokerToBauleiteruebersichtErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.QUEUE;
    }
}
