package de.systeex.cip.nevaris.bauleiteruebersicht.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record Bauleiteruebersicht(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonIgnore
        String odataEtag,
        @JsonProperty("Bauleiter")
        @Nonnull
        String bauleiter, //" Type="Edm.String" Nullable="false" MaxLength="10">
        @JsonProperty("Bezeichnung")
        @Nullable
        String bezeichnung, //" Type="Edm.String" MaxLength="50">
        @JsonProperty("Bezeichnung2")
        @Nullable
        String bezeichnung2, //" Type="Edm.String" MaxLength="50">
        @JsonProperty("Suchbegriff")
        @Nullable
        String suchbegriff, //" Type="Edm.String" MaxLength="30">
        @JsonProperty("Adressnr_des_Bauleiters")
        @Nullable
        String adressnr_des_Bauleiters, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressat_des_Bauleiters")
        @Nullable
        String adressat_des_Bauleiters, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Benutzer")
        @Nullable
        String benutzer, //" Type="Edm.String" MaxLength="50">
        @JsonProperty("Gruppe")
        @Nullable
        String gruppe, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gruppenstufe")
        @Nullable
        String gruppenstufe, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gruppenstufe2")
        @Nullable
        String gruppenstufe2, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gruppenstufe3")
        @Nullable
        String gruppenstufe3, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gruppenstufe4")
        @Nullable
        String gruppenstufe4, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Gruppenstufe5")
        @Nullable
        String gruppenstufe5, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("PersonalnummerZiffern")
        @Nullable
        String personalnummerZiffern, //" Type="Edm.Int32">
        @JsonProperty("PersonalnummerBaulohn")
        @Nullable
        String personalnummerBaulohn, //" Type="Edm.String" MaxLength="20">
        @JsonProperty("Sparte")
        @Nullable
        String sparte, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Neuanlagesystem")
        @Nullable
        @JsonIgnore
        String neuanlagesystem, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Neuanlagebenutzer")
        @Nullable
        @JsonIgnore
        String neuanlagebenutzer, //" Type="Edm.String" MaxLength="50">
        @JsonProperty("Neuanlagedatum")
        @Nullable
        @JsonIgnore
        String neuanlagedatum, //" Type="Edm.Date">
        @JsonProperty("Neuanlagezeit")
        @Nullable
        @JsonIgnore
        String neuanlagezeit, //" Type="Edm.String">
        @JsonProperty("Änderungssystem")
        @Nullable
        @JsonIgnore
        String aenderungssystem, //" Type="Edm.String" MaxLength="10">
        @JsonProperty("Änderungsbenutzer")
        @Nullable
        @JsonIgnore
        String aenderungsbenutzer, //" Type="Edm.String" MaxLength="50">
        @JsonProperty("Änderungsdatum")
        @Nullable
        @JsonIgnore
        String aenderungsdatum, //" Type="Edm.Date">
        @JsonProperty("Änderungszeit")
        @Nullable
        @JsonIgnore
        String aenderungszeit //" Type="Edm.String">
) {
        public static final String NULL_DATE = "0001-01-01";
}
