package de.systeex.cip.nevaris.artikel.artikellieferant_consumer.infrastructure;

import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public class NevarisArtikelLieferantToBanfErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisArtikelLieferantToBanfErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.CRON;
    }
}