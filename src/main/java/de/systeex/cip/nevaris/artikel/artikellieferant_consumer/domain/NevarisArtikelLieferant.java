package de.systeex.cip.nevaris.artikel.artikellieferant_consumer.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class NevarisArtikelLieferant {

    @JsonProperty("@odata.etag")
    private String odataEtag;

    @JsonProperty("Artikelnr")
    private String artikelnr;

    @JsonProperty("Kreditorennr")
    private String kreditorennr;

    @JsonProperty("Einheitencode")
    private String einheitencode;

    @JsonProperty("Startdatum")
    private String startdatum;

    @JsonProperty("Kred_Artikelnr")
    private String kred_Artikelnr;

    @JsonProperty("Markierung")
    private Boolean markierung;

    @JsonProperty("Kreditorenname")
    private String kreditorenname;

    @JsonProperty("KreditorenPLZ")
    private String kreditorenPLZ;

    @JsonProperty("KreditorenOrt")
    private String kreditorenOrt;

    @JsonProperty("Lieferbedingungscode")
    private String lieferbedingungscode;

    @JsonProperty("ZahlungAnKreditor")
    private String zahlungAnKreditor;

    @JsonProperty("Mindestbestellbetrag")
    private double mindestbestellbetrag;

    @JsonProperty("Lieferant_Beschreibung")
    private String lieferant_Beschreibung;

    @JsonProperty("Lieferant_Beschreibung_2")
    private String lieferant_Beschreibung_2;

    @JsonProperty("Lieferantenbemerkungen")
    private String lieferantenbemerkungen;

    @JsonProperty("Artikel_Bezeichnung")
    private String artikel_Bezeichnung;

    @JsonProperty("Artikel_Bezeichnung_2")
    private String artikel_Bezeichnung_2;

    @JsonProperty("Artikelbezeichnung1")
    private String artikelbezeichnung1;

    @JsonProperty("Artikelbezeichnung2")
    private String artikelbezeichnung2;

    @JsonProperty("Bestandsmengeneinheit")
    private String bestandsmengeneinheit;

    @JsonProperty("Umrechnungsfaktor")
    private double umrechnungsfaktor;

    @JsonProperty("EK_Preis")
    private double ek_Preis;

    @JsonProperty("Preis_gilt_pro_Menge")
    private double preis_gilt_pro_Menge;

    @JsonProperty("Bearbeitungsgebühr_pro_Einheit")
    private double bearbeitungsgebuehr_pro_Einheit;

    @JsonProperty("Lieferzeit")
    private String lieferzeit;

    @JsonProperty("Kred_Artikelnr_extern")
    private String kred_Artikelnr_extern;

    @JsonProperty("Lieferantenmengeneinheit")
    private String lieferantenmengeneinheit;

    @JsonProperty("Bestellmenge")
    private double bestellmenge;

    @JsonProperty("Langtextnummer")
    private String langtextnummer;

    @JsonProperty("Datanorm_Nr")
    private String datanorm_Nr;

    @JsonProperty("Datanorm_Warengruppe")
    private String datanorm_Warengruppe;

    @JsonProperty("Datanorm_Rabattgruppe")
    private String datanorm_Rabattgruppe;

    @JsonProperty("Rabatt_Percent")
    private double rabatt_Percent;

    @JsonProperty("Währungscode")
    private String waehrungscode;

    @JsonProperty("Gruppenstufe")
    private String gruppenstufe;

    @JsonProperty("Gruppenstufe2")
    private String gruppenstufe2;

    @JsonProperty("Gruppenstufe3")
    private String gruppenstufe3;

    @JsonProperty("Gruppenstufe4")
    private String gruppenstufe4;

    @JsonProperty("Gruppenstufe5")
    private String gruppenstufe5;

    @JsonProperty("Pflichtlieferant")
    private Boolean pflichtlieferant;

    @JsonProperty("Datanorm_Preis_gilt_pro_Menge")
    private double datanorm_Preis_gilt_pro_Menge;

    @JsonProperty("EAN_Nr")
    private String ean_Nr;

    @JsonProperty("Pflichtlieferant_nur_Hinweis")
    private Boolean pflichtlieferant_nur_Hinweis;

    @JsonProperty("Gültig_ab")
    private String gueltig_ab;

    @JsonProperty("Gültig_bis")
    private String gueltig_bis;

    @JsonProperty("Datensatz_Gesperrt")
    private String datensatz_Gesperrt;

    @JsonProperty("Sperrhinweis")
    private String sperrhinweis;

    @JsonProperty("Neuanlagesystem")
    private String neuanlagesystem;

    @JsonProperty("Neuanlagebenutzer")
    private String neuanlagebenutzer;

    @JsonProperty("Neuanlagedatum")
    private String neuanlagedatum;

    @JsonProperty("Neuanlagezeit")
    private String neuanlagezeit;

    @JsonProperty("Änderungssystem")
    private String aenderungssystem;

    @JsonProperty("Änderungsbenutzer")
    private String aenderungsbenutzer;

    @JsonProperty("Änderungsdatum")
    private String aenderungsdatum;

    @JsonProperty("Änderungszeit")
    private String aenderungszeit;

}