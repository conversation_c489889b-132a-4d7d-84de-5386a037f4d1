package de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.infrastructure;

import de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.domain.NevarisArtikeluebersicht;
import de.systeex.cip.types.Artikel;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

public class NevarisArtikeluebersichtToArtikelProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        NevarisArtikeluebersicht nevarisArtikeluebersicht = exchange.getIn().getBody(NevarisArtikeluebersicht.class);
        exchange.getIn().setHeader("ODATA_ETAG", nevarisArtikeluebersicht.odataEtag());
        Boolean datensatzGesperrt = !(Objects.toString(nevarisArtikeluebersicht.datensatzGesperrt(), "").equals("Nein")) || nevarisArtikeluebersicht.no().startsWith("X_") || nevarisArtikeluebersicht.blocked();
        Artikel artikel = new Artikel(
                (nevarisArtikeluebersicht.no().startsWith("X_")) ? nevarisArtikeluebersicht.no().substring(2) : nevarisArtikeluebersicht.no(),
                nevarisArtikeluebersicht.description(),
                nevarisArtikeluebersicht.description2(),
                nevarisArtikeluebersicht.description()+ " " + nevarisArtikeluebersicht.description2(),
                nevarisArtikeluebersicht.vendorItemNo(),
                (datensatzGesperrt) ? "Gesperrt" : nevarisArtikeluebersicht.searchDescription(),
                nevarisArtikeluebersicht.baseUnitOfMeasure(),
                (Float.parseFloat(Objects.toString(nevarisArtikeluebersicht.einstandspreisDurchschnII(), "0.0")) == 0.0 ) ? new BigDecimal(Objects.toString(nevarisArtikeluebersicht.verrechnungspreisIntern(), "0.0")) : new BigDecimal(nevarisArtikeluebersicht.einstandspreisDurchschnII()),
                nevarisArtikeluebersicht.kontierungshilfeIntern(),
                nevarisArtikeluebersicht.preisGiltProMenge() == null ? null : new BigDecimal(nevarisArtikeluebersicht.preisGiltProMenge()),
                nevarisArtikeluebersicht.indicrectCostPercent() == null ? null : new BigDecimal(nevarisArtikeluebersicht.indicrectCostPercent()),
                LocalDate.now(),
                "",
                datensatzGesperrt

        );
        if (!nevarisArtikeluebersicht.dummyartikel()) {
            exchange.getIn().setBody(artikel);
        }

    }
}
