package de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class NevarisArtikeluebersichtList{

    @JsonProperty("@odata.context")
    private String oDataContext;

    @JsonProperty("value")
    private List<NevarisArtikeluebersicht> nevarisArtikeluebersichtList;

    @JsonProperty("@odata.nextLink")
    private String oDataNextLink;

    public NevarisArtikeluebersichtList() {
    }

    public List<NevarisArtikeluebersicht> getNevarisArtikeluebersichtList() {
        return nevarisArtikeluebersichtList;
    }

    public String getoDataContext() {
        return oDataContext;
    }

    public void setoDataContext(String oDataContext) {
        this.oDataContext = oDataContext;
    }

    public String getoDataNextLink() {
        return oDataNextLink;
    }

    public void setoDataNextLink(String oDataNextLink) {
        this.oDataNextLink = oDataNextLink;
    }
}
