<EntityType Name="Artikelübersicht">
    <Key>
        <PropertyRef Name="No"/>
    </Key>
    <Property Name="No" Type="Edm.String" Nullable="false" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="No."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="true"/>
    </Property>
    <Property Name="Makierung" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Selection"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Description" Type="Edm.String" MaxLength="100">
        <Annotation Term="NAV.LabelId" String="Description"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Search_Description" Type="Edm.String" MaxLength="100">
        <Annotation Term="NAV.LabelId" String="Search Description"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Description_2" Type="Edm.String" MaxLength="50">
        <Annotation Term="NAV.LabelId" String="Description 2"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Base_Unit_of_Measure" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Base Unit of Measure"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Inventory_Posting_Group" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Inventory Posting Group"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Verk_Mengenrabattcode_II" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Sales qty discount code"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Item_Disc_Group" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Item Disc. Group"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Allow_Invoice_Disc" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Allow Invoice Disc."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Unit_Price" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Unit Price"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="VK_Preis_DB_Berechnung_II" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Sales price/DB - calculation"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Profit_Percent" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Profit %"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Costing_Method" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Costing Method"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Unit_Cost" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Unit Cost"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Standard_Cost" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Standard Cost"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Last_Direct_Cost" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Last Direct Cost"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Einstandspreis_durchschn_II" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Cost price (avg.)"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Vendor_No" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Vendor No."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Kreditorenname" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Vendor name"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Vendor_Item_No" Type="Edm.String" MaxLength="50">
        <Annotation Term="NAV.LabelId" String="Vendor Item No."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Lead_Time_Calculation" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Lead Time Calculation"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Reorder_Point" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Reorder Point"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Maximum_Inventory" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Maximum Inventory"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Reorder_Quantity" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Reorder Quantity"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Dispositionsmethodencode" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Code for planning methods"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Losgrößenrundungsfaktor" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Order Multiple"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gross_Weight" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Gross Weight"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Net_Weight" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Net Weight"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Tariff_No" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Tariff No."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Price_Includes_VAT" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Price Includes Tax"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="VAT_Bus_Posting_Gr_Price" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Tax Bus. Posting Gr. (Price)"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gen_Prod_Posting_Group" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Gen. Prod. Posting Group"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Country_Region_of_Origin_Code" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Country/Region of Origin Code"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Automatic_Ext_Texts" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Automatic Ext. Text"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="VAT_Prod_Posting_Group" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="VAT Prod. Posting Group"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Last_Unit_Cost_Calc_Date" Type="Edm.Date">
        <Annotation Term="NAV.LabelId" String="Last Unit Cost Calc. Date"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Date</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Inventory_Value_Zero" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Inventory Value Zero"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Artikeltyp" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Item Type"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Dummyartikel" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Dummy item"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Hersteller" Type="Edm.String" MaxLength="50">
        <Annotation Term="NAV.LabelId" String="Manufacturer"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Hersteller_Nr" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Manufacturer No."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Manufacturer_Code" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Manufacturer Code"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Stammlieferant" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Regular supplier"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Farbe" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Color"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Fabrikat_Nr" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Product no."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="EAN_Nr" Type="Edm.String" MaxLength="30">
        <Annotation Term="NAV.LabelId" String="EAN No."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gruppe" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Category"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gruppenstufe" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Group level"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gruppenstufe2" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Group level 2"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gruppenstufe3" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Group level 3"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gruppenstufe4" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Group level 4"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gruppenstufe5" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Group level 5"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAL_Art_neu" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="BAL type new"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAL_Nr_neu" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="BAL no. new"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAL_Art_alt" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="BAL type old"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAL_Nr_alt" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="BAL no. old"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Preis_gilt_pro_Menge" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Price applies per quantity"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Verrechnungspreis_intern" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Transfer price internal"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Kontierungshilfe_REBU" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Allocation Help ext."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Kontierungshilfe_intern" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Allocation Help intern."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Kostenträger" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Cost object"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Textbaustein_intern" Type="Edm.Int32">
        <Annotation Term="NAV.LabelId" String="Text module internal"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Int</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Arbeitsmittelpreis" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Working materials price"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Arbeitsmittelpreis_2" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Working materials price 2"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Arbeitsmittelpreis_3" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Working materials price 3"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Text_zu_Preis_1" Type="Edm.String" MaxLength="30">
        <Annotation Term="NAV.LabelId" String="Text for Price 1"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Text_zu_Preis_2" Type="Edm.String" MaxLength="30">
        <Annotation Term="NAV.LabelId" String="Text for Price 2"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Text_zu_Preis_3" Type="Edm.String" MaxLength="30">
        <Annotation Term="NAV.LabelId" String="Text for Price 3"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="kalk_Arbeitsmittelpreis" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Calc. Working materials price"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="kalk_Arbeitsmittelpreis_2" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Calc. Working materials price 2"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="kalk_Arbeitsmittelpreis_3" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Calc. Working materials price 3"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="kalk_Mengeneinheit" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Calc. Unit of measure"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Kalkulationsgewicht" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Calculation weight"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gewichtseinheit" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Unit of weight"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Kostenart_BBA" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Cost element CO"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="HKA_Kalkulation" Type="Edm.String" MaxLength="2">
        <Annotation Term="NAV.LabelId" String="OrCost Quantity Survey"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="KA_Kalkulation" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Cost Quantity Survey"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Ist_Material" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Act. material"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Alternative_Item_No" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Replacement item"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Alternativartikel_Beschreibung" Type="Edm.String" MaxLength="100">
        <Annotation Term="NAV.LabelId" String="Substitute Item Description"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Alternativartikel" Type="Edm.String" MaxLength="150">
        <Annotation Term="NAV.LabelId" String="Alternative Item"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Zuschlag_Percent" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Surcharge %"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Indirect_Cost_Percent" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Indirect Cost %"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Rundungsmethodencode" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Rounding method code"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Suchbaum" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Search tree"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Sollzeit" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Target time"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Neu_gebraucht" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Newly used"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Artikel_für_Fremd_Betankung" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Article for ext. refueling"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Artikelzuschlagsgruppe" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Item Add. Group"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Geprüft" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Checked"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Datensatz_Gesperrt" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Data record blocked"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Sperrhinweis" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Block comment"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Bemerkung_II" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Bemerkung_BAU"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Gültig_ab" Type="Edm.Date">
        <Annotation Term="NAV.LabelId" String="Valid from"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Date</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gültig_bis" Type="Edm.Date">
        <Annotation Term="NAV.LabelId" String="Valid until"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Date</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Assembly_BOM" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Assembly BOM"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Metallzuschlag_DEL" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Metal surcharge/DEL"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Lagerfachcode" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Bin Code"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Standardlagerfach" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Standard bin"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Gefahrgut" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Hazardous materials"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Lagerbestand_II" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Quantity on Hand"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Inhaltsstoff" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Ingredient"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Abfallklasse" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Waste class"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Sales_Unit_of_Measure" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Sales Unit of Measure"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Verpackungsart" Type="Edm.String" MaxLength="30">
        <Annotation Term="NAV.LabelId" String="Packaging type"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Artikelverfolgungscode" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Item positioning code"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Chargennummern" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Batch no. No. series"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Ablaufdatumsformel" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Expiry date formula"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Serial_Nos_II" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="No. series, serial no."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Blocked" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Blocked"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Gerätebestand" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Equipment stock"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="ist_Schüttgut" Type="Edm.Boolean">
        <Annotation Term="NAV.LabelId" String="Is bulk material"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Bool</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Zeilenart_Werkstattbericht" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Line type workshop report"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Zuschlagsbetrag" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Surcharge amount"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Mengeneinheit_Zuschlag" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Unit of measure surcharge"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Zugang_Menge_II" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Positive Adjmt. (Qty.)"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Abgang_Menge_II" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Negative Adjmt. (Qty.)"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Kontierungshilfe_Wartung" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Account assgmt aid maint."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Kategorie" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Category"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Neuanlagesystem" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="System of creation"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Produktbuchungsgruppe_Wartung" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Product posting group maint."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Herkunftscode_Abwertung" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Source code deval."/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Neuanlagebenutzer" Type="Edm.String" MaxLength="50">
        <Annotation Term="NAV.LabelId" String="User of creation"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Datum_der_Abwertung" Type="Edm.Date">
        <Annotation Term="NAV.LabelId" String="Date of devaluation"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Date</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Wartungsprozentsatz" Type="Edm.Decimal" Scale="Variable">
        <Annotation Term="NAV.LabelId" String="Maintenance percentage"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Real</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Neuanlagedatum" Type="Edm.Date">
        <Annotation Term="NAV.LabelId" String="Date of creation"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Date</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Neuanlagezeit" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Time of creation"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Änderungssystem" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Change system"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Änderungsbenutzer" Type="Edm.String" MaxLength="50">
        <Annotation Term="NAV.LabelId" String="Change user"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Änderungsdatum" Type="Edm.Date">
        <Annotation Term="NAV.LabelId" String="Change date"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/Date</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Änderungszeit" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Change time"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
        <Annotation Term="NAV.AllowEdit" Bool="false"/>
        <Annotation Term="NAV.AllowEditOnCreate" Bool="false"/>
    </Property>
    <Property Name="Location_Filter" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Location Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Global_Dimension_1_Filter" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Global Dimension 1 Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Global_Dimension_2_Filter" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Global Dimension 2 Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Drop_Shipment_Filter" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Drop Shipment Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Variant_Filter" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Variant Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAU_Lagerfachfilter_II" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Bin filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="Date_Filter" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Date Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAU_Spediteurfilter" Type="Edm.String" MaxLength="10">
        <Annotation Term="NAV.LabelId" String="Shipping Agent Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAU_Artikelpostennummerfilter" Type="Edm.String">
        <Annotation Term="NAV.LabelId" String="Item Entry No. Filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAU_Seriennr_Filter" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Serial no. Filter_BAU"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAU_Chargennr_Filter" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Batch no. Filter_BAU"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <Property Name="BAU_ProduktionsKStnfilter" Type="Edm.String" MaxLength="20">
        <Annotation Term="NAV.LabelId" String="Production cost center filter"/>
        <Annotation Term="NAV.NavType">
            <EnumMember>NAV.NavType/String</EnumMember>
        </Annotation>
    </Property>
    <NavigationProperty Name="Vendor_No_Link" Type="Collection(NAV.Kreditoren)" ContainsTarget="true">
        <ReferentialConstraint Property="Vendor_No" ReferencedProperty="No"/>
    </NavigationProperty>
    <NavigationProperty Name="Kostenart_BBA_Link" Type="Collection(NAV.kostenartenstammdaten)" ContainsTarget="true">
        <ReferentialConstraint Property="Kostenart_BBA" ReferencedProperty="Kostenart"/>
    </NavigationProperty>
    <NavigationProperty Name="BAU_ProduktionsKStnfilter_Link" Type="Collection(NAV.Kostenstellenliste)" ContainsTarget="true">
        <ReferentialConstraint Property="BAU_ProduktionsKStnfilter" ReferencedProperty="Control2"/>
    </NavigationProperty>
    <Annotation Term="NAV.LabelId" String="Artikelübersicht"/>
</EntityType>