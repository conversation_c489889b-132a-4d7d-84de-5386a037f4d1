package de.systeex.cip.nevaris.artikel.artikellieferant_consumer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.artikel.artikellieferant_consumer.domain.NevarisArtikelLieferant;
import de.systeex.cip.nevaris.artikel.artikellieferant_consumer.domain.NevarisArtikelLieferantList;
import de.systeex.cip.nevaris.artikel.artikellieferant_consumer.infrastructure.NevarisArtikelLieferantToBanfErrorMessageTransformer;
import de.systeex.cip.types.ArtikelLieferant;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class NevarisArtikelLieferantToBanfRoute extends ErrorResistantRouteBuilder {

    @Value("${poll.cron.artikellieferant}")
    private String cronExpression;

    @Value("${nevaris.host}")
    private String apiUrl;

    @Value("${nevaris.default.basepath}")
    private String basePath;

    @Value("#{${mandant.mapping.artikel}}")
    private Map<String, String> mandantMapToNevaris;

    private final ObjectMapper mapper = JsonMapper.builder()
            .findAndAddModules()
            .build();

    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final JacksonDataFormat jacksonDataFormatArtikel = new JacksonDataFormat(mapper, ArtikelLieferant.class);
    private final ModelMapper modelMapper = new ModelMapper();

    @Override
    public void configure() throws Exception {
        mandantMapToNevaris.keySet().forEach(mandant -> from("quartz://nevarisArtikelLieferant" + mandant + "?cron=" + cronExpression.replace(' ', '+'))
                .routeId("nevarisArtikelLieferantQuartz" + mandant)
                .log(LoggingLevel.INFO, "Started polling Artikel-Lieferant")
                .setHeader("MANDANT", constant(mandant))
                .toD("http://" + apiUrl + basePath + "/Company('"+ mandant +"')/Artikel_Lieferant?clientBuilder=#nevarisODataHttpClientBuilder")
                .to("direct:processArtikelLieferant"));

        from("direct:processArtikelLieferant")
                .routeId("processArtikelLieferant")
                .convertBodyTo(String.class, "UTF-8")
                .choice()
                .when(httpStatusOk)
                .unmarshal(new JacksonDataFormat(NevarisArtikelLieferantList.class))
                .process(exchange -> {
                    NevarisArtikelLieferantList list = exchange.getIn().getBody(NevarisArtikelLieferantList.class);
                    exchange.getIn().setBody(list.getNevarisArtikelLieferantList());
                })
                .split(body())
                .to("direct:artikelLieferantToBroker")
                .end()
        ;

        from("direct:artikelLieferantToBroker")
                .routeId("artikelLieferantToBroker")
                .process(exchange -> {
                    NevarisArtikelLieferant nevarisArtikelLieferant = exchange.getIn().getBody(NevarisArtikelLieferant.class);
                    ArtikelLieferant artikelLieferant = modelMapper.map(nevarisArtikelLieferant, ArtikelLieferant.class);

                    exchange.getIn().setHeader("ODATA_ETAG", nevarisArtikelLieferant.getOdataEtag());
                    exchange.getIn().setBody(artikelLieferant);
                })
                .marshal(jacksonDataFormatArtikel)
                .to("log:import?showHeaders=true")
                .removeHeaders("*", "MANDANT|ODATA_ETAG")
                .setExchangePattern(ExchangePattern.InOnly)
                .setHeader(DELIVERY_MODE, constant(2))
                .toD("rabbitmq:artikellieferant?exchangeType=topic&routingKey=${headers.MANDANT}&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig")
                .log(LoggingLevel.DEBUG, "artikelLieferant sent to RabbitMQ: ${headers} ${body}")
        ;
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisArtikelLieferantToBanfErrorMessageTransformer("Artikel_Lieferant", "NEVARIS", "nevarisArtikelLieferantQuartz", Map.of());
    }
}