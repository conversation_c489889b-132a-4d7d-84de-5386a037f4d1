package de.systeex.cip.nevaris.artikel.artikelsatzmarkierungwert_consumer.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class NevarisArtikelSatzmarkierungWert {

    @JsonProperty("@odata.etag")
    private String odataEtag;

    @JsonProperty("Art")
    private String art;

    @JsonProperty("Name")
    private String name;

    @JsonProperty("Mandant")
    private String mandant;

    @JsonProperty("Key1")
    private String key1;

    @JsonProperty("Key2")
    private String key2;

    @JsonProperty("Feldfilter")
    private String feldfilter;

    @JsonProperty("BezeichnungID")
    private String bezeichnungID;

    @JsonProperty("TmpBezeichnungÜbersetzt")
    private String tmpBezeichnungUebersetzt;

    @JsonProperty("Menge")
    private Double menge;

    @JsonProperty("Basiseinheitencode")
    private String basiseinheitencode;

    @JsonProperty("TmpBez2")
    private String tmpBez2;

    @JsonProperty("TmpBezeichnung2Übersetzt")
    private String tmpBezeichnung2Uebersetzt;

    @JsonProperty("Sortierreihenfolge")
    private Integer sortierreihenfolge;

    @JsonProperty("Neuanlagesystem")
    private String neuanlagesystem;

    @JsonProperty("Neuanlagebenutzer")
    private String neuanlagebenutzer;

    @JsonProperty("Neuanlagedatum")
    private String neuanlagedatum;

    @JsonProperty("Neuanlagezeit")
    private String neuanlagezeit;

    @JsonProperty("Änderungssystem")
    private String aenderungssystem;

    @JsonProperty("Änderungsbenutzer")
    private String aenderungsbenutzer;

    @JsonProperty("Änderungsdatum")
    private String aenderungsdatum;

    @JsonProperty("Änderungszeit")
    private String aenderungszeit;
}