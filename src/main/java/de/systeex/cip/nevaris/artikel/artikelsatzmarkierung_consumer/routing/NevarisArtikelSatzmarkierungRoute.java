package de.systeex.cip.nevaris.artikel.artikelsatzmarkierung_consumer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.artikel.artikelsatzmarkierung_consumer.domain.NevarisArtikelSatzmarkierung;
import de.systeex.cip.nevaris.artikel.artikelsatzmarkierung_consumer.domain.NevarisArtikelSatzmarkierungList;
import de.systeex.cip.nevaris.artikel.artikelsatzmarkierung_consumer.infrastructure.NevarisArtikelSatzmarkierungErrorMessageTransformer;
import de.systeex.cip.types.ArtikelSatzmarkierung;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.time.LocalDate;
import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class NevarisArtikelSatzmarkierungRoute extends ErrorResistantRouteBuilder {

    @Value("${poll.cron.artikelsatzmarkierung}")
    private String cronExpression;

    @Value("${nevaris.host}")
    private String apiUrl;

    @Value("${nevaris.default.basepath}")
    private String basePath;

    @Value("#{${mandant.mapping.artikel}}")
    private Map<String, String> mandantMapToNevaris;

    private final ObjectMapper mapper = JsonMapper.builder()
            .findAndAddModules()
            .build();

    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final JacksonDataFormat jacksonDataFormatArtikelSatzmarkierung = new JacksonDataFormat(mapper, ArtikelSatzmarkierung.class);
    private final ModelMapper modelMapper = new ModelMapper();

    @Override
    public void configure() throws Exception {
        mandantMapToNevaris.keySet().forEach(mandant -> from("quartz://nevarisArtikelSatzmarkierung" + mandant + "?cron=" + cronExpression.replace(' ', '+'))
                .routeId("nevarisArtikelSatzmarkierungQuartz" + mandant)
                .log(LoggingLevel.INFO, "Started polling Artikel-Satzmarkierung")
                .setHeader("MANDANT", constant(mandant))
                .toD("http://" + apiUrl + basePath + "/Company('" + mandant + "')/Artikel_Satzmarkierung?clientBuilder=#nevarisODataHttpClientBuilder")
                .to("direct:processArtikelSatzmarkierung"));

        from("direct:processArtikelSatzmarkierung")
                .routeId("processArtikelSatzmarkierung")
                .convertBodyTo(String.class, "UTF-8")
                .choice()
                .when(httpStatusOk)
                .unmarshal(new JacksonDataFormat(NevarisArtikelSatzmarkierungList.class))
                .process(exchange -> {
                    NevarisArtikelSatzmarkierungList list = exchange.getIn().getBody(NevarisArtikelSatzmarkierungList.class);
                    exchange.getIn().setBody(list.getNevarisArtikelSatzmarkierungList());
                })
                .split(body())
                .to("direct:artikelSatzmarkierungToBroker")
                .end()
        ;

        from("direct:artikelSatzmarkierungToBroker")
                .routeId("artikelSatzmarkierungToBroker")
                .process(exchange -> {
                    NevarisArtikelSatzmarkierung nevarisArtikelSatzmarkierung = exchange.getIn().getBody(NevarisArtikelSatzmarkierung.class);
                    ArtikelSatzmarkierung artikelSatzmarkierung = modelMapper.map(nevarisArtikelSatzmarkierung, ArtikelSatzmarkierung.class);
                    artikelSatzmarkierung.setNeuanlagedatum(LocalDate.parse(nevarisArtikelSatzmarkierung.getNeuanlagedatum()));
                    artikelSatzmarkierung.setAenderungsdatum(LocalDate.parse(nevarisArtikelSatzmarkierung.getAenderungsdatum()));
                    exchange.getIn().setHeader("ODATA_ETAG", nevarisArtikelSatzmarkierung.getOdataEtag());
                    exchange.getIn().setBody(artikelSatzmarkierung);
                })
                .marshal(jacksonDataFormatArtikelSatzmarkierung)
                .to("log:import?showHeaders=true")
                .removeHeaders("*", "MANDANT|ODATA_ETAG")
                .setExchangePattern(ExchangePattern.InOnly)
                .setHeader(DELIVERY_MODE, constant(2))
                .toD("rabbitmq:artikelsatzmarkierung?exchangeType=topic&routingKey=${headers.MANDANT}&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig")
                .log(LoggingLevel.DEBUG, "artikelSatzmarkierung sent to RabbitMQ: ${headers} ${body}")
        ;
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisArtikelSatzmarkierungErrorMessageTransformer("Artikel_Satzmarkierung", "NEVARIS", "nevarisArtikelSatzmarkierungQuartz", Map.of());
    }
}
