package de.systeex.cip.nevaris.artikel.artikelsatzmarkierung_consumer.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class NevarisArtikelSatzmarkierung {

    @JsonProperty("@odata.etag")
    private String odataEtag;

    @JsonProperty("Art")
    private String art;

    @JsonProperty("Name")
    private String name;

    @JsonProperty("Bezeichnung")
    private String bezeichnung;

    @JsonProperty("TmpEnthalten_Rec")
    private String tmpEnthalten_Rec;

    @JsonProperty("Bezeichnung_2")
    private String bezeichnung2;

    @JsonProperty("Mandant_der_Kostenstelle")
    private String mandant_der_Kostenstelle;

    @JsonProperty("Kostenstelle")
    private String kostenstelle;

    @JsonProperty("Neuanlagesystem")
    private String neuanlagesystem;

    @JsonProperty("Neuanlagebenutzer")
    private String neuanlagebenutzer;

    @JsonProperty("Neuanlagedatum")
    private String neuanlagedatum;

    @JsonProperty("Neuanlagezeit")
    private String neuanlagezeit;

    @JsonProperty("Änderungssystem")
    private String aenderungssystem;

    @JsonProperty("Änderungsbenutzer")
    private String aenderungsbenutzer;

    @JsonProperty("Änderungsdatum")
    private String aenderungsdatum;

    @JsonProperty("Änderungszeit")
    private String aenderungszeit;
}