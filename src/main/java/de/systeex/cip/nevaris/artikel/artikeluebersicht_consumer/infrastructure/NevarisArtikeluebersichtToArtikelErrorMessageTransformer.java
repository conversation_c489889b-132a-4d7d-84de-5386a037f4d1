package de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.infrastructure;

import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public class NevarisArtikeluebersichtToArtikelErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisArtikeluebersichtToArtikelErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.CRON;
    }
}
