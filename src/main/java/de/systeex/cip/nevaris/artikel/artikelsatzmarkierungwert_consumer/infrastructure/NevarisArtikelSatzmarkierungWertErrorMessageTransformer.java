package de.systeex.cip.nevaris.artikel.artikelsatzmarkierungwert_consumer.infrastructure;

import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public final class NevarisArtikelSatzmarkierungWertErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisArtikelSatzmarkierungWertErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.CRON;
    }
}