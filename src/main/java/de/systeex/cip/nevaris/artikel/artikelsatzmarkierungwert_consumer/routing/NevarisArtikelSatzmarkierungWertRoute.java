package de.systeex.cip.nevaris.artikel.artikelsatzmarkierungwert_consumer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.artikel.artikelsatzmarkierungwert_consumer.domain.NevarisArtikelSatzmarkierungWert;
import de.systeex.cip.nevaris.artikel.artikelsatzmarkierungwert_consumer.domain.NevarisArtikelSatzmarkierungWertList;
import de.systeex.cip.nevaris.artikel.artikelsatzmarkierungwert_consumer.infrastructure.NevarisArtikelSatzmarkierungWertErrorMessageTransformer;
import de.systeex.cip.types.ArtikelSatzmarkierungWert;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.time.LocalDate;
import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class NevarisArtikelSatzmarkierungWertRoute extends ErrorResistantRouteBuilder {

    @Value("${poll.cron.artikelsatzmarkierungwert}")
    private String cronExpression;

    @Value("${nevaris.host}")
    private String apiUrl;

    @Value("${nevaris.default.basepath}")
    private String basePath;

    @Value("#{${mandant.mapping.artikel}}")
    private Map<String, String> mandantMapToNevaris;

    private final ObjectMapper mapper = JsonMapper.builder()
            .findAndAddModules()
            .build();

    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final JacksonDataFormat jacksonDataFormatArtikelSatzmarkierungWert = new JacksonDataFormat(mapper, ArtikelSatzmarkierungWert.class);
    private final ModelMapper modelMapper = new ModelMapper();

    @Override
    public void configure() throws Exception {
        mandantMapToNevaris.keySet().forEach(mandant -> from("quartz://nevarisArtikelSatzmarkierungWert" + mandant + "?cron=" + cronExpression.replace(' ', '+'))
                .routeId("nevarisArtikelSatzmarkierungWertQuartz" + mandant)
                .log(LoggingLevel.INFO, "Started polling Artikel-Satzmarkierung-Wert")
                .setHeader("MANDANT", constant(mandant))
                .toD("http://" + apiUrl + basePath + "/Company('"+ mandant +"')/Artikel_Satzmarkierung_Wert?clientBuilder=#nevarisODataHttpClientBuilder")
                .to("direct:processArtikelSatzmarkierungWert"));

        from("direct:processArtikelSatzmarkierungWert")
                .routeId("processArtikelSatzmarkierungWert")
                .convertBodyTo(String.class, "UTF-8")
                .choice()
                .when(httpStatusOk)
                .unmarshal(new JacksonDataFormat(NevarisArtikelSatzmarkierungWertList.class))
                .process(exchange -> {
                    NevarisArtikelSatzmarkierungWertList list = exchange.getIn().getBody(NevarisArtikelSatzmarkierungWertList.class);
                    exchange.getIn().setBody(list.getNevarisArtikelSatzmarkierungWertList());
                })
                .split(body())
                .to("direct:artikelSatzmarkierungWertToBroker")
                .end()
        ;

        from("direct:artikelSatzmarkierungWertToBroker")
                .routeId("artikelSatzmarkierungWertToBroker")
                .process(exchange -> {
                    NevarisArtikelSatzmarkierungWert nevarisArtikelSatzmarkierungWert = exchange.getIn().getBody(NevarisArtikelSatzmarkierungWert.class);
                    ArtikelSatzmarkierungWert artikelSatzmarkierungWert = modelMapper.map(nevarisArtikelSatzmarkierungWert, ArtikelSatzmarkierungWert.class);
                    artikelSatzmarkierungWert.setNeuanlagedatum(LocalDate.parse(nevarisArtikelSatzmarkierungWert.getNeuanlagedatum()));
                    artikelSatzmarkierungWert.setAenderungsdatum(LocalDate.parse(nevarisArtikelSatzmarkierungWert.getAenderungsdatum()));
                    exchange.getIn().setHeader("ODATA_ETAG", nevarisArtikelSatzmarkierungWert.getOdataEtag());
                    exchange.getIn().setBody(artikelSatzmarkierungWert);
                })
                .marshal(jacksonDataFormatArtikelSatzmarkierungWert)
                .to("log:import?showHeaders=true")
                .removeHeaders("*", "MANDANT|ODATA_ETAG")
                .setExchangePattern(ExchangePattern.InOnly)
                .setHeader(DELIVERY_MODE, constant(2))
                .toD("rabbitmq:artikelsatzmarkierungwert?exchangeType=topic&routingKey=${headers.MANDANT}&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig")
                .log(LoggingLevel.DEBUG, "artikelSatzmarkierungWert sent to RabbitMQ: ${headers} ${body}")
        ;
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisArtikelSatzmarkierungWertErrorMessageTransformer("Artikel_Satzmarkierung_Wert", "NEVARIS", "nevarisArtikelSatzmarkierungWertQuartz", Map.of());
    }
}
