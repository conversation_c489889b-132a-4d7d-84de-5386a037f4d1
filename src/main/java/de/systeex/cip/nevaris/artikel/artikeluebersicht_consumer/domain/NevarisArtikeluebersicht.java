package de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.systeex.cip.nevaris.domain.odata.NevarisOdataTimedClass;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
public final class NevarisArtikeluebersicht extends NevarisOdataTimedClass {
    @JsonProperty("@odata.context")
    @JsonIgnore
    private final String odataContext;
    @JsonProperty("@odata.etag")
    @JsonIgnore
    private final String odataEtag;
    @JsonProperty("No")
    @Nonnull
    private final String no;
    @JsonProperty("Makierung")
    @Nullable
    private final Boolean markierung;
    @JsonProperty("Description")
    @Nullable
    private final String description;
    @JsonProperty("Search_Description")
    @Nullable
    private final String searchDescription;
    @JsonProperty("Description_2")
    @Nullable
    private final String description2;
    @JsonProperty("Base_Unit_of_Measure")
    @Nullable
    private final String baseUnitOfMeasure;
    @JsonProperty("Inventory_Posting_Group")
    @Nullable
    private final String inventoryPostingGroup;
    @JsonProperty("Verk_Mengenrabattcode_II")
    @Nullable
    private final String verkMengenrabattcodeII;
    @JsonProperty("Item_Disc_Group")
    @Nullable
    private final String itemDiscGroup;
    @JsonProperty("Allow_Invoice_Disc")
    @Nullable
    private final Boolean allowInvoiceDisc;
    @JsonProperty("Unit_Price")
    @Nullable
    private final String unit_price;
    @JsonProperty("VK_Preis_DB_Berechnung_II")
    @Nullable
    private final String vkPreisDCBerechnungII;
    @JsonProperty("Profit_Percent")
    @Nullable
    private final String profitPercent;
    @JsonProperty("Costing_Method")
    @Nullable
    private final String costingMethod;
    @JsonProperty("Unit_Cost")
    @Nullable
    private final String unitCost;
    @JsonProperty("Standard_Cost")
    @Nullable
    private final String standardCost;
    @JsonProperty("Last_Direct_Cost")
    @Nullable
    private final String lastDirectCost;
    @JsonProperty("Einstandspreis_durchschn_II")
    @Nullable
    private final String einstandspreisDurchschnII;
    @JsonProperty("Vendor_No")
    @Nullable
    private final String vendorNo;
    @JsonProperty("Kreditorenname")
    @Nullable
    private final String kreditorenname;
    @JsonProperty("Vendor_Item_No")
    @Nullable
    private final String vendorItemNo;
    @JsonProperty("Lead_Time_Calculation")
    @Nullable
    private final String leadTimeCalculation;
    @JsonProperty("Reorder_Point")
    @Nullable
    private final String reorderPoint;
    @JsonProperty("Maximum_Inventory")
    @Nullable
    private final String maximumInventory;
    @JsonProperty("Reorder_Quantity")
    @Nullable
    private final String reorderQuantity;
    @JsonProperty("Dispositionsmethodencode")
    @Nullable
    private final String dispositionsmethodencode;
    @JsonProperty("Losgrößenrundungsfaktor")
    @Nullable
    private final String lossgroessenrundungsfaktor;
    @JsonProperty("Gross_Weight")
    @Nullable
    private final String grossWeight;
    @JsonProperty("Net_Weight")
    @Nullable
    private final String netWeight;
    @JsonProperty("Tariff_No")
    @Nullable
    private final String tariffNo;
    @JsonProperty("Price_Includes_VAT")
    @Nullable
    private final Boolean priceIncludesVat;
    @JsonProperty("VAT_Bus_Posting_Gr_Price")
    @Nullable
    private final String VATBusPostingGrPrice;
    @JsonProperty("Gen_Prod_Posting_Group")
    @Nullable
    private final String GenProdPostingGroup;
    @JsonProperty("Country_Region_of_Origin_Code")
    @Nullable
    private final String countryRegionOfOriginCode;
    @JsonProperty("Automatic_Ext_Texts")
    @Nullable
    private final Boolean automaticExtTexts;
    @JsonProperty("VAT_Prod_Posting_Group")
    @Nullable
    private final String VATProdPostingGroup;
    @JsonProperty("Last_Unit_Cost_Calc_Date")
    @Nullable
    private final String lastUnitCostCalcDate;
    @JsonProperty("Inventory_Value_Zero")
    @Nullable
    private final Boolean inventoryValueZero;
    @JsonProperty("Artikeltyp")
    @Nullable
    private final String artikelTyp;
    @JsonProperty("Dummyartikel")
    @Nullable
    private final Boolean dummyartikel;
    @JsonProperty("Hersteller")
    @Nullable
    private final String hersteller;
    @JsonProperty("Hersteller_Nr")
    @Nullable
    private final String herstellerNr;
    @JsonProperty("Stammlieferant")
    @Nullable
    private final String stammlieferant;
    @JsonProperty("Farbe")
    @Nullable
    private final String farbe;
    @JsonProperty("Fabrikat_Nr")
    @Nullable
    private final String fabrikatNr;
    @JsonProperty("EAN_Nr")
    @Nullable
    private final String eANNr;
    @JsonProperty("Gruppe")
    @Nullable
    private final String gruppe;
    @JsonProperty("Gruppenstufe")
    @Nullable
    private final String gruppenstufe;
    @JsonProperty("Gruppenstufe2")
    @Nullable
    private final String gruppenstufe2;
    @JsonProperty("Gruppenstufe3")
    @Nullable
    private final String gruppenstufe3;
    @JsonProperty("Gruppenstufe4")
    @Nullable
    private final String gruppenstufe4;
    @JsonProperty("Gruppenstufe5")
    @Nullable
    private final String gruppenstufe5;
    @JsonProperty("BAL_Art_neu")
    @Nullable
    private final String bALArtNeu;
    @JsonProperty("BAL_Nr_neu")
    @Nullable
    private final String bALNrNeu;
    @JsonProperty("BAL_Art_alt")
    @Nullable
    private final String bALArtAlt;
    @JsonProperty("BAL_Nr_alt")
    @Nullable
    private final String bALNrAlt;
    @JsonProperty("Preis_gilt_pro_Menge")
    @Nullable
    private final String preisGiltProMenge;
    @JsonProperty("Verrechnungspreis_intern")
    @Nullable
    private final String verrechnungspreisIntern;
    @JsonProperty("Kontierungshilfe_REBU")
    @Nullable
    private final String kontierungshilfeREBU;
    @JsonProperty("Kontierungshilfe_intern")
    @Nullable
    private final String kontierungshilfeIntern;
    @JsonProperty("Kostenträger")
    @Nullable
    private final String kostentraeger;
    @JsonProperty("Textbaustein_intern")
    @Nullable
    private final String textbausteinIntern;
    @JsonProperty("Arbeitsmittelpreis")
    @Nullable
    private final String arbeitsmittelpreis;
    @JsonProperty("Arbeitsmittelpreis_2")
    @Nullable
    private final String arbeitsmittelpreis2;
    @JsonProperty("Arbeitsmittelpreis_3")
    @Nullable
    private final String arbeitsmittelpreis3;
    @JsonProperty("Text_zu_Preis_1")
    @Nullable
    private final String textZuPreis1;
    @JsonProperty("Text_zu_Preis_2")
    @Nullable
    private final String textZuPreis2;
    @JsonProperty("Text_zu_Preis_3")
    @Nullable
    private final String textZuPreis3;
    @JsonProperty("kalk_Arbeitsmittelpreis")
    @Nullable
    private final String kalkArbeitsmittelpreis;
    @JsonProperty("kalk_Arbeitsmittelpreis_2")
    @Nullable
    private final String kalkArbeitsmittelpreis2;
    @JsonProperty("kalk_Arbeitsmittelpreis_3")
    @Nullable
    private final String kalkArbeitsmittelpreis3;
    @JsonProperty("kalk_Mengeneinheit")
    @Nullable
    private final String kalkMengeneinheit;
    @JsonProperty("Kalkulationsgewicht")
    @Nullable
    private final String kalkulationsgewicht;
    @JsonProperty("Gewichtseinheit")
    @Nullable
    private final String gewichtseinheit;
    @JsonProperty("Kostenart_BBA")
    @Nullable
    private final String kostenartBBA;
    @JsonProperty("HKA_Kalkulation")
    @Nullable
    private final String hKAKalkulation;
    @JsonProperty("KA_Kalkulation")
    @Nullable
    private final String kAKalkulation;
    @JsonProperty("Ist_Material")
    @Nullable
    private final Boolean istMaterial;
    @JsonProperty("Alternative_Item_No")
    @Nullable
    private final String alternativeItemNo;
    @JsonProperty("Alternativartikel_Beschreibung")
    @Nullable
    private final String alternativeBeschreibung;
    @JsonProperty("Alternativartikel")
    @Nullable
    private final String alternativartikel;
    @JsonProperty("Zuschlag_Percent")
    @Nullable
    private final String zuschlagPercent;
    @JsonProperty("Indirect_Cost_Percent")
    @Nullable
    private final String indicrectCostPercent;
    @JsonProperty("Rundungsmethodencode")
    @Nullable
    private final String rundungsmethodencade;
    @JsonProperty("Suchbaum")
    @Nullable
    private final String suchbaum;
    @JsonProperty("Sollzeit")
    @Nullable
    private final String sollzeit;
    @JsonProperty("Neu_gebraucht")
    @Nullable
    private final String neuGebraucht;
    @JsonProperty("Artikel_für_Fremd_Betankung")
    @Nullable
    private final Boolean artikelFuerFremdbetankung;
    @JsonProperty("Artikelzuschlagsgruppe")
    @Nullable
    private final String artikelzuschlagsgruppe;
    @JsonProperty("Geprüft")
    @Nullable
    private final Boolean geprueft;
    @JsonProperty("Datensatz_Gesperrt")
    @Nullable
    private final String datensatzGesperrt;
    @JsonProperty("Sperrhinweis")
    @Nullable
    private final String sperrhinweis;
    @JsonProperty("Bemerkung_II")
    @Nullable
    private final Boolean bemerkungII;
    @JsonProperty("Gültig_ab")
    @Nullable
    private final String gueltigAb;
    @JsonProperty("Gültig_bis")
    @Nullable
    private final String gueltigBis;
    @JsonProperty("Assembly_BOM")
    @Nullable
    private final Boolean assemblyBOM;
    @JsonProperty("Metallzuschlag_DEL")
    @Nullable
    private final Boolean metallzuschlagDEL;
    @JsonProperty("Lagerfachcode")
    @Nullable
    private final String lagerfachcode;
    @JsonProperty("Standardlagerfach")
    @Nullable
    private final String standardlagerfach;
    @JsonProperty("Gefahrgut")
    @Nullable
    private final Boolean gefahrgut;
    @JsonProperty("Lagerbestand_II")
    @Nullable
    private final String lagerbestandII;
    @JsonProperty("Inhaltsstoff")
    @Nullable
    private final String inhaltsstoffe;
    @JsonProperty("Abfallklasse")
    @Nullable
    private final String abfallklasse;
    @JsonProperty("Sales_Unit_of_Measure")
    @Nullable
    private final String salesUnitOfMeasure;
    @JsonProperty("Verpackungsart")
    @Nullable
    private final String verpackungsart;
    @JsonProperty("Artikelverfolgungscode")
    @Nullable
    private final String artikelverfolgungscode;
    @JsonProperty("Chargennummern")
    @Nullable
    private final String chargennummern;
    @JsonProperty("Ablaufdatumsformel")
    @Nullable
    private final String ablaufdatumsformel;
    @JsonProperty("Serial_Nos_II")
    @Nullable
    private final String serialNosII;
    @JsonProperty("Blocked")
    @Nullable
    private final Boolean blocked;
    @JsonProperty("Gerätebestand")
    @Nullable
    private final String geraetebestand;
    @JsonProperty("ist_Schüttgut")
    @Nullable
    private final Boolean istSchuettgut;
    @JsonProperty("Zeilenart_Werkstattbericht")
    @Nullable
    private final String zeilenartWerkstattbericht;
    @JsonProperty("Zuschlagsbetrag")
    @Nullable
    private final String zuschlagsbetrag;
    @JsonProperty("Mengeneinheit_Zuschlag")
    @Nullable
    private final String mengeneinheitZuschlag;
    @JsonProperty("Zugang_Menge_II")
    @Nullable
    private final String zugangMengeII;
    @JsonProperty("Abgang_Menge_II")
    @Nullable
    private final String abgangMengeII;
    @JsonProperty("Kontierungshilfe_Wartung")
    @Nullable
    private final String kontierungshilfeWartung;
    @JsonProperty("Kategorie")
    @Nullable
    private final String kategorie;
    @JsonProperty("Neuanlagesystem")
    @Nullable
    private final String neuanlagesystem;
    @JsonProperty("Produktbuchungsgruppe_Wartung")
    @Nullable
    private final String produktbuchungsgruppeWartung;
    @JsonProperty("Herkunftscode_Abwertung")
    @Nullable
    private final String herkunftscodeAbwertung;
    @JsonProperty("Neuanlagebenutzer")
    @Nullable
    private final String neuanlagenbenutzer;
    @JsonProperty("Datum_der_Abwertung")
    @Nullable
    private final String datumDerAbwertung;
    @JsonProperty("Wartungsprozentsatz")
    @Nullable
    private final String wartungsprozentsatz;
    @JsonProperty("Neuanlagedatum")
    @Nullable
    private final String neuanlagendatum;
    @JsonProperty("Neuanlagezeit")
    @Nullable
    private final String neuanlagenzeit;
    @JsonProperty("Änderungssystem")
    @Nullable
    private final String aenderungssystem;
    @JsonProperty("Änderungsbenutzer")
    @Nullable
    private final String aenderungsbenutzer;
    @JsonProperty("Änderungsdatum")
    @Nullable
    private final String aenderungsdatum;
    @JsonProperty("Änderungszeit")
    @Nullable
    private final String aenderungszeit;
    @JsonProperty("Location_Filter")
    @Nullable
    private final String locationFilter;
    @JsonProperty("Global_Dimension_1_Filter")
    @Nullable
    private final String globalDimension1Filter;
    @JsonProperty("Global_Dimension_2_Filter")
    @Nullable
    private final String globalDimension2Filter;
    @JsonProperty("Drop_Shipment_Filter")
    @Nullable
    private final String dropShipmentFilter;
    @JsonProperty("Variant_Filter")
    @Nullable
    private final String variantFilter;
    @JsonProperty("BAU_Lagerfachfilter_II")
    @Nullable
    private final String lagerfachfilterII;
    @JsonProperty("Date_Filter")
    @Nullable
    private final String dateFilter;
    @JsonProperty("BAU_Spediteurfilter")
    @Nullable
    private final String spediteurfilter;
    @JsonProperty("BAU_Artikelpostennummerfilter")
    @Nullable
    private final String artikelpostennummerfilter;
    @JsonProperty("BAU_Seriennr_Filter")
    @Nullable
    private final String seriennrFilter;
    @JsonProperty("BAU_Chargennr_Filter")
    @Nullable
    private final String chargennrFilter;
    @JsonProperty("BAU_ProduktionsKStnfilter")
    @Nullable
    private final String produktionsKstnFilter;
    @JsonProperty("BAU_ProduktionsKStnfilter_Link")
    @Nullable
    private final String produktionsKstnFilterLink;
    @JsonProperty("Country_Region_of_Origin_Code_Link")
    @Nullable
    private final String countryRegionOfOriginCodeLink;



    public NevarisArtikeluebersicht(

            @JsonProperty("@odata.context")
            String odataContext,
            @JsonProperty("@odata.etag")
            String odataEtag,
            @JsonProperty("No")
            @Nonnull
            String no,// Type="Edm.String" Nullable="false" MaxLength="20">
            @JsonProperty("Makierung")
            @Nullable
            Boolean markierung, // Type="Edm.Boolean">
            @JsonProperty("Description")
            @Nullable
            String description, //  Type="Edm.String" MaxLength="100">
            @JsonProperty("Search_Description")
            @Nullable
            String searchDescription, // Type="Edm.String" MaxLength="100">
            @JsonProperty("Description_2")
            @Nullable
            String description2, //Type="Edm.String" MaxLength="50">
            @JsonProperty("Base_Unit_of_Measure")
            @Nullable
            String baseUnitOfMeasure, //Type="Edm.String" MaxLength="10">
            @JsonProperty("Inventory_Posting_Group")
            @Nullable
            String inventoryPostingGroup, //Type="Edm.String" MaxLength="20">
            @JsonProperty("Verk_Mengenrabattcode_II")
            @Nullable
            String verkMengenrabattcodeII, // Type="Edm.String" MaxLength="20">
            @JsonProperty("Item_Disc_Group")
            @Nullable
            String itemDiscGroup, // Type="Edm.String" MaxLength="20">
            @JsonProperty("Allow_Invoice_Disc")
            @Nullable
            Boolean allowInvoiceDisc, //  Type="Edm.Boolean">
            @JsonProperty("Unit_Price")
            @Nullable
            String unit_price, //ype="Edm.Decimal" Scale="Variable">
            @JsonProperty("VK_Preis_DB_Berechnung_II")
            @Nullable
            String vkPreisDCBerechnungII, // Type="Edm.String">
            @JsonProperty("Profit_Percent")
            @Nullable
            String profitPercent, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Costing_Method")
            @Nullable
            String costingMethod, //Type="Edm.String">
            @JsonProperty("Unit_Cost")
            @Nullable
            String unitCost, //Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Standard_Cost")
            @Nullable
            String standardCost, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Last_Direct_Cost")
            @Nullable
            String lastDirectCost, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Einstandspreis_durchschn_II")
            @Nullable
            String einstandspreisDurchschnII, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Vendor_No")
            @Nullable
            String vendorNo, // Type="Edm.String" MaxLength="20">
            @JsonProperty("Kreditorenname")
            @Nullable
            String kreditorenname, // Type="Edm.String">
            @JsonProperty("Vendor_Item_No")
            @Nullable
            String vendorItemNo, // Type="Edm.String" MaxLength="50">
            @JsonProperty("Lead_Time_Calculation")
            @Nullable
            String leadTimeCalculation, // Type="Edm.String">
            @JsonProperty("Reorder_Point")
            @Nullable
            String reorderPoint, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Maximum_Inventory")
            @Nullable
            String maximumInventory, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Reorder_Quantity")
            @Nullable
            String reorderQuantity, //Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Dispositionsmethodencode")
            @Nullable
            String dispositionsmethodencode, //  Type="Edm.String" MaxLength="10">
            @JsonProperty("Losgrößenrundungsfaktor")
            @Nullable
            String lossgroessenrundungsfaktor, //Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Gross_Weight")
            @Nullable
            String grossWeight, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Net_Weight")
            @Nullable
            String netWeight, // Type="Edm.Decimal" Scale="Variable">
            @JsonProperty("Tariff_No")
            @Nullable
            String tariffNo, // Type="Edm.String" MaxLength="20">
            @JsonProperty("Price_Includes_VAT")
            @Nullable
            Boolean priceIncludesVat, //  Type="Edm.Boolean">
            @JsonProperty("VAT_Bus_Posting_Gr_Price")
            @Nullable
            String VATBusPostingGrPrice, // Type="Edm.String" MaxLength="20">
            @JsonProperty("Gen_Prod_Posting_Group")
            @Nullable
            String GenProdPostingGroup, // Type="Edm.String" MaxLength="20">
            @JsonProperty("Country_Region_of_Origin_Code")
            @Nullable
            String countryRegionOfOriginCode, // Type="Edm.String" MaxLength="10">
            @JsonProperty("Automatic_Ext_Texts")
            @Nullable
            Boolean automaticExtTexts, // Type="Edm.Boolean">
            @JsonProperty("VAT_Prod_Posting_Group")
            @Nullable
            String VATProdPostingGroup, //  Type="Edm.String" MaxLength="20">
            @JsonProperty("Last_Unit_Cost_Calc_Date")
            @Nullable
            String lastUnitCostCalcDate, //  Type="Edm.Date">
            @JsonProperty("Inventory_Value_Zero")
            @Nullable
            Boolean inventoryValueZero, // Type="Edm.Boolean">
            @JsonProperty("Artikeltyp")
            @Nullable
            String artikelTyp, // Type="Edm.String" MaxLength="10">
            @JsonProperty("Dummyartikel")
            @Nullable
            Boolean dummyartikel, // Type="Edm.Boolean">
            @JsonProperty("Hersteller")
            @Nullable
            String hersteller, //Type="Edm.String" MaxLength="50">
            @JsonProperty("Hersteller_Nr")
            @Nullable
            String herstellerNr, //Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Stammlieferant")
            @Nullable
            String stammlieferant,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Farbe")
            @Nullable
            String farbe,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Fabrikat_Nr")
            @Nullable
            String fabrikatNr,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("EAN_Nr")
            @Nullable
            String eANNr,//Type=	"Edm.String" MaxLength="30">
            @JsonProperty("Gruppe")
            @Nullable
            String gruppe, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe")
            @Nullable
            String gruppenstufe, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe2")
            @Nullable
            String gruppenstufe2, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe3")
            @Nullable
            String gruppenstufe3, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe4")
            @Nullable
            String gruppenstufe4,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Gruppenstufe5")
            @Nullable
            String gruppenstufe5,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("BAL_Art_neu")
            @Nullable
            String bALArtNeu,//Type=	"Edm.String">
            @JsonProperty("BAL_Nr_neu")
            @Nullable
            String bALNrNeu,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("BAL_Art_alt")
            @Nullable
            String bALArtAlt,//Type=	"Edm.String">
            @JsonProperty("BAL_Nr_alt")
            @Nullable
            String bALNrAlt,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Preis_gilt_pro_Menge")
            @Nullable
            String preisGiltProMenge,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Verrechnungspreis_intern")
            @Nullable
            String verrechnungspreisIntern,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Kontierungshilfe_REBU")
            @Nullable
            String kontierungshilfeREBU,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Kontierungshilfe_intern")
            @Nullable
            String kontierungshilfeIntern,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Kostenträger")
            @Nullable
            String kostentraeger,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Textbaustein_intern")
            @Nullable
            String textbausteinIntern,//Type=	"Edm.Int32">
            @JsonProperty("Arbeitsmittelpreis")
            @Nullable
            String arbeitsmittelpreis, //Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Arbeitsmittelpreis_2")
            @Nullable
            String arbeitsmittelpreis2,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Arbeitsmittelpreis_3")
            @Nullable
            String arbeitsmittelpreis3, //Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Text_zu_Preis_1")
            @Nullable
            String textZuPreis1,//Type=	"Edm.String" MaxLength="30">
            @JsonProperty("Text_zu_Preis_2")
            @Nullable
            String textZuPreis2,//Type=	"Edm.String" MaxLength="30">
            @JsonProperty("Text_zu_Preis_3")
            @Nullable
            String textZuPreis3,//Type=	"Edm.String" MaxLength="30">
            @JsonProperty("kalk_Arbeitsmittelpreis")
            @Nullable
            String kalkArbeitsmittelpreis,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("kalk_Arbeitsmittelpreis_2")
            @Nullable
            String kalkArbeitsmittelpreis2,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("kalk_Arbeitsmittelpreis_3")
            @Nullable
            String kalkArbeitsmittelpreis3,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("kalk_Mengeneinheit")
            @Nullable
            String kalkMengeneinheit,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Kalkulationsgewicht")
            @Nullable
            String kalkulationsgewicht,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Gewichtseinheit")
            @Nullable
            String gewichtseinheit, //Type=	"Edm.String">
            @JsonProperty("Kostenart_BBA")
            @Nullable
            String kostenartBBA,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("HKA_Kalkulation")
            @Nullable
            String hKAKalkulation,//Type=	"Edm.String" MaxLength="2">
            @JsonProperty("KA_Kalkulation")
            @Nullable
            String kAKalkulation,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Ist_Material")
            @Nullable
            Boolean istMaterial,//Type=	"Edm.Boolean">
            @JsonProperty("Alternative_Item_No")
            @Nullable
            String alternativeItemNo,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Alternativartikel_Beschreibung")
            @Nullable
            String alternativeBeschreibung,//Type=	"Edm.String" MaxLength="100">
            @JsonProperty("Alternativartikel")
            @Nullable
            String alternativartikel,//Type=	"Edm.String" MaxLength="150">
            @JsonProperty("Zuschlag_Percent")
            @Nullable
            String zuschlagPercent,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Indirect_Cost_Percent")
            @Nullable
            String indicrectCostPercent,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Rundungsmethodencode")
            @Nullable
            String rundungsmethodencade,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Suchbaum")
            @Nullable
            String suchbaum,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Sollzeit")
            @Nullable
            String sollzeit,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Neu_gebraucht")
            @Nullable
            String neuGebraucht,//Type=	"Edm.String">
            @JsonProperty("Artikel_für_Fremd_Betankung")
            @Nullable
            Boolean artikelFuerFremdbetankung,//Type=	"Edm.Boolean">
            @JsonProperty("Artikelzuschlagsgruppe")
            @Nullable
            String artikelzuschlagsgruppe,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Geprüft")
            @Nullable
            Boolean geprueft, //Type=	"Edm.Boolean">
            @JsonProperty("Datensatz_Gesperrt")
            @Nullable
            String datensatzGesperrt,//Type=	"Edm.String">
            @JsonProperty("Sperrhinweis")
            @Nullable
            String sperrhinweis, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Bemerkung_II")
            @Nullable
            Boolean bemerkungII,//Type=	"Edm.Boolean">
            @JsonProperty("Gültig_ab")
            @Nullable
            String gueltigAb,//Type=	"Edm.Date">
            @JsonProperty("Gültig_bis")
            @Nullable
            String gueltigBis,//Type=	"Edm.Date">
            @JsonProperty("Assembly_BOM")
            @Nullable
            Boolean assemblyBOM,//Type=	"Edm.Boolean">
            @JsonProperty("Metallzuschlag_DEL")
            @Nullable
            Boolean metallzuschlagDEL,//Type=	"Edm.Boolean">
            @JsonProperty("Lagerfachcode")
            @Nullable
            String lagerfachcode,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Standardlagerfach")
            @Nullable
            String standardlagerfach, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Gefahrgut")
            @Nullable
            Boolean gefahrgut,//Type=	"Edm.Boolean">
            @JsonProperty("Lagerbestand_II")
            @Nullable
            String lagerbestandII,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Inhaltsstoff")
            @Nullable
            String inhaltsstoffe,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Abfallklasse")
            @Nullable
            String abfallklasse,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Sales_Unit_of_Measure")
            @Nullable
            String salesUnitOfMeasure,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Verpackungsart")
            @Nullable
            String verpackungsart, //Type=	"Edm.String" MaxLength="30">
            @JsonProperty("Artikelverfolgungscode")
            @Nullable
            String artikelverfolgungscode,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Chargennummern")
            @Nullable
            String chargennummern, //Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Ablaufdatumsformel")
            @Nullable
            String ablaufdatumsformel,//Type=	"Edm.String">
            @JsonProperty("Serial_Nos_II")
            @Nullable
            String serialNosII,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Blocked")
            @Nullable
            Boolean blocked, //Type=	"Edm.Boolean">
            @JsonProperty("Gerätebestand")
            @Nullable
            String geraetebestand, //Type=	"Edm.String">
            @JsonProperty("ist_Schüttgut")
            @Nullable
            Boolean istSchuettgut,//Type=	"Edm.Boolean">
            @JsonProperty("Zeilenart_Werkstattbericht")
            @Nullable
            String zeilenartWerkstattbericht, //Type=	"Edm.String">
            @JsonProperty("Zuschlagsbetrag")
            @Nullable
            String zuschlagsbetrag, //Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Mengeneinheit_Zuschlag")
            @Nullable
            String mengeneinheitZuschlag, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Zugang_Menge_II")
            @Nullable
            String zugangMengeII,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Abgang_Menge_II")
            @Nullable
            String abgangMengeII,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Kontierungshilfe_Wartung")
            @Nullable
            String kontierungshilfeWartung, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Kategorie")
            @Nullable
            String kategorie,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Neuanlagesystem")
            @Nullable
            String neuanlagesystem, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Produktbuchungsgruppe_Wartung")
            @Nullable
            String produktbuchungsgruppeWartung, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Herkunftscode_Abwertung")
            @Nullable
            String herkunftscodeAbwertung, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Neuanlagebenutzer")
            @Nullable
            String neuanlagenbenutzer, //Type=	"Edm.String" MaxLength="50">
            @JsonProperty("Datum_der_Abwertung")
            @Nullable
            String datumDerAbwertung,//Type=	"Edm.Date">
            @JsonProperty("Wartungsprozentsatz")
            @Nullable
            String wartungsprozentsatz,//Type=	"Edm.Decimal" Scale="Variable">
            @JsonProperty("Neuanlagedatum")
            @Nullable
            String neuanlagendatum,//Type=	"Edm.Date">
            @JsonProperty("Neuanlagezeit")
            @Nullable
            String neuanlagenzeit,//Type=	"Edm.String">
            @JsonProperty("Änderungssystem")
            @Nullable
            String aenderungssystem, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Änderungsbenutzer")
            @Nullable
            String aenderungsbenutzer, //Type=	"Edm.String" MaxLength="50">
            @JsonProperty("Änderungsdatum")
            @Nullable
            String aenderungsdatum, //Type=	"Edm.Date">
            @JsonProperty("Änderungszeit")
            @Nullable
            String aenderungszeit, //Type=	"Edm.String">
            @JsonProperty("Location_Filter")
            @Nullable
            String locationFilter, //Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Global_Dimension_1_Filter")
            @Nullable
            String globalDimension1Filter, //Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Global_Dimension_2_Filter")
            @Nullable
            String globalDimension2Filter, //Type=	"Edm.String" MaxLength="20">
            @JsonProperty("Drop_Shipment_Filter")
            @Nullable
            String dropShipmentFilter,//Type=	"Edm.String">
            @JsonProperty("Variant_Filter")
            @Nullable
            String variantFilter,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("BAU_Lagerfachfilter_II")
            @Nullable
            String lagerfachfilterII,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("Date_Filter")
            @Nullable
            String dateFilter,//Type=	"Edm.String">
            @JsonProperty("BAU_Spediteurfilter")
            @Nullable
            String spediteurfilter,//Type=	"Edm.String" MaxLength="10">
            @JsonProperty("BAU_Artikelpostennummerfilter")
            @Nullable
            String artikelpostennummerfilter,//Type=	"Edm.String">
            @JsonProperty("BAU_Seriennr_Filter")
            @Nullable
            String seriennrFilter,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("BAU_Chargennr_Filter")
            @Nullable
            String chargennrFilter,//Type=	"Edm.String" MaxLength="20">
            @JsonProperty("BAU_ProduktionsKStnfilter")
            @Nullable
            String produktionsKstnFilter,
            @JsonProperty("BAU_ProduktionsKStnfilter_Link")
            @Nullable
            String produktionsKstnFilterLink,
            @JsonProperty("Country_Region_of_Origin_Code_Link")
            @Nullable
            String countryRegionOfOriginCodeLink
    ) {
        this.odataContext = odataContext;
        this.odataEtag = odataEtag;
        this.no = no;
        this.markierung = markierung;
        this.description = description;
        this.searchDescription = searchDescription;
        this.description2 = description2;
        this.baseUnitOfMeasure = baseUnitOfMeasure;
        this.inventoryPostingGroup = inventoryPostingGroup;
        this.verkMengenrabattcodeII = verkMengenrabattcodeII;
        this.itemDiscGroup = itemDiscGroup;
        this.allowInvoiceDisc = allowInvoiceDisc;
        this.unit_price = unit_price;
        this.vkPreisDCBerechnungII = vkPreisDCBerechnungII;
        this.profitPercent = profitPercent;
        this.costingMethod = costingMethod;
        this.unitCost = unitCost;
        this.standardCost = standardCost;
        this.lastDirectCost = lastDirectCost;
        this.einstandspreisDurchschnII = einstandspreisDurchschnII;
        this.vendorNo = vendorNo;
        this.kreditorenname = kreditorenname;
        this.vendorItemNo = vendorItemNo;
        this.leadTimeCalculation = leadTimeCalculation;
        this.reorderPoint = reorderPoint;
        this.maximumInventory = maximumInventory;
        this.reorderQuantity = reorderQuantity;
        this.dispositionsmethodencode = dispositionsmethodencode;
        this.lossgroessenrundungsfaktor = lossgroessenrundungsfaktor;
        this.grossWeight = grossWeight;
        this.netWeight = netWeight;
        this.tariffNo = tariffNo;
        this.priceIncludesVat = priceIncludesVat;
        this.VATBusPostingGrPrice = VATBusPostingGrPrice;
        this.GenProdPostingGroup = GenProdPostingGroup;
        this.countryRegionOfOriginCode = countryRegionOfOriginCode;
        this.automaticExtTexts = automaticExtTexts;
        this.VATProdPostingGroup = VATProdPostingGroup;
        this.lastUnitCostCalcDate = lastUnitCostCalcDate;
        this.inventoryValueZero = inventoryValueZero;
        this.artikelTyp = artikelTyp;
        this.dummyartikel = dummyartikel;
        this.hersteller = hersteller;
        this.herstellerNr = herstellerNr;
        this.stammlieferant = stammlieferant;
        this.farbe = farbe;
        this.fabrikatNr = fabrikatNr;
        this.eANNr = eANNr;
        this.gruppe = gruppe;
        this.gruppenstufe = gruppenstufe;
        this.gruppenstufe2 = gruppenstufe2;
        this.gruppenstufe3 = gruppenstufe3;
        this.gruppenstufe4 = gruppenstufe4;
        this.gruppenstufe5 = gruppenstufe5;
        this.bALArtNeu = bALArtNeu;
        this.bALNrNeu = bALNrNeu;
        this.bALArtAlt = bALArtAlt;
        this.bALNrAlt = bALNrAlt;
        this.preisGiltProMenge = preisGiltProMenge;
        this.verrechnungspreisIntern = verrechnungspreisIntern;
        this.kontierungshilfeREBU = kontierungshilfeREBU;
        this.kontierungshilfeIntern = kontierungshilfeIntern;
        this.kostentraeger = kostentraeger;
        this.textbausteinIntern = textbausteinIntern;
        this.arbeitsmittelpreis = arbeitsmittelpreis;
        this.arbeitsmittelpreis2 = arbeitsmittelpreis2;
        this.arbeitsmittelpreis3 = arbeitsmittelpreis3;
        this.textZuPreis1 = textZuPreis1;
        this.textZuPreis2 = textZuPreis2;
        this.textZuPreis3 = textZuPreis3;
        this.kalkArbeitsmittelpreis = kalkArbeitsmittelpreis;
        this.kalkArbeitsmittelpreis2 = kalkArbeitsmittelpreis2;
        this.kalkArbeitsmittelpreis3 = kalkArbeitsmittelpreis3;
        this.kalkMengeneinheit = kalkMengeneinheit;
        this.kalkulationsgewicht = kalkulationsgewicht;
        this.gewichtseinheit = gewichtseinheit;
        this.kostenartBBA = kostenartBBA;
        this.hKAKalkulation = hKAKalkulation;
        this.kAKalkulation = kAKalkulation;
        this.istMaterial = istMaterial;
        this.alternativeItemNo = alternativeItemNo;
        this.alternativeBeschreibung = alternativeBeschreibung;
        this.alternativartikel = alternativartikel;
        this.zuschlagPercent = zuschlagPercent;
        this.indicrectCostPercent = indicrectCostPercent;
        this.rundungsmethodencade = rundungsmethodencade;
        this.suchbaum = suchbaum;
        this.sollzeit = sollzeit;
        this.neuGebraucht = neuGebraucht;
        this.artikelFuerFremdbetankung = artikelFuerFremdbetankung;
        this.artikelzuschlagsgruppe = artikelzuschlagsgruppe;
        this.geprueft = geprueft;
        this.datensatzGesperrt = datensatzGesperrt;
        this.sperrhinweis = sperrhinweis;
        this.bemerkungII = bemerkungII;
        this.gueltigAb = gueltigAb;
        this.gueltigBis = gueltigBis;
        this.assemblyBOM = assemblyBOM;
        this.metallzuschlagDEL = metallzuschlagDEL;
        this.lagerfachcode = lagerfachcode;
        this.standardlagerfach = standardlagerfach;
        this.gefahrgut = gefahrgut;
        this.lagerbestandII = lagerbestandII;
        this.inhaltsstoffe = inhaltsstoffe;
        this.abfallklasse = abfallklasse;
        this.salesUnitOfMeasure = salesUnitOfMeasure;
        this.verpackungsart = verpackungsart;
        this.artikelverfolgungscode = artikelverfolgungscode;
        this.chargennummern = chargennummern;
        this.ablaufdatumsformel = ablaufdatumsformel;
        this.serialNosII = serialNosII;
        this.blocked = blocked;
        this.geraetebestand = geraetebestand;
        this.istSchuettgut = istSchuettgut;
        this.zeilenartWerkstattbericht = zeilenartWerkstattbericht;
        this.zuschlagsbetrag = zuschlagsbetrag;
        this.mengeneinheitZuschlag = mengeneinheitZuschlag;
        this.zugangMengeII = zugangMengeII;
        this.abgangMengeII = abgangMengeII;
        this.kontierungshilfeWartung = kontierungshilfeWartung;
        this.kategorie = kategorie;
        this.neuanlagesystem = neuanlagesystem;
        this.produktbuchungsgruppeWartung = produktbuchungsgruppeWartung;
        this.herkunftscodeAbwertung = herkunftscodeAbwertung;
        this.neuanlagenbenutzer = neuanlagenbenutzer;
        this.datumDerAbwertung = datumDerAbwertung;
        this.wartungsprozentsatz = wartungsprozentsatz;
        this.neuanlagendatum = neuanlagendatum;
        this.neuanlagenzeit = neuanlagenzeit;
        this.aenderungssystem = aenderungssystem;
        this.aenderungsbenutzer = aenderungsbenutzer;
        this.aenderungsdatum = aenderungsdatum;
        this.aenderungszeit = aenderungszeit;
        this.locationFilter = locationFilter;
        this.globalDimension1Filter = globalDimension1Filter;
        this.globalDimension2Filter = globalDimension2Filter;
        this.dropShipmentFilter = dropShipmentFilter;
        this.variantFilter = variantFilter;
        this.lagerfachfilterII = lagerfachfilterII;
        this.dateFilter = dateFilter;
        this.spediteurfilter = spediteurfilter;
        this.artikelpostennummerfilter = artikelpostennummerfilter;
        this.seriennrFilter = seriennrFilter;
        this.chargennrFilter = chargennrFilter;
        this.produktionsKstnFilter = produktionsKstnFilter;
        this.produktionsKstnFilterLink = produktionsKstnFilterLink;
        this.countryRegionOfOriginCodeLink = countryRegionOfOriginCodeLink;
    }

    @JsonProperty("@odata.context")
    @JsonIgnore
    public String odataContext() {
        return odataContext;
    }

    @JsonProperty("@odata.etag")
    @JsonIgnore
    public String odataEtag() {
        return odataEtag;
    }

    @JsonProperty("No")
    @Nonnull
    public String no() {
        return no;
    }

    @JsonProperty("Makierung")
    @Nullable
    public Boolean markierung() {
        return markierung;
    }

    @JsonProperty("Description")
    @Nullable
    public String description() {
        return description;
    }

    @JsonProperty("Search_Description")
    @Nullable
    public String searchDescription() {
        return searchDescription;
    }

    @JsonProperty("Description_2")
    @Nullable
    public String description2() {
        return description2;
    }

    @JsonProperty("Base_Unit_of_Measure")
    @Nullable
    public String baseUnitOfMeasure() {
        return baseUnitOfMeasure;
    }

    @JsonProperty("Inventory_Posting_Group")
    @Nullable
    public String inventoryPostingGroup() {
        return inventoryPostingGroup;
    }

    @JsonProperty("Verk_Mengenrabattcode_II")
    @Nullable
    public String verkMengenrabattcodeII() {
        return verkMengenrabattcodeII;
    }

    @JsonProperty("Item_Disc_Group")
    @Nullable
    public String itemDiscGroup() {
        return itemDiscGroup;
    }

    @JsonProperty("Allow_Invoice_Disc")
    @Nullable
    public Boolean allowInvoiceDisc() {
        return allowInvoiceDisc;
    }

    @JsonProperty("Unit_Price")
    @Nullable
    public String unit_price() {
        return unit_price;
    }

    @JsonProperty("VK_Preis_DB_Berechnung_II")
    @Nullable
    public String vkPreisDCBerechnungII() {
        return vkPreisDCBerechnungII;
    }

    @JsonProperty("Profit_Percent")
    @Nullable
    public String profitPercent() {
        return profitPercent;
    }

    @JsonProperty("Costing_Method")
    @Nullable
    public String costingMethod() {
        return costingMethod;
    }

    @JsonProperty("Unit_Cost")
    @Nullable
    public String unitCost() {
        return unitCost;
    }

    @JsonProperty("Standard_Cost")
    @Nullable
    public String standardCost() {
        return standardCost;
    }

    @JsonProperty("Last_Direct_Cost")
    @Nullable
    public String lastDirectCost() {
        return lastDirectCost;
    }

    @JsonProperty("Einstandspreis_durchschn_II")
    @Nullable
    public String einstandspreisDurchschnII() {
        return einstandspreisDurchschnII;
    }

    @JsonProperty("Vendor_No")
    @Nullable
    public String vendorNo() {
        return vendorNo;
    }

    @JsonProperty("Kreditorenname")
    @Nullable
    public String kreditorenname() {
        return kreditorenname;
    }

    @JsonProperty("Vendor_Item_No")
    @Nullable
    public String vendorItemNo() {
        return vendorItemNo;
    }

    @JsonProperty("Lead_Time_Calculation")
    @Nullable
    public String leadTimeCalculation() {
        return leadTimeCalculation;
    }

    @JsonProperty("Reorder_Point")
    @Nullable
    public String reorderPoint() {
        return reorderPoint;
    }

    @JsonProperty("Maximum_Inventory")
    @Nullable
    public String maximumInventory() {
        return maximumInventory;
    }

    @JsonProperty("Reorder_Quantity")
    @Nullable
    public String reorderQuantity() {
        return reorderQuantity;
    }

    @JsonProperty("Dispositionsmethodencode")
    @Nullable
    public String dispositionsmethodencode() {
        return dispositionsmethodencode;
    }

    @JsonProperty("Losgrößenrundungsfaktor")
    @Nullable
    public String lossgroessenrundungsfaktor() {
        return lossgroessenrundungsfaktor;
    }

    @JsonProperty("Gross_Weight")
    @Nullable
    public String grossWeight() {
        return grossWeight;
    }

    @JsonProperty("Net_Weight")
    @Nullable
    public String netWeight() {
        return netWeight;
    }

    @JsonProperty("Tariff_No")
    @Nullable
    public String tariffNo() {
        return tariffNo;
    }

    @JsonProperty("Price_Includes_VAT")
    @Nullable
    public Boolean priceIncludesVat() {
        return priceIncludesVat;
    }

    @JsonProperty("VAT_Bus_Posting_Gr_Price")
    @Nullable
    public String VATBusPostingGrPrice() {
        return VATBusPostingGrPrice;
    }

    @JsonProperty("Gen_Prod_Posting_Group")
    @Nullable
    public String GenProdPostingGroup() {
        return GenProdPostingGroup;
    }

    @JsonProperty("Country_Region_of_Origin_Code")
    @Nullable
    public String countryRegionOfOriginCode() {
        return countryRegionOfOriginCode;
    }

    @JsonProperty("Automatic_Ext_Texts")
    @Nullable
    public Boolean automaticExtTexts() {
        return automaticExtTexts;
    }

    @JsonProperty("VAT_Prod_Posting_Group")
    @Nullable
    public String VATProdPostingGroup() {
        return VATProdPostingGroup;
    }

    @JsonProperty("Last_Unit_Cost_Calc_Date")
    @Nullable
    public String lastUnitCostCalcDate() {
        return lastUnitCostCalcDate;
    }

    @JsonProperty("Inventory_Value_Zero")
    @Nullable
    public Boolean inventoryValueZero() {
        return inventoryValueZero;
    }

    @JsonProperty("Artikeltyp")
    @Nullable
    public String artikelTyp() {
        return artikelTyp;
    }

    @JsonProperty("Dummyartikel")
    @Nullable
    public Boolean dummyartikel() {
        return dummyartikel;
    }

    @JsonProperty("Hersteller")
    @Nullable
    public String hersteller() {
        return hersteller;
    }

    @JsonProperty("Hersteller_Nr")
    @Nullable
    public String herstellerNr() {
        return herstellerNr;
    }

    @JsonProperty("Stammlieferant")
    @Nullable
    public String stammlieferant() {
        return stammlieferant;
    }

    @JsonProperty("Farbe")
    @Nullable
    public String farbe() {
        return farbe;
    }

    @JsonProperty("Fabrikat_Nr")
    @Nullable
    public String fabrikatNr() {
        return fabrikatNr;
    }

    @JsonProperty("EAN_Nr")
    @Nullable
    public String eANNr() {
        return eANNr;
    }

    @JsonProperty("Gruppe")
    @Nullable
    public String gruppe() {
        return gruppe;
    }

    @JsonProperty("Gruppenstufe")
    @Nullable
    public String gruppenstufe() {
        return gruppenstufe;
    }

    @JsonProperty("Gruppenstufe2")
    @Nullable
    public String gruppenstufe2() {
        return gruppenstufe2;
    }

    @JsonProperty("Gruppenstufe3")
    @Nullable
    public String gruppenstufe3() {
        return gruppenstufe3;
    }

    @JsonProperty("Gruppenstufe4")
    @Nullable
    public String gruppenstufe4() {
        return gruppenstufe4;
    }

    @JsonProperty("Gruppenstufe5")
    @Nullable
    public String gruppenstufe5() {
        return gruppenstufe5;
    }

    @JsonProperty("BAL_Art_neu")
    @Nullable
    public String bALArtNeu() {
        return bALArtNeu;
    }

    @JsonProperty("BAL_Nr_neu")
    @Nullable
    public String bALNrNeu() {
        return bALNrNeu;
    }

    @JsonProperty("BAL_Art_alt")
    @Nullable
    public String bALArtAlt() {
        return bALArtAlt;
    }

    @JsonProperty("BAL_Nr_alt")
    @Nullable
    public String bALNrAlt() {
        return bALNrAlt;
    }

    @JsonProperty("Preis_gilt_pro_Menge")
    @Nullable
    public String preisGiltProMenge() {
        return preisGiltProMenge;
    }

    @JsonProperty("Verrechnungspreis_intern")
    @Nullable
    public String verrechnungspreisIntern() {
        return verrechnungspreisIntern;
    }

    @JsonProperty("Kontierungshilfe_REBU")
    @Nullable
    public String kontierungshilfeREBU() {
        return kontierungshilfeREBU;
    }

    @JsonProperty("Kontierungshilfe_intern")
    @Nullable
    public String kontierungshilfeIntern() {
        return kontierungshilfeIntern;
    }

    @JsonProperty("Kostenträger")
    @Nullable
    public String kostentraeger() {
        return kostentraeger;
    }

    @JsonProperty("Textbaustein_intern")
    @Nullable
    public String textbausteinIntern() {
        return textbausteinIntern;
    }

    @JsonProperty("Arbeitsmittelpreis")
    @Nullable
    public String arbeitsmittelpreis() {
        return arbeitsmittelpreis;
    }

    @JsonProperty("Arbeitsmittelpreis_2")
    @Nullable
    public String arbeitsmittelpreis2() {
        return arbeitsmittelpreis2;
    }

    @JsonProperty("Arbeitsmittelpreis_3")
    @Nullable
    public String arbeitsmittelpreis3() {
        return arbeitsmittelpreis3;
    }

    @JsonProperty("Text_zu_Preis_1")
    @Nullable
    public String textZuPreis1() {
        return textZuPreis1;
    }

    @JsonProperty("Text_zu_Preis_2")
    @Nullable
    public String textZuPreis2() {
        return textZuPreis2;
    }

    @JsonProperty("Text_zu_Preis_3")
    @Nullable
    public String textZuPreis3() {
        return textZuPreis3;
    }

    @JsonProperty("kalk_Arbeitsmittelpreis")
    @Nullable
    public String kalkArbeitsmittelpreis() {
        return kalkArbeitsmittelpreis;
    }

    @JsonProperty("kalk_Arbeitsmittelpreis_2")
    @Nullable
    public String kalkArbeitsmittelpreis2() {
        return kalkArbeitsmittelpreis2;
    }

    @JsonProperty("kalk_Arbeitsmittelpreis_3")
    @Nullable
    public String kalkArbeitsmittelpreis3() {
        return kalkArbeitsmittelpreis3;
    }

    @JsonProperty("kalk_Mengeneinheit")
    @Nullable
    public String kalkMengeneinheit() {
        return kalkMengeneinheit;
    }

    @JsonProperty("Kalkulationsgewicht")
    @Nullable
    public String kalkulationsgewicht() {
        return kalkulationsgewicht;
    }

    @JsonProperty("Gewichtseinheit")
    @Nullable
    public String gewichtseinheit() {
        return gewichtseinheit;
    }

    @JsonProperty("Kostenart_BBA")
    @Nullable
    public String kostenartBBA() {
        return kostenartBBA;
    }

    @JsonProperty("HKA_Kalkulation")
    @Nullable
    public String hKAKalkulation() {
        return hKAKalkulation;
    }

    @JsonProperty("KA_Kalkulation")
    @Nullable
    public String kAKalkulation() {
        return kAKalkulation;
    }

    @JsonProperty("Ist_Material")
    @Nullable
    public Boolean istMaterial() {
        return istMaterial;
    }

    @JsonProperty("Alternative_Item_No")
    @Nullable
    public String alternativeItemNo() {
        return alternativeItemNo;
    }

    @JsonProperty("Alternativartikel_Beschreibung")
    @Nullable
    public String alternativeBeschreibung() {
        return alternativeBeschreibung;
    }

    @JsonProperty("Alternativartikel")
    @Nullable
    public String alternativartikel() {
        return alternativartikel;
    }

    @JsonProperty("Zuschlag_Percent")
    @Nullable
    public String zuschlagPercent() {
        return zuschlagPercent;
    }

    @JsonProperty("Indirect_Cost_Percent")
    @Nullable
    public String indicrectCostPercent() {
        return indicrectCostPercent;
    }

    @JsonProperty("Rundungsmethodencode")
    @Nullable
    public String rundungsmethodencade() {
        return rundungsmethodencade;
    }

    @JsonProperty("Suchbaum")
    @Nullable
    public String suchbaum() {
        return suchbaum;
    }

    @JsonProperty("Sollzeit")
    @Nullable
    public String sollzeit() {
        return sollzeit;
    }

    @JsonProperty("Neu_gebraucht")
    @Nullable
    public String neuGebraucht() {
        return neuGebraucht;
    }

    @JsonProperty("Artikel_für_Fremd_Betankung")
    @Nullable
    public Boolean artikelFuerFremdbetankung() {
        return artikelFuerFremdbetankung;
    }

    @JsonProperty("Artikelzuschlagsgruppe")
    @Nullable
    public String artikelzuschlagsgruppe() {
        return artikelzuschlagsgruppe;
    }

    @JsonProperty("Geprüft")
    @Nullable
    public Boolean geprueft() {
        return geprueft;
    }

    @JsonProperty("Datensatz_Gesperrt")
    @Nullable
    public String datensatzGesperrt() {
        return datensatzGesperrt;
    }

    @JsonProperty("Sperrhinweis")
    @Nullable
    public String sperrhinweis() {
        return sperrhinweis;
    }

    @JsonProperty("Bemerkung_II")
    @Nullable
    public Boolean bemerkungII() {
        return bemerkungII;
    }

    @JsonProperty("Gültig_ab")
    @Nullable
    public String gueltigAb() {
        return gueltigAb;
    }

    @JsonProperty("Gültig_bis")
    @Nullable
    public String gueltigBis() {
        return gueltigBis;
    }

    @JsonProperty("Assembly_BOM")
    @Nullable
    public Boolean assemblyBOM() {
        return assemblyBOM;
    }

    @JsonProperty("Metallzuschlag_DEL")
    @Nullable
    public Boolean metallzuschlagDEL() {
        return metallzuschlagDEL;
    }

    @JsonProperty("Lagerfachcode")
    @Nullable
    public String lagerfachcode() {
        return lagerfachcode;
    }

    @JsonProperty("Standardlagerfach")
    @Nullable
    public String standardlagerfach() {
        return standardlagerfach;
    }

    @JsonProperty("Gefahrgut")
    @Nullable
    public Boolean gefahrgut() {
        return gefahrgut;
    }

    @JsonProperty("Lagerbestand_II")
    @Nullable
    public String lagerbestandII() {
        return lagerbestandII;
    }

    @JsonProperty("Inhaltsstoff")
    @Nullable
    public String inhaltsstoffe() {
        return inhaltsstoffe;
    }

    @JsonProperty("Abfallklasse")
    @Nullable
    public String abfallklasse() {
        return abfallklasse;
    }

    @JsonProperty("Sales_Unit_of_Measure")
    @Nullable
    public String salesUnitOfMeasure() {
        return salesUnitOfMeasure;
    }

    @JsonProperty("Verpackungsart")
    @Nullable
    public String verpackungsart() {
        return verpackungsart;
    }

    @JsonProperty("Artikelverfolgungscode")
    @Nullable
    public String artikelverfolgungscode() {
        return artikelverfolgungscode;
    }

    @JsonProperty("Chargennummern")
    @Nullable
    public String chargennummern() {
        return chargennummern;
    }

    @JsonProperty("Ablaufdatumsformel")
    @Nullable
    public String ablaufdatumsformel() {
        return ablaufdatumsformel;
    }

    @JsonProperty("Serial_Nos_II")
    @Nullable
    public String serialNosII() {
        return serialNosII;
    }

    @JsonProperty("Blocked")
    @Nullable
    public Boolean blocked() {
        return blocked;
    }

    @JsonProperty("Gerätebestand")
    @Nullable
    public String geraetebestand() {
        return geraetebestand;
    }

    @JsonProperty("ist_Schüttgut")
    @Nullable
    public Boolean istSchuettgut() {
        return istSchuettgut;
    }

    @JsonProperty("Zeilenart_Werkstattbericht")
    @Nullable
    public String zeilenartWerkstattbericht() {
        return zeilenartWerkstattbericht;
    }

    @JsonProperty("Zuschlagsbetrag")
    @Nullable
    public String zuschlagsbetrag() {
        return zuschlagsbetrag;
    }

    @JsonProperty("Mengeneinheit_Zuschlag")
    @Nullable
    public String mengeneinheitZuschlag() {
        return mengeneinheitZuschlag;
    }

    @JsonProperty("Zugang_Menge_II")
    @Nullable
    public String zugangMengeII() {
        return zugangMengeII;
    }

    @JsonProperty("Abgang_Menge_II")
    @Nullable
    public String abgangMengeII() {
        return abgangMengeII;
    }

    @JsonProperty("Kontierungshilfe_Wartung")
    @Nullable
    public String kontierungshilfeWartung() {
        return kontierungshilfeWartung;
    }

    @JsonProperty("Kategorie")
    @Nullable
    public String kategorie() {
        return kategorie;
    }

    @JsonProperty("Neuanlagesystem")
    @Nullable
    public String neuanlagesystem() {
        return neuanlagesystem;
    }

    @JsonProperty("Produktbuchungsgruppe_Wartung")
    @Nullable
    public String produktbuchungsgruppeWartung() {
        return produktbuchungsgruppeWartung;
    }

    @JsonProperty("Herkunftscode_Abwertung")
    @Nullable
    public String herkunftscodeAbwertung() {
        return herkunftscodeAbwertung;
    }

    @JsonProperty("Neuanlagebenutzer")
    @Nullable
    public String neuanlagenbenutzer() {
        return neuanlagenbenutzer;
    }

    @JsonProperty("Datum_der_Abwertung")
    @Nullable
    public String datumDerAbwertung() {
        return datumDerAbwertung;
    }

    @JsonProperty("Wartungsprozentsatz")
    @Nullable
    public String wartungsprozentsatz() {
        return wartungsprozentsatz;
    }

    @JsonProperty("Neuanlagedatum")
    @Nullable
    public String neuanlagendatum() {
        return neuanlagendatum;
    }

    @JsonProperty("Neuanlagezeit")
    @Nullable
    public String neuanlagenzeit() {
        return neuanlagenzeit;
    }

    @JsonProperty("Änderungssystem")
    @Nullable
    public String aenderungssystem() {
        return aenderungssystem;
    }

    @JsonProperty("Änderungsbenutzer")
    @Nullable
    public String aenderungsbenutzer() {
        return aenderungsbenutzer;
    }

    @JsonProperty("Änderungsdatum")
    @Nullable
    public String aenderungsdatum() {
        return aenderungsdatum;
    }

    @JsonProperty("Änderungszeit")
    @Nullable
    public String aenderungszeit() {
        return aenderungszeit;
    }

    @Override
    @JsonIgnore
    public String getUUID() {
        return this.no;
    }

    @JsonProperty("Location_Filter")
    @Nullable
    public String locationFilter() {
        return locationFilter;
    }

    @JsonProperty("Global_Dimension_1_Filter")
    @Nullable
    public String globalDimension1Filter() {
        return globalDimension1Filter;
    }

    @JsonProperty("Global_Dimension_2_Filter")
    @Nullable
    public String globalDimension2Filter() {
        return globalDimension2Filter;
    }

    @JsonProperty("Drop_Shipment_Filter")
    @Nullable
    public String dropShipmentFilter() {
        return dropShipmentFilter;
    }

    @JsonProperty("Variant_Filter")
    @Nullable
    public String variantFilter() {
        return variantFilter;
    }

    @JsonProperty("Lagerfachfilter_II")
    @Nullable
    public String lagerfachfilterII() {
        return lagerfachfilterII;
    }


    @JsonProperty("Date_Filter")
    @Nullable
    public String dateFilter() {
        return dateFilter;
    }

    @JsonProperty("BAU_Spediteurfilter")
    @Nullable
    public String spediteurfilter() {
        return spediteurfilter;
    }

    @JsonProperty("BAU_Artikelpostennummerfilter")
    @Nullable
    public String artikelpostennummerfilter() {
        return artikelpostennummerfilter;
    }

    @JsonProperty("BAU_Seriennr_Filter")
    @Nullable
    public String seriennrFilter() {
        return seriennrFilter;
    }

    @JsonProperty("BAU_Chargennr_Filter")
    @Nullable
    public String chargennrFilter() {
        return chargennrFilter;
    }

    @JsonProperty("BAU_ProduktionsKStnfilter")
    @Nullable
    public String produktionsKstnFilter(){
        return produktionsKstnFilter;
    }

    @JsonProperty("BAU_ProduktionsKStnfilter_Link")
    @Nullable
    public String produktionsKstnFilterLink(){
        return produktionsKstnFilterLink;
    }
    @JsonProperty("Country_Region_of_Origin_Code_Link")
    @Nullable
    public String countryRegionOfOriginCodeLink(){
        return countryRegionOfOriginCodeLink;
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (NevarisArtikeluebersicht) obj;
        return Objects.equals(this.odataContext, that.odataContext) &&
                Objects.equals(this.odataEtag, that.odataEtag) &&
                Objects.equals(this.no, that.no) &&
                Objects.equals(this.markierung, that.markierung) &&
                Objects.equals(this.description, that.description) &&
                Objects.equals(this.searchDescription, that.searchDescription) &&
                Objects.equals(this.description2, that.description2) &&
                Objects.equals(this.baseUnitOfMeasure, that.baseUnitOfMeasure) &&
                Objects.equals(this.inventoryPostingGroup, that.inventoryPostingGroup) &&
                Objects.equals(this.verkMengenrabattcodeII, that.verkMengenrabattcodeII) &&
                Objects.equals(this.itemDiscGroup, that.itemDiscGroup) &&
                Objects.equals(this.allowInvoiceDisc, that.allowInvoiceDisc) &&
                Objects.equals(this.unit_price, that.unit_price) &&
                Objects.equals(this.vkPreisDCBerechnungII, that.vkPreisDCBerechnungII) &&
                Objects.equals(this.profitPercent, that.profitPercent) &&
                Objects.equals(this.costingMethod, that.costingMethod) &&
                Objects.equals(this.unitCost, that.unitCost) &&
                Objects.equals(this.standardCost, that.standardCost) &&
                Objects.equals(this.lastDirectCost, that.lastDirectCost) &&
                Objects.equals(this.einstandspreisDurchschnII, that.einstandspreisDurchschnII) &&
                Objects.equals(this.vendorNo, that.vendorNo) &&
                Objects.equals(this.kreditorenname, that.kreditorenname) &&
                Objects.equals(this.vendorItemNo, that.vendorItemNo) &&
                Objects.equals(this.leadTimeCalculation, that.leadTimeCalculation) &&
                Objects.equals(this.reorderPoint, that.reorderPoint) &&
                Objects.equals(this.maximumInventory, that.maximumInventory) &&
                Objects.equals(this.reorderQuantity, that.reorderQuantity) &&
                Objects.equals(this.dispositionsmethodencode, that.dispositionsmethodencode) &&
                Objects.equals(this.lossgroessenrundungsfaktor, that.lossgroessenrundungsfaktor) &&
                Objects.equals(this.grossWeight, that.grossWeight) &&
                Objects.equals(this.netWeight, that.netWeight) &&
                Objects.equals(this.tariffNo, that.tariffNo) &&
                Objects.equals(this.priceIncludesVat, that.priceIncludesVat) &&
                Objects.equals(this.VATBusPostingGrPrice, that.VATBusPostingGrPrice) &&
                Objects.equals(this.GenProdPostingGroup, that.GenProdPostingGroup) &&
                Objects.equals(this.countryRegionOfOriginCode, that.countryRegionOfOriginCode) &&
                Objects.equals(this.automaticExtTexts, that.automaticExtTexts) &&
                Objects.equals(this.VATProdPostingGroup, that.VATProdPostingGroup) &&
                Objects.equals(this.lastUnitCostCalcDate, that.lastUnitCostCalcDate) &&
                Objects.equals(this.inventoryValueZero, that.inventoryValueZero) &&
                Objects.equals(this.artikelTyp, that.artikelTyp) &&
                Objects.equals(this.dummyartikel, that.dummyartikel) &&
                Objects.equals(this.hersteller, that.hersteller) &&
                Objects.equals(this.herstellerNr, that.herstellerNr) &&
                Objects.equals(this.stammlieferant, that.stammlieferant) &&
                Objects.equals(this.farbe, that.farbe) &&
                Objects.equals(this.fabrikatNr, that.fabrikatNr) &&
                Objects.equals(this.eANNr, that.eANNr) &&
                Objects.equals(this.gruppe, that.gruppe) &&
                Objects.equals(this.gruppenstufe, that.gruppenstufe) &&
                Objects.equals(this.gruppenstufe2, that.gruppenstufe2) &&
                Objects.equals(this.gruppenstufe3, that.gruppenstufe3) &&
                Objects.equals(this.gruppenstufe4, that.gruppenstufe4) &&
                Objects.equals(this.gruppenstufe5, that.gruppenstufe5) &&
                Objects.equals(this.bALArtNeu, that.bALArtNeu) &&
                Objects.equals(this.bALNrNeu, that.bALNrNeu) &&
                Objects.equals(this.bALArtAlt, that.bALArtAlt) &&
                Objects.equals(this.bALNrAlt, that.bALNrAlt) &&
                Objects.equals(this.preisGiltProMenge, that.preisGiltProMenge) &&
                Objects.equals(this.verrechnungspreisIntern, that.verrechnungspreisIntern) &&
                Objects.equals(this.kontierungshilfeREBU, that.kontierungshilfeREBU) &&
                Objects.equals(this.kontierungshilfeIntern, that.kontierungshilfeIntern) &&
                Objects.equals(this.kostentraeger, that.kostentraeger) &&
                Objects.equals(this.textbausteinIntern, that.textbausteinIntern) &&
                Objects.equals(this.arbeitsmittelpreis, that.arbeitsmittelpreis) &&
                Objects.equals(this.arbeitsmittelpreis2, that.arbeitsmittelpreis2) &&
                Objects.equals(this.arbeitsmittelpreis3, that.arbeitsmittelpreis3) &&
                Objects.equals(this.textZuPreis1, that.textZuPreis1) &&
                Objects.equals(this.textZuPreis2, that.textZuPreis2) &&
                Objects.equals(this.textZuPreis3, that.textZuPreis3) &&
                Objects.equals(this.kalkArbeitsmittelpreis, that.kalkArbeitsmittelpreis) &&
                Objects.equals(this.kalkArbeitsmittelpreis2, that.kalkArbeitsmittelpreis2) &&
                Objects.equals(this.kalkArbeitsmittelpreis3, that.kalkArbeitsmittelpreis3) &&
                Objects.equals(this.kalkMengeneinheit, that.kalkMengeneinheit) &&
                Objects.equals(this.kalkulationsgewicht, that.kalkulationsgewicht) &&
                Objects.equals(this.gewichtseinheit, that.gewichtseinheit) &&
                Objects.equals(this.kostenartBBA, that.kostenartBBA) &&
                Objects.equals(this.hKAKalkulation, that.hKAKalkulation) &&
                Objects.equals(this.kAKalkulation, that.kAKalkulation) &&
                Objects.equals(this.istMaterial, that.istMaterial) &&
                Objects.equals(this.alternativeItemNo, that.alternativeItemNo) &&
                Objects.equals(this.alternativeBeschreibung, that.alternativeBeschreibung) &&
                Objects.equals(this.alternativartikel, that.alternativartikel) &&
                Objects.equals(this.zuschlagPercent, that.zuschlagPercent) &&
                Objects.equals(this.indicrectCostPercent, that.indicrectCostPercent) &&
                Objects.equals(this.rundungsmethodencade, that.rundungsmethodencade) &&
                Objects.equals(this.suchbaum, that.suchbaum) &&
                Objects.equals(this.sollzeit, that.sollzeit) &&
                Objects.equals(this.neuGebraucht, that.neuGebraucht) &&
                Objects.equals(this.artikelFuerFremdbetankung, that.artikelFuerFremdbetankung) &&
                Objects.equals(this.artikelzuschlagsgruppe, that.artikelzuschlagsgruppe) &&
                Objects.equals(this.geprueft, that.geprueft) &&
                Objects.equals(this.datensatzGesperrt, that.datensatzGesperrt) &&
                Objects.equals(this.sperrhinweis, that.sperrhinweis) &&
                Objects.equals(this.bemerkungII, that.bemerkungII) &&
                Objects.equals(this.gueltigAb, that.gueltigAb) &&
                Objects.equals(this.gueltigBis, that.gueltigBis) &&
                Objects.equals(this.assemblyBOM, that.assemblyBOM) &&
                Objects.equals(this.metallzuschlagDEL, that.metallzuschlagDEL) &&
                Objects.equals(this.lagerfachcode, that.lagerfachcode) &&
                Objects.equals(this.standardlagerfach, that.standardlagerfach) &&
                Objects.equals(this.gefahrgut, that.gefahrgut) &&
                Objects.equals(this.lagerbestandII, that.lagerbestandII) &&
                Objects.equals(this.inhaltsstoffe, that.inhaltsstoffe) &&
                Objects.equals(this.abfallklasse, that.abfallklasse) &&
                Objects.equals(this.salesUnitOfMeasure, that.salesUnitOfMeasure) &&
                Objects.equals(this.verpackungsart, that.verpackungsart) &&
                Objects.equals(this.artikelverfolgungscode, that.artikelverfolgungscode) &&
                Objects.equals(this.chargennummern, that.chargennummern) &&
                Objects.equals(this.ablaufdatumsformel, that.ablaufdatumsformel) &&
                Objects.equals(this.serialNosII, that.serialNosII) &&
                Objects.equals(this.blocked, that.blocked) &&
                Objects.equals(this.geraetebestand, that.geraetebestand) &&
                Objects.equals(this.istSchuettgut, that.istSchuettgut) &&
                Objects.equals(this.zeilenartWerkstattbericht, that.zeilenartWerkstattbericht) &&
                Objects.equals(this.zuschlagsbetrag, that.zuschlagsbetrag) &&
                Objects.equals(this.mengeneinheitZuschlag, that.mengeneinheitZuschlag) &&
                Objects.equals(this.zugangMengeII, that.zugangMengeII) &&
                Objects.equals(this.abgangMengeII, that.abgangMengeII) &&
                Objects.equals(this.kontierungshilfeWartung, that.kontierungshilfeWartung) &&
                Objects.equals(this.kategorie, that.kategorie) &&
                Objects.equals(this.neuanlagesystem, that.neuanlagesystem) &&
                Objects.equals(this.produktbuchungsgruppeWartung, that.produktbuchungsgruppeWartung) &&
                Objects.equals(this.herkunftscodeAbwertung, that.herkunftscodeAbwertung) &&
                Objects.equals(this.neuanlagenbenutzer, that.neuanlagenbenutzer) &&
                Objects.equals(this.datumDerAbwertung, that.datumDerAbwertung) &&
                Objects.equals(this.wartungsprozentsatz, that.wartungsprozentsatz) &&
                Objects.equals(this.neuanlagendatum, that.neuanlagendatum) &&
                Objects.equals(this.neuanlagenzeit, that.neuanlagenzeit) &&
                Objects.equals(this.aenderungssystem, that.aenderungssystem) &&
                Objects.equals(this.aenderungsbenutzer, that.aenderungsbenutzer) &&
                Objects.equals(this.aenderungsdatum, that.aenderungsdatum) &&
                Objects.equals(this.aenderungszeit, that.aenderungszeit) &&
                Objects.equals(this.locationFilter, that.locationFilter) &&
                Objects.equals(this.globalDimension1Filter, that.globalDimension1Filter) &&
                Objects.equals(this.globalDimension2Filter, that.globalDimension2Filter) &&
                Objects.equals(this.dropShipmentFilter, that.dropShipmentFilter) &&
                Objects.equals(this.variantFilter, that.variantFilter) &&
                Objects.equals(this.lagerfachfilterII, that.lagerfachfilterII) &&
                Objects.equals(this.dateFilter, that.dateFilter) &&
                Objects.equals(this.spediteurfilter, that.spediteurfilter) &&
                Objects.equals(this.artikelpostennummerfilter, that.artikelpostennummerfilter) &&
                Objects.equals(this.seriennrFilter, that.seriennrFilter) &&
                Objects.equals(this.chargennrFilter, that.chargennrFilter);
    }

    @Override
    public int hashCode() {
        return Objects.hash(odataContext, odataEtag, no, markierung, description, searchDescription, description2, baseUnitOfMeasure, inventoryPostingGroup, verkMengenrabattcodeII, itemDiscGroup, allowInvoiceDisc, unit_price, vkPreisDCBerechnungII, profitPercent, costingMethod, unitCost, standardCost, lastDirectCost, einstandspreisDurchschnII, vendorNo, kreditorenname, vendorItemNo, leadTimeCalculation, reorderPoint, maximumInventory, reorderQuantity, dispositionsmethodencode, lossgroessenrundungsfaktor, grossWeight, netWeight, tariffNo, priceIncludesVat, VATBusPostingGrPrice, GenProdPostingGroup, countryRegionOfOriginCode, automaticExtTexts, VATProdPostingGroup, lastUnitCostCalcDate, inventoryValueZero, artikelTyp, dummyartikel, hersteller, herstellerNr, stammlieferant, farbe, fabrikatNr, eANNr, gruppe, gruppenstufe, gruppenstufe2, gruppenstufe3, gruppenstufe4, gruppenstufe5, bALArtNeu, bALNrNeu, bALArtAlt, bALNrAlt, preisGiltProMenge, verrechnungspreisIntern, kontierungshilfeREBU, kontierungshilfeIntern, kostentraeger, textbausteinIntern, arbeitsmittelpreis, arbeitsmittelpreis2, arbeitsmittelpreis3, textZuPreis1, textZuPreis2, textZuPreis3, kalkArbeitsmittelpreis, kalkArbeitsmittelpreis2, kalkArbeitsmittelpreis3, kalkMengeneinheit, kalkulationsgewicht, gewichtseinheit, kostenartBBA, hKAKalkulation, kAKalkulation, istMaterial, alternativeItemNo, alternativeBeschreibung, alternativartikel, zuschlagPercent, indicrectCostPercent, rundungsmethodencade, suchbaum, sollzeit, neuGebraucht, artikelFuerFremdbetankung, artikelzuschlagsgruppe, geprueft, datensatzGesperrt, sperrhinweis, bemerkungII, gueltigAb, gueltigBis, assemblyBOM, metallzuschlagDEL, lagerfachcode, standardlagerfach, gefahrgut, lagerbestandII, inhaltsstoffe, abfallklasse, salesUnitOfMeasure, verpackungsart, artikelverfolgungscode, chargennummern, ablaufdatumsformel, serialNosII, blocked, geraetebestand, istSchuettgut, zeilenartWerkstattbericht, zuschlagsbetrag, mengeneinheitZuschlag, zugangMengeII, abgangMengeII, kontierungshilfeWartung, kategorie, neuanlagesystem, produktbuchungsgruppeWartung, herkunftscodeAbwertung, neuanlagenbenutzer, datumDerAbwertung, wartungsprozentsatz, neuanlagendatum, neuanlagenzeit, aenderungssystem, aenderungsbenutzer, aenderungsdatum, aenderungszeit, locationFilter, globalDimension1Filter, globalDimension2Filter, dropShipmentFilter, variantFilter, lagerfachfilterII, dateFilter, spediteurfilter, artikelpostennummerfilter, seriennrFilter, chargennrFilter);
    }

    @Override
    public String toString() {
        return "NevarisArtikeluebersicht[" +
                "odataContext=" + odataContext + ", " +
                "odataEtag=" + odataEtag + ", " +
                "no=" + no + ", " +
                "markierung=" + markierung + ", " +
                "description=" + description + ", " +
                "searchDescription=" + searchDescription + ", " +
                "description2=" + description2 + ", " +
                "baseUnitOfMeasure=" + baseUnitOfMeasure + ", " +
                "inventoryPostingGroup=" + inventoryPostingGroup + ", " +
                "verkMengenrabattcodeII=" + verkMengenrabattcodeII + ", " +
                "itemDiscGroup=" + itemDiscGroup + ", " +
                "allowInvoiceDisc=" + allowInvoiceDisc + ", " +
                "unit_price=" + unit_price + ", " +
                "vkPreisDCBerechnungII=" + vkPreisDCBerechnungII + ", " +
                "profitPercent=" + profitPercent + ", " +
                "costingMethod=" + costingMethod + ", " +
                "unitCost=" + unitCost + ", " +
                "standardCost=" + standardCost + ", " +
                "lastDirectCost=" + lastDirectCost + ", " +
                "einstandspreisDurchschnII=" + einstandspreisDurchschnII + ", " +
                "vendorNo=" + vendorNo + ", " +
                "kreditorenname=" + kreditorenname + ", " +
                "vendorItemNo=" + vendorItemNo + ", " +
                "leadTimeCalculation=" + leadTimeCalculation + ", " +
                "reorderPoint=" + reorderPoint + ", " +
                "maximumInventory=" + maximumInventory + ", " +
                "reorderQuantity=" + reorderQuantity + ", " +
                "dispositionsmethodencode=" + dispositionsmethodencode + ", " +
                "lossgroessenrundungsfaktor=" + lossgroessenrundungsfaktor + ", " +
                "grossWeight=" + grossWeight + ", " +
                "netWeight=" + netWeight + ", " +
                "tariffNo=" + tariffNo + ", " +
                "priceIncludesVat=" + priceIncludesVat + ", " +
                "VATBusPostingGrPrice=" + VATBusPostingGrPrice + ", " +
                "GenProdPostingGroup=" + GenProdPostingGroup + ", " +
                "countryRegionOfOriginCode=" + countryRegionOfOriginCode + ", " +
                "automaticExtTexts=" + automaticExtTexts + ", " +
                "VATProdPostingGroup=" + VATProdPostingGroup + ", " +
                "lastUnitCostCalcDate=" + lastUnitCostCalcDate + ", " +
                "inventoryValueZero=" + inventoryValueZero + ", " +
                "artikelTyp=" + artikelTyp + ", " +
                "dummyartikel=" + dummyartikel + ", " +
                "hersteller=" + hersteller + ", " +
                "herstellerNr=" + herstellerNr + ", " +
                "stammlieferant=" + stammlieferant + ", " +
                "farbe=" + farbe + ", " +
                "fabrikatNr=" + fabrikatNr + ", " +
                "eANNr=" + eANNr + ", " +
                "gruppe=" + gruppe + ", " +
                "gruppenstufe=" + gruppenstufe + ", " +
                "gruppenstufe2=" + gruppenstufe2 + ", " +
                "gruppenstufe3=" + gruppenstufe3 + ", " +
                "gruppenstufe4=" + gruppenstufe4 + ", " +
                "gruppenstufe5=" + gruppenstufe5 + ", " +
                "bALArtNeu=" + bALArtNeu + ", " +
                "bALNrNeu=" + bALNrNeu + ", " +
                "bALArtAlt=" + bALArtAlt + ", " +
                "bALNrAlt=" + bALNrAlt + ", " +
                "preisGiltProMenge=" + preisGiltProMenge + ", " +
                "verrechnungspreisIntern=" + verrechnungspreisIntern + ", " +
                "kontierungshilfeREBU=" + kontierungshilfeREBU + ", " +
                "kontierungshilfeIntern=" + kontierungshilfeIntern + ", " +
                "kostentraeger=" + kostentraeger + ", " +
                "textbausteinIntern=" + textbausteinIntern + ", " +
                "arbeitsmittelpreis=" + arbeitsmittelpreis + ", " +
                "arbeitsmittelpreis2=" + arbeitsmittelpreis2 + ", " +
                "arbeitsmittelpreis3=" + arbeitsmittelpreis3 + ", " +
                "textZuPreis1=" + textZuPreis1 + ", " +
                "textZuPreis2=" + textZuPreis2 + ", " +
                "textZuPreis3=" + textZuPreis3 + ", " +
                "kalkArbeitsmittelpreis=" + kalkArbeitsmittelpreis + ", " +
                "kalkArbeitsmittelpreis2=" + kalkArbeitsmittelpreis2 + ", " +
                "kalkArbeitsmittelpreis3=" + kalkArbeitsmittelpreis3 + ", " +
                "kalkMengeneinheit=" + kalkMengeneinheit + ", " +
                "kalkulationsgewicht=" + kalkulationsgewicht + ", " +
                "gewichtseinheit=" + gewichtseinheit + ", " +
                "kostenartBBA=" + kostenartBBA + ", " +
                "hKAKalkulation=" + hKAKalkulation + ", " +
                "kAKalkulation=" + kAKalkulation + ", " +
                "istMaterial=" + istMaterial + ", " +
                "alternativeItemNo=" + alternativeItemNo + ", " +
                "alternativeBeschreibung=" + alternativeBeschreibung + ", " +
                "alternativartikel=" + alternativartikel + ", " +
                "zuschlagPercent=" + zuschlagPercent + ", " +
                "indicrectCostPercent=" + indicrectCostPercent + ", " +
                "rundungsmethodencade=" + rundungsmethodencade + ", " +
                "suchbaum=" + suchbaum + ", " +
                "sollzeit=" + sollzeit + ", " +
                "neuGebraucht=" + neuGebraucht + ", " +
                "artikelFuerFremdbetankung=" + artikelFuerFremdbetankung + ", " +
                "artikelzuschlagsgruppe=" + artikelzuschlagsgruppe + ", " +
                "geprueft=" + geprueft + ", " +
                "datensatzGesperrt=" + datensatzGesperrt + ", " +
                "sperrhinweis=" + sperrhinweis + ", " +
                "bemerkungII=" + bemerkungII + ", " +
                "gueltigAb=" + gueltigAb + ", " +
                "gueltigBis=" + gueltigBis + ", " +
                "assemblyBOM=" + assemblyBOM + ", " +
                "metallzuschlagDEL=" + metallzuschlagDEL + ", " +
                "lagerfachcode=" + lagerfachcode + ", " +
                "standardlagerfach=" + standardlagerfach + ", " +
                "gefahrgut=" + gefahrgut + ", " +
                "lagerbestandII=" + lagerbestandII + ", " +
                "inhaltsstoffe=" + inhaltsstoffe + ", " +
                "abfallklasse=" + abfallklasse + ", " +
                "salesUnitOfMeasure=" + salesUnitOfMeasure + ", " +
                "verpackungsart=" + verpackungsart + ", " +
                "artikelverfolgungscode=" + artikelverfolgungscode + ", " +
                "chargennummern=" + chargennummern + ", " +
                "ablaufdatumsformel=" + ablaufdatumsformel + ", " +
                "serialNosII=" + serialNosII + ", " +
                "blocked=" + blocked + ", " +
                "geraetebestand=" + geraetebestand + ", " +
                "istSchuettgut=" + istSchuettgut + ", " +
                "zeilenartWerkstattbericht=" + zeilenartWerkstattbericht + ", " +
                "zuschlagsbetrag=" + zuschlagsbetrag + ", " +
                "mengeneinheitZuschlag=" + mengeneinheitZuschlag + ", " +
                "zugangMengeII=" + zugangMengeII + ", " +
                "abgangMengeII=" + abgangMengeII + ", " +
                "kontierungshilfeWartung=" + kontierungshilfeWartung + ", " +
                "kategorie=" + kategorie + ", " +
                "neuanlagesystem=" + neuanlagesystem + ", " +
                "produktbuchungsgruppeWartung=" + produktbuchungsgruppeWartung + ", " +
                "herkunftscodeAbwertung=" + herkunftscodeAbwertung + ", " +
                "neuanlagenbenutzer=" + neuanlagenbenutzer + ", " +
                "datumDerAbwertung=" + datumDerAbwertung + ", " +
                "wartungsprozentsatz=" + wartungsprozentsatz + ", " +
                "neuanlagendatum=" + neuanlagendatum + ", " +
                "neuanlagenzeit=" + neuanlagenzeit + ", " +
                "aenderungssystem=" + aenderungssystem + ", " +
                "aenderungsbenutzer=" + aenderungsbenutzer + ", " +
                "aenderungsdatum=" + aenderungsdatum + ", " +
                "aenderungszeit=" + aenderungszeit + ", " +
                "locationFilter=" + locationFilter + ", " +
                "globalDimension1Filter=" + globalDimension1Filter + ", " +
                "globalDimension2Filter=" + globalDimension2Filter + ", " +
                "dropShipmentFilter=" + dropShipmentFilter + ", " +
                "variantFilter=" + variantFilter + ", " +
                "lagerfachfilterII=" + lagerfachfilterII + ", " +
                "dateFilter=" + dateFilter + ", " +
                "spediteurfilter=" + spediteurfilter + ", " +
                "artikelpostennummerfilter=" + artikelpostennummerfilter + ", " +
                "seriennrFilter=" + seriennrFilter + ", " +
                "chargennrFilter=" + chargennrFilter + ", " +
                ']';
    }
}