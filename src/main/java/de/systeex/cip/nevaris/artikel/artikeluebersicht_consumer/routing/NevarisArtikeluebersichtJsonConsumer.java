package de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.domain.NevarisArtikeluebersicht;
import de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.domain.NevarisArtikeluebersichtList;
import de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.infrastructure.NevarisArtikeluebersichtToArtikelErrorMessageTransformer;
import de.systeex.cip.nevaris.artikel.artikeluebersicht_consumer.infrastructure.NevarisArtikeluebersichtToArtikelProcessor;
import de.systeex.cip.nevaris.infrastructure.NevarisConsumerAlreadySeenProcessor;
import de.systeex.cip.nevaris.infrastructure.NevarisDateTimeFormatter;
import de.systeex.cip.nevaris.infrastructure.NevarisPqSQLTimeStampProcessor;
import de.systeex.cip.types.Artikel;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.List;
import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class NevarisArtikeluebersichtJsonConsumer extends ErrorResistantRouteBuilder {

    @Value("${nevaris.host}")
    private String apiUrl;

    @Value("${nevaris.default.basepath}")
    private String basePath;

    @Value("${poll.cron.artikel}")
    private String cronExpression;

    @Value("#{${mandant.mapping.artikel}}")
    private Map<String, String> mandantMapToNevaris;


    private final ObjectMapper mapper = JsonMapper.builder()
            .findAndAddModules()
            .build();
    private final JacksonDataFormat jacksonDataFormat = new JacksonDataFormat(mapper, Artikel.class);

    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final Predicate notAlreadySeen = PredicateBuilder.isEqualTo(header("ALREADY_SEEN"), constant("false"));

    //TODO: CBS must be included now
    private final String importFilter = "and Artikeltyp ne 'DUMMY'";

    @Override
    public void configure() throws Exception {
        super.configure();
        final NevarisDateTimeFormatter nevarisDateTimeFormatter = NevarisDateTimeFormatter.getInstance();
        final NevarisConsumerAlreadySeenProcessor nevarisConsumerAlreadySeenProcessor = new NevarisConsumerAlreadySeenProcessor();

        mandantMapToNevaris.keySet().forEach(mandant -> {
            from("quartz://nevarisArtikeluebersicht" + mandantMapToNevaris.get(mandant) + "?cron=" + cronExpression.replace(' ', '+'))
                    .routeId("nevarisArtikeluebersichtQuartz" + mandantMapToNevaris.get(mandant))
                    .log(LoggingLevel.INFO, "started polling Artikeluebersicht")
                    .setHeader("MANDANT", constant(mandant))
                    .setHeader("NEVARIS_COMPANY_KEY", constant(mandantMapToNevaris.get(mandant)))
                    .setHeader("NEVARIS_MANDANT", constant(mandantMapToNevaris.get(mandant)))
                    .setHeader("NEVARIS_ENTITY", constant("ARTIKEL"))
                    .to("direct:getLastTimestampFromDatabase")
                    .process( new NevarisPqSQLTimeStampProcessor())
                    .setHeader("DATE_PART", constant("1900-01-01") ) // deactivate timed logic, get complete list
                    .toD("http://" + apiUrl + basePath + "/Company('${headers.NEVARIS_COMPANY_KEY}')/Artikelübersicht?$filter=Änderungsdatum ge ${headers.DATE_PART} " + importFilter + "&$orderby=Änderungsdatum asc,Änderungszeit asc,No asc&clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFail`ure=false")
                    .to("direct:processArtikel")
            ;
        });


        from("direct:processArtikel")
                .id("processArtikel")
                .convertBodyTo(String.class, "UTF-8")
                .choice()
                    .when(httpStatusOk)
                        .unmarshal(new JacksonDataFormat(NevarisArtikeluebersichtList.class))
                        .process(exchange -> {
                            NevarisArtikeluebersichtList list = exchange.getIn().getBody(NevarisArtikeluebersichtList.class);
                            exchange.getIn().setBody(list.getNevarisArtikeluebersichtList());
                            exchange.getIn().setHeader("NEXT_LINK", list.getoDataNextLink());
                        })
                        .to("direct:fetchNextPageArtikel")
                        .split(body())
                            .process(nevarisConsumerAlreadySeenProcessor)
                            .choice()
                                .when(notAlreadySeen)
                                    .enrich("direct:artikeluebersichtToBroker", (oldExchange, newExchange) -> oldExchange)
                                .to("direct:saveLastTimestamp")
                            .endChoice()
                    .otherwise()
                        .log(LoggingLevel.INFO,"artikel: skip ${headers}")
                    .endChoice()
                .end()
        ;

        from("direct:fetchNextPageArtikel")
                .id("fetchNextPageArtikel")
                .choice()
                .when(simple("${headers.NEXT_LINK} != null && ${headers.NEXT_LINK} != ''"))
                    .log(LoggingLevel.INFO, "Fetching next page: ${headers.NEXT_LINK}")
                    .toD("${headers.NEXT_LINK}&clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false")
                    .convertBodyTo(String.class, "UTF-8")
                    .choice()
                    .when(httpStatusOk)
                        .unmarshal(new JacksonDataFormat(NevarisArtikeluebersichtList.class))
                        .process(exchange -> {
                            NevarisArtikeluebersichtList next = exchange.getIn().getBody(NevarisArtikeluebersichtList.class);
                            List<NevarisArtikeluebersicht> current = exchange.getIn().getBody(List.class);
                            current.addAll(next.getNevarisArtikeluebersichtList());
                            exchange.getIn().setBody(current);
                            exchange.getIn().setHeader("NEXT_LINK", next.getoDataNextLink());
                        })
                        .to("direct:fetchNextPageArtikel")
                    .otherwise()
                        .log(LoggingLevel.ERROR, "Error while retrieving the next page: ${header.CamelHttpResponseCode}")
                    .endChoice()
                .otherwise()
                    .log(LoggingLevel.INFO, "Paging completed")
                    .log(LoggingLevel.DEBUG, "${body}")
                .end();


        from("direct:artikeluebersichtToBroker")
            .id("artikeluebersichtToBroker")
            .process(new NevarisArtikeluebersichtToArtikelProcessor())
            .marshal(jacksonDataFormat)
            .to("log:import?showHeaders=true")
            .removeHeaders("*", "MANDANT|ODATA_ETAG|MESSAGE_ID|DELIVERY_MODE")
            .setExchangePattern(ExchangePattern.InOnly)
            .setHeader(DELIVERY_MODE, constant(2))
            .toD("rabbitmq:artikel?exchangeType=topic&routingKey=${headers.MANDANT}&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig")
                .log(LoggingLevel.DEBUG, "artikel sent to RabbitMQ: ${headers} ${body}")
        ;

    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisArtikeluebersichtToArtikelErrorMessageTransformer("Artikeluebersicht", "NEVARIS", "nevarisArtikeluebersichtQuartz", mandantMapToNevaris);
    }
}
