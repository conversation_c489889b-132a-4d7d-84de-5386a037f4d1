package de.systeex.cip.nevaris.artikel.artikelsatzmarkierung_consumer.infrastructure;

import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public class NevarisArtikelSatzmarkierungErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisArtikelSatzmarkierungErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.CRON;
    }
}