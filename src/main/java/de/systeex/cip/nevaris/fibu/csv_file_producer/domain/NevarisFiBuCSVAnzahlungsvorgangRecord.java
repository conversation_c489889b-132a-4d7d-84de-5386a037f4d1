package de.systeex.cip.nevaris.fibu.csv_file_producer.domain;

import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

@CsvRecord(separator = ";", generateHeaderColumns = true)
public class NevarisFiBuCSVAnzahlungsvorgangRecord {
    @DataField(pos = 1, columnName = "Anzahlungsvorgang")
    private String anzahlungsvorgang;
    @DataField(pos = 2, columnName = "Konto-Nr. Kreditor")
    private String kontoNrKreditor;
    @DataField(pos = 3, columnName = "Kostenstelle")
    private String kostenstelle;
    @DataField(pos = 4, columnName = "Betragseingabe")
    private String betragseingabe;
    @DataField(pos = 5, columnName = "Buchungsgruppe Ford. Aus ANZ")
    private String buchungsgruppeFordAusANZ;
    @DataField(pos = 6, columnName = "Buchungsgruppe ANZ")
    private String buchungsgruppeANZ;
    @DataField(pos = 7, columnName = "Geschäftsbuchungs-gruppe")
    private String geschaeftsbuchungsGruppe;
    @DataField(pos = 8, columnName = "Produktbuchungs-gruppe")
    private String produktbuchungsGruppe;
    @DataField(pos = 9, columnName = "MWSt_Geschäftsbuchungsgruppe")
    private String mwStGeschaeftsbuchungsgruppe;
    @DataField(pos = 10, columnName = "MWSt_Produktbuchungsgruppe")
    private String mwStProduktbuchungsgruppe;
    @DataField(pos = 11, columnName = "Art")
    private String art;

    public NevarisFiBuCSVAnzahlungsvorgangRecord() {
    }

    public NevarisFiBuCSVAnzahlungsvorgangRecord(String anzahlungsvorgang, String kontoNrKreditor, String kostenstelle, String betragseingabe, String buchungsgruppeFordAusANZ, String buchungsgruppeANZ, String geschaeftsbuchungsGruppe, String produktbuchungsGruppe, String mwStGeschaeftsbuchungsgruppe, String mwStProduktbuchungsgruppe, String art) {
        this.anzahlungsvorgang = anzahlungsvorgang;
        this.kontoNrKreditor = kontoNrKreditor;
        this.kostenstelle = kostenstelle;
        this.betragseingabe = betragseingabe;
        this.buchungsgruppeFordAusANZ = buchungsgruppeFordAusANZ;
        this.buchungsgruppeANZ = buchungsgruppeANZ;
        this.geschaeftsbuchungsGruppe = geschaeftsbuchungsGruppe;
        this.produktbuchungsGruppe = produktbuchungsGruppe;
        this.mwStGeschaeftsbuchungsgruppe = mwStGeschaeftsbuchungsgruppe;
        this.mwStProduktbuchungsgruppe = mwStProduktbuchungsgruppe;
        this.art = art;
    }

    public String getAnzahlungsvorgang() {
        return anzahlungsvorgang;
    }

    public void setAnzahlungsvorgang(String anzahlungsvorgang) {
        this.anzahlungsvorgang = anzahlungsvorgang;
    }

    public String getKontoNrKreditor() {
        return kontoNrKreditor;
    }

    public void setKontoNrKreditor(String kontoNrKreditor) {
        this.kontoNrKreditor = kontoNrKreditor;
    }

    public String getKostenstelle() {
        return kostenstelle;
    }

    public void setKostenstelle(String kostenstelle) {
        this.kostenstelle = kostenstelle;
    }

    public String getBetragseingabe() {
        return betragseingabe;
    }

    public void setBetragseingabe(String betragseingabe) {
        this.betragseingabe = betragseingabe;
    }

    public String getBuchungsgruppeFordAusANZ() {
        return buchungsgruppeFordAusANZ;
    }

    public void setBuchungsgruppeFordAusANZ(String buchungsgruppeFordAusANZ) {
        this.buchungsgruppeFordAusANZ = buchungsgruppeFordAusANZ;
    }

    public String getBuchungsgruppeANZ() {
        return buchungsgruppeANZ;
    }

    public void setBuchungsgruppeANZ(String buchungsgruppeANZ) {
        this.buchungsgruppeANZ = buchungsgruppeANZ;
    }

    public String getGeschaeftsbuchungsGruppe() {
        return geschaeftsbuchungsGruppe;
    }

    public void setGeschaeftsbuchungsGruppe(String geschaeftsbuchungsGruppe) {
        this.geschaeftsbuchungsGruppe = geschaeftsbuchungsGruppe;
    }

    public String getProduktbuchungsGruppe() {
        return produktbuchungsGruppe;
    }

    public void setProduktbuchungsGruppe(String produktbuchungsGruppe) {
        this.produktbuchungsGruppe = produktbuchungsGruppe;
    }

    public String getMwStGeschaeftsbuchungsgruppe() {
        return mwStGeschaeftsbuchungsgruppe;
    }

    public void setMwStGeschaeftsbuchungsgruppe(String mwStGeschaeftsbuchungsgruppe) {
        this.mwStGeschaeftsbuchungsgruppe = mwStGeschaeftsbuchungsgruppe;
    }

    public String getMwStProduktbuchungsgruppe() {
        return mwStProduktbuchungsgruppe;
    }

    public void setMwStProduktbuchungsgruppe(String mwStProduktbuchungsgruppe) {
        this.mwStProduktbuchungsgruppe = mwStProduktbuchungsgruppe;
    }

    public String getArt() {
        return art;
    }

    public void setArt(String art) {
        this.art = art;
    }
}
