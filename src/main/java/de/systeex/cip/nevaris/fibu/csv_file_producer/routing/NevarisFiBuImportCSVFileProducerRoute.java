package de.systeex.cip.nevaris.fibu.csv_file_producer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.fibu.csv_file_producer.domain.NevarisFiBuCSVAnzahlungsvorgangRecord;
import de.systeex.cip.nevaris.fibu.csv_file_producer.domain.NevarisFiBuCSVRecord;
import de.systeex.cip.nevaris.fibu.csv_file_producer.infrastructure.FiBuToNevarisCSVAnzahlungsvorgangProcessor;
import de.systeex.cip.nevaris.fibu.csv_file_producer.infrastructure.FibuToNevarisFibuListProcessor;
import de.systeex.cip.nevaris.fibu.csv_file_producer.infrastructure.NevarisFiBuImportCsvErrorMessageTransformer;
import de.systeex.cip.nevaris.fibu.odata_producer.domain.NevarisAnzahlungsvorgang;
import de.systeex.cip.nevaris.fibu.xlsx_file_producer.infrastructure.FiBuToNevarisXLSXFiBuProcessor;
import de.systeex.cip.nevaris.infrastructure.ArrayListMergeAggregationStrategy;
import de.systeex.cip.types.Anzahlungsvorgang;
import de.systeex.cip.types.FiBuRecord;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.apache.camel.component.jackson.ListJacksonDataFormat;
import org.apache.camel.dataformat.bindy.csv.BindyCsvDataFormat;
import org.apache.camel.spi.DataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class NevarisFiBuImportCSVFileProducerRoute extends ErrorResistantRouteBuilder {
    @Value("${fibu.file.output.filename}")
    private String outputFilename;
    @Value("${fibu.file.output.path}")
    private String outputPath;

    @Value("#{${mandant.mapping}}")
    private Map<String, String> mandantMapping;

    @Value("#{${mandant.mapping.nummer}}")
    private Map<String, String> mandantMapToNevarisNummer;

    private final Predicate hasAnzahlungsvorgang = PredicateBuilder.isNotNull(header("ANZAHLUNGSVORGANG"));
    private final Predicate createNevarisAnzahlungsvorgang = PredicateBuilder.and(hasAnzahlungsvorgang, PredicateBuilder.isNull(header("NEVARIS_ANZAHLUNGSVORGANG")));

    private final String originQueueName = "nevaris-%s.csv.fibu";

    @Override
    public void configure() throws Exception {
        super.configure();
        DataFormat bindyNevarisFiBuRecord = new BindyCsvDataFormat(NevarisFiBuCSVRecord.class);
        DataFormat bindyNevarisFiBuAnzahlungsvorgangRecord = new BindyCsvDataFormat(NevarisFiBuCSVAnzahlungsvorgangRecord.class);
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat jacksonDataFormat = new JacksonDataFormat(mapper, FiBuRecord.class);
        JacksonDataFormat nevarisFibuRecordsList = new ListJacksonDataFormat(mapper, NevarisFiBuCSVRecord.class);

        mandantMapping.keySet().forEach(mandant -> {
            String queueName = String.format(originQueueName, mandantMapping.get(mandant));
            from("rabbitmq:fibu-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=" + queueName + "&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapping.get(mandant) + ".csv.fibu.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".csv.fibu&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchCSVFiBuRecordFromBroker" + mandantMapping.get(mandant))
                    .unmarshal(jacksonDataFormat)
                    .removeHeaders("Camel*", "MESSAGE_ID|DELIVERY_MODE")
                    .setHeader("MANDANT", constant(mandant))
                    .setHeader("NEVARIS_MANDANT", constant(mandantMapping.get(mandant)))
                    .setHeader("MANDANT_NR", constant(mandantMapToNevarisNummer.get(mandant)))
                    .process(exchange -> {
                        FiBuRecord fiBuRecord = exchange.getIn().getBody(FiBuRecord.class);
                        Anzahlungsvorgang anzvorgang = fiBuRecord.anzahlungsvorgang();
                        if (anzvorgang != null) {
                            exchange.getIn().setHeader("ANZAHLUNGSVORGANG", anzvorgang.anzahlungsvorgang().replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)));
                        }
                    })
                    .choice()
                    .when(hasAnzahlungsvorgang)
                        .enrich("direct:getNevarisAnzahlungsvorgang", (oldExchange, newExchange) -> {
                            NevarisAnzahlungsvorgang nevarisAnzahlungsvorgang = newExchange.getIn().getBody(NevarisAnzahlungsvorgang.class);
                            if (nevarisAnzahlungsvorgang != null) {
                                oldExchange.getIn().setHeader("NEVARIS_ANZAHLUNGSVORGANG", nevarisAnzahlungsvorgang.vorgang());
                            }
                            return oldExchange;
                        })
                    .end()
                    .choice()
                    .when(createNevarisAnzahlungsvorgang)
                    .enrich("direct:createNevarisCSVAnzahlungsvorgang", (oldExchange, newExchange) -> {
                        oldExchange.getIn().setHeader("NEVARIS_ANZAHLUNGSVORGANG", newExchange.getIn().getHeader("NEVARIS_ANZAHLUNGSVORGANG"));
                        return oldExchange;
                    })
                    .end()
                    .process(new FibuToNevarisFibuListProcessor())
                    .aggregate(new ArrayListMergeAggregationStrategy()).constant(true)
                    .completionTimeout(5000L)
                    .marshal(nevarisFibuRecordsList)
                    .setHeader(DELIVERY_MODE, constant(2))
                    .to("rabbitmq:fibu-anfragen-aggregated?exchangeType=topic&routingKey=" + mandant + "-xlsx&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");

            from("rabbitmq:fibu-anfragen-aggregated?exchangeType=topic&routingKey=" + mandant + "-xlsx&queue=nevaris-" + mandantMapping.get(mandant) + ".xlsx.fibu-aggregated&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapping.get(mandant) + ".xlsx.fibu-aggregated.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".xlsx.fibu-aggregated&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchNevarisFibuCSVListFromBroker" + mandantMapping.get(mandant))
                    .removeHeaders("Camel*", "MESSAGE_ID|DELIVERY_MODE")
                    .unmarshal(nevarisFibuRecordsList)
                    .process(new FiBuToNevarisXLSXFiBuProcessor())
                    .toD("file:" + outputPath + "/${headers.NEVARIS_MANDANT}?filename=${headers.NEVARIS_MANDANT}_${date:now:yyyyMMdd_HHmmss}_${id}_FiBu.xlsx");
        });

        from("direct:createNevarisCSVAnzahlungsvorgang")
                .routeId("createNevarisCSVAnzahlungsvorgang")
                .process(new FiBuToNevarisCSVAnzahlungsvorgangProcessor())
                .marshal(bindyNevarisFiBuAnzahlungsvorgangRecord)
                .toD("file:" + outputPath + "/${headers.NEVARIS_MANDANT}?filename=${headers.NEVARIS_MANDANT}_${date:now:yyyyMMdd_HHmmss}_${id}_Anzahlungsvorgang.csv");
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisFiBuImportCsvErrorMessageTransformer("fibu","NEVARIS", originQueueName, mandantMapping);
    }
}
