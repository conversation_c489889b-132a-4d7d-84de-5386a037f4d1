package de.systeex.cip.nevaris.fibu.xlsx_file_producer.infrastructure;

import de.systeex.cip.nevaris.fibu.csv_file_producer.domain.NevarisFiBuCSVRecord;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.apache.camel.builder.Builder.simple;
import static org.apache.camel.component.file.FileConstants.FILE_NAME_ONLY;

public class FiBuToNevarisXLSXFiBuProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        List<NevarisFiBuCSVRecord> nevarisFiBuCSVRecords = exchange.getIn().getBody(List.class);

        try (Workbook workbook = new XSSFWorkbook()) {

            // Create a blank Sheet
            Sheet sheet = workbook.createSheet("FiBu");

            // column names
            List<String> columns = new ArrayList<>();
            columns.add("Kontoart");
            columns.add("Kontonr.");
            columns.add("Buchungsdatum");
            columns.add("Belegart");
            columns.add("Belegnr.");
            columns.add("Externe Belegnr.");
            columns.add("Belegdatum");
            columns.add("Beschreibung");
            columns.add("Zlg.-Bedingungscode");
            columns.add("Fälligkeitsdatum");
            columns.add("Skontodatum");
            columns.add("Skonto %");
            columns.add("Skontierfähiger Betrag");
            columns.add("Währungscode");
            columns.add("Sollbetrag");
            columns.add("Habenbetrag");
            columns.add("MWSt Betrag");
            columns.add("MWSt-Satz");
            columns.add("Buchungsgruppe");
            columns.add("Geschäftsbuchungsgruppe");
            columns.add("Produktbuchungsgruppe");
            columns.add("Menge");
            columns.add("Mengeneinheit");
            columns.add("Kostenstelle");
            columns.add("Kostenträger");
            columns.add("Kostenart");
            columns.add("Gerät");
            columns.add("Artikel");
            columns.add("BAS");
            columns.add("LV-Position");
            columns.add("Positionsnr.");
            columns.add("Dokument-ID");
            columns.add("Versicherungsgesellschaft");
            columns.add("Risikonr.");
            columns.add("Verk.-/Einkäufercode");
            columns.add("Niederlassung");
            columns.add("Projektnr.");
            columns.add("Abwarten");
            columns.add("Abweichender Mandant Kurzbez.");
            columns.add("Belegnr2");
            columns.add("Zusatzkennung");
            columns.add("Abgrenzung");
            columns.add("Mahnstufe");
            columns.add("Mahnstopp");
            columns.add("Ausgleich mit Belegart");
            columns.add("Ausgleich mit Belegnr.");
            columns.add("Zahlungsform");
            columns.add("Bankkonto für Zahlungen");
            columns.add("Herkunftscode");
            columns.add("MWSt-Gruppe");
            columns.add("Zielmandant Belegübertr.");
            columns.add("Zielkostenstelle Belegübertr.");
            columns.add("Adressnr.");
            columns.add("Debitor/Kreditor Anz. Vorgang");
            columns.add("Anz. Vorgang");
            columns.add("Belegart Anz. Vorgang");
            columns.add("Mandant");
            columns.add("abw. Adressnr.");
            columns.add("Segment");
            columns.add("Skontodatum II");
            columns.add("Skonto % II");
            columns.add("Infofeld");
            columns.add("Leistungsdatum");


            Row headerRow = sheet.createRow(0);

            // Create columns/first row in a file
            for (int i = 0; i < columns.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columns.get(i));
            }

            int rowNum = 1;

            // iterate over the list of nevarisFiBuCSVRecord and for each nevarisFiBuCSVRecord write its values to the excel row
            for (NevarisFiBuCSVRecord nevarisFiBuCSVRecord: nevarisFiBuCSVRecords) {
                Row row = sheet.createRow(rowNum++);

                // populate file with the values for each column
                row.createCell(0).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getKontoart(), ""));
                row.createCell(1).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getKontonr(), ""));
                row.createCell(2).setCellValue(this.formatDate(nevarisFiBuCSVRecord.getBuchungsdatum()));
                row.createCell(3).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBelegart(), ""));
                row.createCell(4).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBelegnr(), ""));
                row.createCell(5).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getExterneBelegnr(), ""));
                row.createCell(6).setCellValue(this.formatDate(nevarisFiBuCSVRecord.getBelegdatum()));
                row.createCell(7).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBeschreibung(), ""));
                row.createCell(8).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getZlgBedingungscode(), ""));
                row.createCell(9).setCellValue(this.formatDate(nevarisFiBuCSVRecord.getFaelligkeitsdatum()));
                row.createCell(10).setCellValue(this.formatDate(nevarisFiBuCSVRecord.getSkontodatum()));
                row.createCell(11).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getSkontoProzent(), ""));
                row.createCell(12).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getSkontierfaehigerBetrag(), ""));
                row.createCell(13).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getWaehrungscode(), ""));
                row.createCell(14).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getSollbetrag(), ""));
                row.createCell(15).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getHabenbetrag(), ""));
                row.createCell(16).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMwstBetrag(), ""));
                row.createCell(17).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMwstSatz(), ""));
                row.createCell(18).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBuchungsgruppe(), ""));
                row.createCell(19).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getGeschaeftsbuchungsgruppe(), ""));
                row.createCell(20).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getProduktbuchungsgruppe(), ""));
                row.createCell(21).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMenge(), ""));
                row.createCell(22).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMengeneinheit(), ""));
                row.createCell(23).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getKostenstelle(), ""));
                row.createCell(24).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getKostentraeger(), ""));
                row.createCell(25).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getKostenart(), ""));
                row.createCell(26).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getGeraet(), ""));
                row.createCell(27).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getArtikel(), ""));
                row.createCell(28).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBas(), ""));
                row.createCell(29).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getLvPosition(), ""));
                row.createCell(30).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getPositionsnr(), ""));
                row.createCell(31).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getDokumentId(), ""));
                row.createCell(32).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getVersicherungsgesellschaft(), ""));
                row.createCell(33).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getRisikonr(), ""));
                row.createCell(34).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getVerkEinkaeufercode(), ""));
                row.createCell(35).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getNiederlassung(), ""));
                row.createCell(36).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getProjektnr(), ""));
                row.createCell(37).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAbwarten(), ""));
                row.createCell(38).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAbweichenderMandantKurzbez(), ""));
                row.createCell(39).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBelegnr2(), ""));
                row.createCell(40).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getZusatzkennung(), ""));
                row.createCell(41).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAbgrenzung(), ""));
                row.createCell(42).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMahnstufe(), ""));
                row.createCell(43).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMahnstopp(), ""));
                row.createCell(44).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAusgleichMitBelegart(), ""));
                row.createCell(45).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAusgleichMitBelegnr(), ""));
                row.createCell(46).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getZahlungsform(), ""));
                row.createCell(47).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBankkontoFuerZahlungen(), ""));
                row.createCell(48).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getHerkunftscode(), ""));
                row.createCell(49).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMwstGruppe(), ""));
                row.createCell(50).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getZielmandantBeleguebertr(), ""));
                row.createCell(51).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getZielkostenstelleBeleguebertr(), ""));
                row.createCell(52).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAdressnr(), ""));
                row.createCell(53).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getDebitorKreditorAnzVorgang(), ""));
                row.createCell(54).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAnzVorgang(), ""));
                row.createCell(55).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getBelegartAnzVorgang(), ""));
                row.createCell(56).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getMandant(), ""));
                row.createCell(57).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getAbwAdressnr(), ""));
                row.createCell(58).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getSegment(), ""));
                row.createCell(59).setCellValue(this.formatDate(nevarisFiBuCSVRecord.getSkontoDatumII()));
                row.createCell(60).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getSkontoProzentII(), ""));
                row.createCell(61).setCellValue(Objects.toString(nevarisFiBuCSVRecord.getInfofeld(), ""));
                row.createCell(62).setCellValue(this.formatDate(nevarisFiBuCSVRecord.getLeistungsdatum()));
            }

            // create file
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try (bos) {
                workbook.write(bos);
            } catch (IOException ignored) {
            }
            byte[] xlsx = bos.toByteArray();

            exchange.getIn().setBody(xlsx);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String formatDate(LocalDate date) {
        DateTimeFormatter nevarisDateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        if (date == null) {
            return "";
        } else {
            return date.format(nevarisDateTimeFormatter);
        }
    }
}
