package de.systeex.cip.nevaris.fibu.odata_producer.infrastructure;


import de.systeex.cip.nevaris.fibu.odata_producer.domain.NevarisAnzahlungsvorgang;
import de.systeex.cip.types.Anzahlungsvorgang;
import de.systeex.cip.types.FiBuRecord;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.util.Objects;

public class FibuToNevarisODataAnzahlungProcessor implements Processor {

    @Override
    public void process(Exchange exchange) throws Exception {
        FiBuRecord fibu = exchange.getIn().getBody(FiBuRecord.class);

        Anzahlungsvorgang anzahlungsVorgang = fibu.anzahlungsvorgang();


        if (anzahlungsVorgang != null) {
            NevarisAnzahlungsvorgang nevarisAnzahlungsVorgang = new NevarisAnzahlungsvorgang(
                    null,
                    null,
                    anzahlungsVorgang.anzahlungsvorgang().replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)),
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    NevarisAnzahlungsvorgang.DEBITOR,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    anzahlungsVorgang.kontonummer(),
                    null,
                    null,
                    Objects.toString(anzahlungsVorgang.kostenstelle(), "").replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)),
                    null,
                    null,
                    anzahlungsVorgang.buchungsgruppeFordAusAnz(),
                    anzahlungsVorgang.buchungsgruppeAnz(),
                    anzahlungsVorgang.geschaeftsbuchungsgruppe(),
                    anzahlungsVorgang.produktbuchungsgruppe(),
                    anzahlungsVorgang.mwstGeschaeftsbuchungsgruppe(),
                    anzahlungsVorgang.mwstProduktbuchungsgruppe(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
            );

            exchange.getIn().setBody(nevarisAnzahlungsVorgang);
        }
    }
}
