package de.systeex.cip.nevaris.fibu.odata_producer.domain;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;

import java.math.BigDecimal;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NevarisDebAnzVorgKarte {
    @JsonProperty("@odata.context")
    @JsonIgnore
    String odataContext;
    @JsonProperty("@odata.etag")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    String odataEtag;

    @JsonProperty("Vorgang")
    @Nullable
    private String vorgang;

    @JsonProperty("Debitor_Kreditor")
    @Nullable
    private String debitorKreditor;

    @JsonProperty("Bezeichnung")
    @Nullable
    private String bezeichnung;

    @JsonProperty("Bezeichnung2")
    @Nullable
    private String bezeichnung2;

    @JsonProperty("Suchbegriff")
    @Nullable
    private String suchbegriff;

    @JsonProperty("Saldo_Debitor")
    @Nullable
    private BigDecimal saldoDebitor;

    @JsonProperty("Betragseingabe")
    @Nullable
    private String betragseingabe;

    @JsonProperty("Kum_Erfassung")
    @Nullable
    private Boolean kumErfassung;

    @JsonProperty("Vorgabewerte")
    @Nullable
    private String vorgabewerte;

    @JsonProperty("UID_der_Betriebsstätte_drucken")
    @Nullable
    private Boolean uidDerBetriebsstaetteDrucken;

    @JsonProperty("Erledigt")
    @Nullable
    private Boolean erledigt;

    @JsonProperty("TmpTextErledigtID")
    @Nullable
    private String tmpTextErledigtID;

    @JsonProperty("Debitornr_Kreditornr")
    @Nullable
    private String debitornrKreditornr;

    @JsonProperty("abw_Adressnr")
    @Nullable
    private String abwAdressnr;

    @JsonProperty("TmpName")
    @Nullable
    private String tmpName;

    @JsonProperty("TmpName2")
    @Nullable
    private String tmpName2;

    @JsonProperty("TmpAdresseTextID")
    @Nullable
    private String tmpAdresseTextID;

    @JsonProperty("TmpAdresse")
    @Nullable
    private String tmpAdresse;

    @JsonProperty("TmpPLZCode")
    @Nullable
    private String tmpPLZCode;

    @JsonProperty("TmpOrt")
    @Nullable
    private String tmpOrt;

    @JsonProperty("Bürgschaften")
    @Nullable
    private BigDecimal buergschaften;

    @JsonProperty("Kostenstelle")
    @Nullable
    private String kostenstelle;

    @JsonProperty("IstBauträger")
    @Nullable
    private Boolean istBautraeger;

    @JsonProperty("Niederlassung")
    @Nullable
    private String niederlassung;

    @JsonProperty("Buchgrp_Forderung_aus_Anz")
    @Nullable
    private String buchgrpForderungAusAnz;

    @JsonProperty("Buchgrp_Anzahlung")
    @Nullable
    private String buchgrpAnzahlung;

    @JsonProperty("Geschäftsbuchungsgruppe")
    @Nullable
    private String geschaeftsbuchungsgruppe;

    @JsonProperty("Produktbuchungsgruppe")
    @Nullable
    private String produktbuchungsgruppe;

    @JsonProperty("MWSt_Geschäftsbuchungsgruppe")
    @Nullable
    private String mwstGeschaeftsbuchungsgruppe;

    @JsonProperty("MWSt_Produktbuchungsgruppe")
    @Nullable
    private String mwstProduktbuchungsgruppe;

    @JsonProperty("Neue_Verarb_f_deb_Anf_II")
    @Nullable
    private Boolean neueVerarbFDebAnfII;

    @JsonProperty("Bauleiter")
    @Nullable
    private String bauleiter;

    @JsonProperty("Kostenträger")
    @Nullable
    private String kostentraeger;

    @JsonProperty("Auftragssumme")
    @Nullable
    private BigDecimal auftragssumme;

    @JsonProperty("BAS")
    @Nullable
    private String bas;

    @JsonProperty("Projekt")
    @Nullable
    private String projekt;

    @JsonProperty("ARGE_Auftrag")
    @Nullable
    private String argeAuftrag;

    @JsonProperty("Versicherungsgesellschaft")
    @Nullable
    private String versicherungsgesellschaft;

    @JsonProperty("Risikonr")
    @Nullable
    private String risikonr;

    @JsonProperty("Währungscode")
    @Nullable
    private String waehrungscode;

    @JsonProperty("Zlg_Bedingungscode")
    @Nullable
    private String zlgBedingungscode;

    @JsonProperty("DR_Prozent")
    @Nullable
    private BigDecimal drProzent;

    @JsonProperty("DR_Fälligkeitsformel")
    @Nullable
    private String drFaelligkeitsformel;

    @JsonProperty("Verk_Einkäufercode")
    @Nullable
    private String verkEinkaeufercode;

    @JsonProperty("Segment")
    @Nullable
    private String segment;

    @JsonProperty("Zahlunsbedinung_SR")
    @Nullable
    private String zahlunsbedinungSR;

    @JsonProperty("Rechnungskopie")
    @Nullable
    private Boolean rechnungskopie;

    @JsonProperty("Textbaustein_Kopf_1")
    @Nullable
    private Integer textbausteinKopf1;

    @JsonProperty("Textbaustein_Kopf_2")
    @Nullable
    private Integer textbausteinKopf2;

    @JsonProperty("Textbaustein_Kopf_3")
    @Nullable
    private Integer textbausteinKopf3;

    @JsonProperty("Textbaustein_Kopf_4")
    @Nullable
    private Integer textbausteinKopf4;

    @JsonProperty("Textbaustein_Kopf_5")
    @Nullable
    private Integer textbausteinKopf5;

    @JsonProperty("Fußtext_1")
    @Nullable
    private Integer fusstext1;

    @JsonProperty("Fußtext_2")
    @Nullable
    private Integer fusstext2;

    @JsonProperty("Fußtext_3")
    @Nullable
    private Integer fusstext3;

    @JsonProperty("Sachkonto_Anforderung")
    @Nullable
    private String sachkontoAnforderung;

    @JsonProperty("Kostenart_Anforderung")
    @Nullable
    private String kostenartAnforderung;

    @JsonProperty("Sachkonto_Schlussrechnung")
    @Nullable
    private String sachkontoSchlussrechnung;

    @JsonProperty("Kostenart_Schlussrechnung")
    @Nullable
    private String kostenartSchlussrechnung;

    @JsonProperty("Reportfilter_AR")
    @Nullable
    private String reportfilterAR;

    @JsonProperty("Reportfilter_SR")
    @Nullable
    private String reportfilterSR;

    @JsonProperty("Geschäftsbuchungsgruppe_SR")
    @Nullable
    private String geschaeftsbuchungsgruppeSR;

    @JsonProperty("Produktbuchungsgruppe_SR")
    @Nullable
    private String produktbuchungsgruppeSR;

    @JsonProperty("MWSt_Geschäftsbuchungsgr_SR")
    @Nullable
    private String mwStGeschaeftsbuchungsgrSR;

    @JsonProperty("MWSt_Produktbuchungsgr_SR")
    @Nullable
    private String mwStProduktbuchungsgrSR;

    @JsonProperty("Adressnr_externer_Prüfer")
    @Nullable
    private String adressnrExternerPruefer;

    public NevarisDebAnzVorgKarte() {

    }
}