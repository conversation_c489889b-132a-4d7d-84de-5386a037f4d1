package de.systeex.cip.nevaris.fibu.odata_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;



@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisAnzahlungsvorgang (
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String odataEtag,
        @JsonProperty("Vorgang")
        @Nonnull
        String vorgang,
        @JsonProperty("Änderungsdatum")
        @JsonIgnore
        @Nullable
        String aenderungsdatum,
        @JsonProperty("Änderungszeit")
        @JsonIgnore
        @Nullable
        String aenderungszeit,
        @JsonProperty("Änderungssystem")
        @JsonIgnore
        @Nullable
        String aenderungssystem,
        @JsonProperty("Änderungsbenutzer")
        @JsonIgnore
        @Nullable
        String aenderungsbenutzer,
        @JsonProperty("Neuanlagedatum")
        @JsonIgnore
        @Nullable
        String neuanlagedatum,
        @JsonProperty("Neuanlagezeit")
        @JsonIgnore
        @Nullable
        String neuanlagezeit,
        @JsonProperty("Neuanlagebenutzer")
        @JsonIgnore
        @Nullable
        String neuanlagebenutzer,
        @JsonProperty("Neuanlagesystem")
        @JsonIgnore
        @Nullable
        String neuanlagesystem,
        @JsonProperty("Schlußrechnung_gebucht")
        @JsonIgnore
        @Nullable
        String schlussrechnungGebucht,
        @JsonProperty("Erledigt_um")
        @JsonIgnore
        @Nullable
        String erledigtUm,
        @JsonProperty("Erledigt_am")
        @JsonIgnore
        @Nullable
        String erledigtAm,
        @JsonProperty("Erledigt_durch")
        @JsonIgnore
        @Nullable
        String erledigtDurch,
        @JsonProperty("Bemerkung")
        @JsonIgnore
        @Nullable
        String bemerkung,
        @JsonProperty("Bürgschaften_MW")
        @JsonIgnore
        @Nullable
        String buergschaftenMw,
        @JsonProperty("TmpBürgschaftID")
        @JsonIgnore
        @Nullable
        String tmpBuergschaftId,
        @JsonProperty("Name")
        @JsonIgnore
        @Nullable
        String name,
        @JsonProperty("Debitor_Kreditor")
        @Nonnull
        String debitorKreditor,
        @JsonProperty("Bezeichnung")
        @JsonIgnore
        @Nullable
        String bezeichnung,
        @JsonProperty("Bezeichnung2")
        @JsonIgnore
        @Nullable
        String bezeichnung2,
        @JsonProperty("Suchbegriff")
        @JsonIgnore
        @Nullable
        String suchbegriff,
        @JsonProperty("Saldo_Debitor")
        @JsonIgnore
        BigDecimal saldoDebitor,
        @JsonProperty("Betragseingabe")
        @JsonIgnore
        String betragseingabe,
        @JsonProperty("Erledigt")
        @JsonIgnore
        Boolean erledigt,
        @JsonProperty("Debitornr_Kreditornr")
        String debitornrKreditornr,
        @JsonProperty("abw_Adressnr")
        @JsonIgnore
        String abwAdressnr,
        @JsonProperty("Bürgschaften")
        @JsonIgnore
        BigDecimal buergschaften,
        @JsonProperty("Kostenstelle")
        String kostenstelle,
        @JsonProperty("IstBauträger")
        @JsonIgnore
        String istBautraeger,
        @JsonProperty("Niederlassung")
        @JsonIgnore
        String niederlassung,
        @JsonProperty("Buchgrp_Forderung_aus_Anz")
        String buchgrpForderungAusAnz,
        @JsonProperty("Buchgrp_Anzahlung")
        String buchgrpAnzahlung,
        @JsonProperty("Geschäftsbuchungsgruppe")
        String geschaeftsbuchungsgruppe,
        @JsonProperty("Produktbuchungsgruppe")
        String produktbuchungsgruppe,
        @JsonProperty("MWSt_Geschäftsbuchungsgruppe")
        String mwstGeschaeftsbuchungsgruppe,
        @JsonProperty("MWSt_Produktbuchungsgruppe")
        String mwstProduktbuchungsgruppe,
        @JsonProperty("Bauleiter")
        @JsonIgnore
        String bauleiter,
        @JsonProperty("Kostenträger")
        @JsonIgnore
        String kostentraeger,
        @JsonProperty("BAS")
        @JsonIgnore
        String bas,
        @JsonProperty("Projekt")
        @JsonIgnore
        String projekt,
        @JsonProperty("Versicherungsgesellschaft")
        @JsonIgnore
        String versicherungsgesellschaft,
        @JsonProperty("Risikonr")
        @JsonIgnore
        String risikonr,
        @JsonProperty("Währungscode")
        @JsonIgnore
        String waehrungscode,
        @JsonProperty("Zlg_Bedingungscode")
        @JsonIgnore
        String zlgBedingungscode,
        @JsonProperty("Verk_Einkäufercode")
        @JsonIgnore
        String verkEinkaeufercode,
        @JsonProperty("Segment")
        @JsonIgnore
        String segment
) {
    public static final String DEBITOR = "Debitor";
}