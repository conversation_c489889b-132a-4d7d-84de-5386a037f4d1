package de.systeex.cip.nevaris.fibu.csv_file_producer.infrastructure;
import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public class NevarisFiBuImportCsvErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisFiBuImportCsvErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.QUEUE;
    }
}
