package de.systeex.cip.nevaris.fibu.odata_producer.infrastructure;


import de.systeex.cip.nevaris.fibu.odata_producer.domain.NevarisAnzahlungsvorgang;
import de.systeex.cip.nevaris.fibu.odata_producer.domain.NevarisDebAnzVorgKarte;
import de.systeex.cip.types.Anzahlungsvorgang;
import de.systeex.cip.types.FiBuRecord;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.util.Objects;

public class FibuToNevarisODataDebAnzVorgKarteProcessor implements Processor {

    @Override
    public void process(Exchange exchange) throws Exception {
        FiBuRecord fibu = exchange.getIn().getBody(FiBuRecord.class);

        Anzahlungsvorgang anzahlungsVorgang = fibu.anzahlungsvorgang();

        if (anzahlungsVorgang != null) {
            NevarisDebAnzVorgKarte nevarisAnzahlungsVorgang = new NevarisDebAnzVorgKarte();
            nevarisAnzahlungsVorgang.setVorgang(anzahlungsVorgang.anzahlungsvorgang().replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)));
            nevarisAnzahlungsVorgang.setVorgabewerte(anzahlungsVorgang.vorgabewerte());
            nevarisAnzahlungsVorgang.setBetragseingabe(anzahlungsVorgang.anzahlungsvorgang().replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)));
            nevarisAnzahlungsVorgang.setDebitorKreditor(NevarisAnzahlungsvorgang.DEBITOR);
            nevarisAnzahlungsVorgang.setDebitornrKreditornr(anzahlungsVorgang.kontonummer());
            nevarisAnzahlungsVorgang.setKostenstelle(Objects.toString(anzahlungsVorgang.kostenstelle(), "").replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)));
            nevarisAnzahlungsVorgang.setBuchgrpForderungAusAnz(anzahlungsVorgang.buchungsgruppeFordAusAnz());
            nevarisAnzahlungsVorgang.setBuchgrpAnzahlung(anzahlungsVorgang.buchungsgruppeAnz());
            nevarisAnzahlungsVorgang.setGeschaeftsbuchungsgruppe(anzahlungsVorgang.geschaeftsbuchungsgruppe());
            nevarisAnzahlungsVorgang.setProduktbuchungsgruppe(anzahlungsVorgang.produktbuchungsgruppe());
            nevarisAnzahlungsVorgang.setMwStGeschaeftsbuchungsgrSR(anzahlungsVorgang.mwstGeschaeftsbuchungsgruppe());
            nevarisAnzahlungsVorgang.setMwStProduktbuchungsgrSR(anzahlungsVorgang.mwstProduktbuchungsgruppe());


            exchange.getIn().setBody(nevarisAnzahlungsVorgang);
        }
    }
}
