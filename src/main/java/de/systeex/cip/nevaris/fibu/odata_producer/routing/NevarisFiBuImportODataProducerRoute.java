package de.systeex.cip.nevaris.fibu.odata_producer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.fibu.odata_producer.domain.*;
import de.systeex.cip.nevaris.fibu.odata_producer.infrastructure.FiBuToNevarisFiBuODataListProcessor;
import de.systeex.cip.nevaris.fibu.odata_producer.infrastructure.FibuToNevarisODataAnzahlungProcessor;
import de.systeex.cip.nevaris.fibu.odata_producer.infrastructure.FibuToNevarisODataDebAnzVorgKarteProcessor;
import de.systeex.cip.nevaris.fibu.odata_producer.infrastructure.NevarisFiBuImportODataErrorMessageTransformer;
import de.systeex.cip.nevaris.infrastructure.ArrayListMergeAggregationStrategy;
import de.systeex.cip.types.Anzahlungsvorgang;
import de.systeex.cip.types.FiBuRecord;
import org.apache.camel.Exchange;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.http.HttpConstants;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.apache.camel.component.jackson.ListJacksonDataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.List;
import java.util.Map;

import static org.apache.camel.component.rabbitmq.RabbitMQConstants.DELIVERY_MODE;

@Component
public class NevarisFiBuImportODataProducerRoute extends ErrorResistantRouteBuilder {

    @Value("${nevaris.host}")
    private String nevarisHost;

    @Value("${nevaris.default.basepath}")
    private String basePath;

    @Value("${nevaris.custom.basepath.v2}")
    private String basePathV2;

    @Value("#{${mandant.mapping}}")
    private Map<String, String> mandantMapping;

    @Value("#{${mandant.mapping.nummer}}")
    private Map<String, String> mandantMapToNevarisNummer;

    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final Predicate hasAnzahlungsvorgang = PredicateBuilder.isNotNull(header("ANZAHLUNGSVORGANG"));
    private final Predicate createNevarisAnzahlungsvorgang = PredicateBuilder.and(hasAnzahlungsvorgang, PredicateBuilder.isNull(header("NEVARIS_ANZAHLUNGSVORGANG")));

    private final String originQueueName = "nevaris-%s.odata.fibu";

    @Override
    public void configure() throws Exception {
        super.configure();
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat inputJacksonDataFormat = new JacksonDataFormat(mapper, FiBuRecord.class);
        JacksonDataFormat outputJacksonDataFormat = new JacksonDataFormat(mapper, NevarisFiBuOData.class);
        JacksonDataFormat nevarisFibuRecordsList = new ListJacksonDataFormat(mapper, NevarisFiBuODataRecord.class);
        JacksonDataFormat nevarisAnzahlungsvorgangListFormat = new JacksonDataFormat(mapper, NevarisAnzahlungsvorgangList.class);
        JacksonDataFormat nevarisAnzahlungsvorgangFormat = new JacksonDataFormat(mapper, NevarisAnzahlungsvorgang.class);
        JacksonDataFormat nevarisDebAnzVorgKarteFormat = new JacksonDataFormat(mapper, NevarisDebAnzVorgKarte.class);

        mandantMapping.keySet().forEach(mandant -> {
            String queueName = String.format(originQueueName, mandantMapping.get(mandant));
            from("rabbitmq:fibu-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=" + queueName + "&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapping.get(mandant) + ".odata.fibu.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".odata.fibu&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchODataFiBuRecordFromBroker" + mandantMapping.get(mandant))
                    .removeHeaders("Camel*", DELIVERY_MODE+"|MESSAGE_ID|DELIVERY_MODE")
                    .setHeader("NEVARIS_MANDANT", constant(mandantMapping.get(mandant)))
                    .setHeader("MANDANT", constant(mandant))
                    .setHeader("MANDANT_NR", constant(mandantMapToNevarisNummer.get(mandant)))
                    .unmarshal(inputJacksonDataFormat)
                    .process(exchange -> {
                        FiBuRecord fiBuRecord = exchange.getIn().getBody(FiBuRecord.class);
                        Anzahlungsvorgang anzvorgang = fiBuRecord.anzahlungsvorgang();
                        if (anzvorgang != null) {
                            exchange.getIn().setHeader("ANZAHLUNGSVORGANG", anzvorgang.anzahlungsvorgang().replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)));
                        }
                    })
                    .choice()
                        .when(hasAnzahlungsvorgang)
                            .enrich("direct:getNevarisAnzahlungsvorgang", (oldExchange, newExchange) -> {
                                NevarisAnzahlungsvorgang nevarisAnzahlungsvorgang = newExchange.getIn().getBody(NevarisAnzahlungsvorgang.class);
                                if (nevarisAnzahlungsvorgang != null) {
                                    oldExchange.getIn().setHeader("NEVARIS_ANZAHLUNGSVORGANG", nevarisAnzahlungsvorgang.vorgang());
                                }
                                return oldExchange;
                            })
                    .end()
                    .choice()
                        .when(createNevarisAnzahlungsvorgang)
                            .log(LoggingLevel.DEBUG,"create anzahlungsvorgang: ${headers.ANZAHLUNGSVORGANG}")
                            .enrich("direct:createNevarisDebAnzVorgKarte", (oldExchange, newExchange) -> {
                                NevarisDebAnzVorgKarte nevarisAnzahlungsvorgang = newExchange.getIn().getBody(NevarisDebAnzVorgKarte.class);
                                oldExchange.getIn().setHeader("NEVARIS_ANZAHLUNGSVORGANG", nevarisAnzahlungsvorgang.getVorgang());
                                return oldExchange;
                            })
                    //TODO remove split anzahlungsvorgangstransmission, header NEVARIS_ANZAHLUNGSVORGANG still needed?
//                            .enrich("direct:createNevarisDebAnzVorgKarte", (oldExchange, newExchange) -> oldExchange)NevarisAnzahlungsvorgang
                    .end()
                    .process(new FiBuToNevarisFiBuODataListProcessor())
                    .aggregate(new ArrayListMergeAggregationStrategy()).constant(true)
                    .completionTimeout(1500L)
                    .marshal(nevarisFibuRecordsList)
                    .log(LoggingLevel.DEBUG,"nevarisFibu?showHeaders=true")
                    .setHeader(DELIVERY_MODE, constant(2))
                    .to("rabbitmq:fibu-anfragen-aggregated?exchangeType=topic&routingKey=" + mandant + "-odata&declare=true&skipQueueDeclare=true&skipQueueBind=true&autoDelete=false&connectionFactory=#rabbitConnectionFactoryConfig");

            from("rabbitmq:fibu-anfragen-aggregated?exchangeType=topic&routingKey=" + mandant + "-odata&queue=nevaris-" + mandantMapping.get(mandant) + ".odata.fibu-aggregated&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapping.get(mandant) + ".odata.fibu-aggregated.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".odata.fibu-aggregated&connectionFactory=#rabbitConnectionFactoryConfig")
                    .unmarshal(nevarisFibuRecordsList)
                    .process(exchange -> {
                        List<NevarisFiBuODataRecord> nevarisFiBuODataList = exchange.getIn().getBody(List.class);
                        NevarisFiBuOData nevarisFiBuOData = new NevarisFiBuOData("STANDARD", nevarisFiBuODataList);

                        exchange.getIn().setBody(nevarisFiBuOData);
                    })
                    .marshal(outputJacksonDataFormat)
                    .convertBodyTo(String.class, "UTF-8")
                    .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                    .toD("sql:SELECT nevaris_company_key FROM mapping.companies WHERE mandant = '${headers.MANDANT}'?outputType=SelectOne&OutputHeader=NEVARIS_COMPANY_ID")
                    .toD("http://" + nevarisHost + basePathV2 + "/fibuImportBuffers?$expand=fibuImportBufferLines&company=${headers.NEVARIS_COMPANY_ID}&clientBuilder=#nevarisODataHttpClientBuilder&httpMethod=POST&throwExceptionOnFailure=true")
                    .log(LoggingLevel.INFO,"ImportBuffer transmitted: ${headers.MESSAGE_ID}")
                    .log(LoggingLevel.DEBUG,"${body}");
        });

        from("direct:getNevarisAnzahlungsvorgang")
                .routeId("getNevarisAnzahlungsvorgang")
                .removeHeaders("*", "NEVARIS_MANDANT|MANDANT|MANDANT_NR|ANZAHLUNGSVORGANG|MESSAGE_ID|DELIVERY_MODE")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .toD("http://" + nevarisHost + basePath +"/Company('${headers.NEVARIS_MANDANT}')/Deb_Anzahlungsvorgänge?$filter=Vorgang eq '${headers.ANZAHLUNGSVORGANG}' and Debitor_Kreditor eq 'Debitor'&clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .choice()
                    .when(httpStatusOk)
                        .unmarshal(nevarisAnzahlungsvorgangListFormat)
                        .process(exchange -> {
                            NevarisAnzahlungsvorgangList nevarisAnzahlungsVorgangList = exchange.getIn().getBody(NevarisAnzahlungsvorgangList.class);
                            if (nevarisAnzahlungsVorgangList.values().isEmpty()) {
                                exchange.getIn().setBody(null);
                            } else {
                                NevarisAnzahlungsvorgang nevarisAnzahlungsvorgang = nevarisAnzahlungsVorgangList.values().get(0);
                                exchange.getIn().setBody(nevarisAnzahlungsvorgang);
                            }
                        })
                .end();
        from("direct:createNevarisODataAnzahlungsvorgang")
                .routeId("createNevarisODataAnzahlungsvorgang")
                .process(new FibuToNevarisODataAnzahlungProcessor())
                .marshal(nevarisAnzahlungsvorgangFormat)
                .convertBodyTo(String.class, "UTF-8")
                .removeHeaders("*", "CamelRabbitmqDeliveryMode|NEVARIS_MANDANT|MANDANT|MANDANT_NR|ANZAHLUNGSVORGANG|MESSAGE_ID|DELIVERY_MODE")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .toD("http://" + nevarisHost + basePath +"/Company('${headers.NEVARIS_MANDANT}')/Deb_Anzahlungsvorgänge?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=POST")
                .streamCaching()
                .log(LoggingLevel.INFO,"log:createNevarisODataAnzahlungsvorgang?showHeaders=false")
                .log(LoggingLevel.DEBUG,"log:createNevarisODataAnzahlungsvorgang?showHeaders=true")
                .choice()
                    .when(httpStatusOk)
                        .unmarshal(nevarisAnzahlungsvorgangFormat)
                    .otherwise()
                        .throwException(Exception.class, "Fehler beim Anlegen des Anzahlungsvorgangs: Vorgang auf Deb_Anzahlungsvorgänge : ${headers.ANZAHLUNGSVORGANG}, Body: ${body}, Headers: ${headers} ")
                .end();


        from("direct:createNevarisDebAnzVorgKarte")
                .routeId("createNevarisDebAnzVorgkarte")
                .process(new FibuToNevarisODataDebAnzVorgKarteProcessor())
                .marshal(nevarisDebAnzVorgKarteFormat)
                .convertBodyTo(String.class, "UTF-8")
                .removeHeaders("*", "CamelRabbitmqDeliveryMode|NEVARIS_MANDANT|MANDANT|MANDANT_NR|ANZAHLUNGSVORGANG|MESSAGE_ID|DELIVERY_MODE")
                .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                .log(LoggingLevel.INFO,"Anzahlungsvorgangskarte: \n ${body}")
                .toD("http://" + nevarisHost + basePath +"/Company('${headers.NEVARIS_MANDANT}')/Deb_Anz_Vorg_Karte?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=POST")
                .streamCaching()
                .log(LoggingLevel.DEBUG,"createNevarisDebAnzKarteg?showHeaders=true")
                .choice()
                .when(httpStatusOk)
                .unmarshal(nevarisDebAnzVorgKarteFormat)
                .otherwise()
                .throwException(Exception.class, "Fehler beim Anlegen des Anzahlungsvorgangs auf Deb_Anz_Vorg_Karte: Vorgang: ${headers.ANZAHLUNGSVORGANG}, Body: ${body}, Headers: ${headers} ")
                .end();
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisFiBuImportODataErrorMessageTransformer("fibu","NEVARIS",originQueueName, mandantMapping);
    }
}
