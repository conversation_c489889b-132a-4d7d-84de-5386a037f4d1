package de.systeex.cip.nevaris.fibu.odata_producer.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude;


@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisFiBuODataRecord (
    @JsonProperty("abgrenzung")
        String abgrenzung,
    @JsonProperty("abwarten")
    String abwarten,
    @JsonProperty("abweichenderMandantKurzbez")
    String abweichenderMandantKurzbez,
    @JsonProperty("adressnr")
    String adressnr,
    @JsonProperty("anzVorgang")
    String anzVorgang,
    @JsonProperty("artikel")
    String artikel,
    @JsonProperty("ausgleichMitBelegart")
    String ausgleichMitBelegart,
    @JsonProperty("ausgleichMitBelegnr")
    String ausgleichMitBelegnr,
    @JsonProperty("ausgleichskonto")
    String ausgleichskonto,
    @JsonProperty("bas")
    String bas,
    @JsonProperty("bankkontoFRZahlungen")
    String bankkontoFRZahlungen,
    @JsonProperty("belegart")
    String belegart,
    @JsonProperty("belegartAnzVorgang")
    String belegartAnzVorgang,
    @JsonProperty("belegdatum")
    String belegdatum,
    @JsonProperty("belegnr")
    String belegnr,
    @JsonProperty("belegnrGutschriftsverfahren")
    String belegnrGutschriftsverfahren,
    @JsonProperty("belegnr2")
    String belegnr2,
    @JsonProperty("beschreibung")
    String beschreibung,
    @JsonProperty("buchungsdatum")
    String buchungsdatum,
    @JsonProperty("buchungsgruppe")
    String buchungsgruppe,
    @JsonProperty("debitorKreditorAnzVorgang")
    String debitorKreditorAnzVorgang,
    @JsonProperty("dokumentID")
    String dokumentID,
    @JsonProperty("externeBelegnr")
    String externeBelegnr,
    @JsonProperty("fLligAb")
    String fLligAb,
    @JsonProperty("fLligkeitsdatum")
    String fLligkeitsdatum,
    @JsonProperty("gsNrGutschriftsverfahren")
    String gsNrGutschriftsverfahren,
    @JsonProperty("gerT")
    String gerT,
    @JsonProperty("geschFtsbuchungsgruppe")
    String geschFtsbuchungsgruppe,
    @JsonProperty("habenbetrag")
    String habenbetrag,
    @JsonProperty("herkunftscode")
    String herkunftscode,
    @JsonProperty("infofeld")
    String infofeld,
    @JsonProperty("kontoart")
    String kontoart,
    @JsonProperty("kontonr")
    String kontonr,
    @JsonProperty("kostenart")
    String kostenart,
    @JsonProperty("kostenstelle")
    String kostenstelle,
    @JsonProperty("kostentrGer")
    String kostentrGer,
    @JsonProperty("lvPosition")
    String lvPosition,
    @JsonProperty("leistungsdatum")
    String leistungsdatum,
    @JsonProperty("mwStBetrag")
    String mwStBetrag,
    @JsonProperty("mwStGruppe")
    String mwStGruppe,
    @JsonProperty("mwStSatz")
    String mwStSatz,
    @JsonProperty("mahnstopp")
    String mahnstopp,
    @JsonProperty("mahnstufe")
    String mahnstufe,
    @JsonProperty("mandant")
    String mandant,
    @JsonProperty("menge")
    String menge,
    @JsonProperty("mengeneinheit")
    String mengeneinheit,
    @JsonProperty("niederlassung")
    String niederlassung,
    @JsonProperty("positionsnr")
    String positionsnr,
    @JsonProperty("produktbuchungsgruppe")
    String produktbuchungsgruppe,
    @JsonProperty("projektnr")
    String projektnr,
    @JsonProperty("referenznummer")
    String referenznummer,
    @JsonProperty("risikonr")
    String risikonr,
    @JsonProperty("schema")
    String schema,
    @JsonProperty("segment")
    String segment,
    @JsonProperty("skontierfHigerBetrag")
    String skontierfHigerBetrag,
    @JsonProperty("skonto")
    String skonto,
    @JsonProperty("skontoII")
    String skontoII,
    @JsonProperty("skontodatum")
    String skontodatum,
    @JsonProperty("skontodatumII")
    String skontodatumII,
    @JsonProperty("sollbetrag")
    String sollbetrag,
    @JsonProperty("verkEinkUfercode")
    String verkEinkUfercode,
    @JsonProperty("versicherungsgesellschaft")
    String versicherungsgesellschaft,
    @JsonProperty("wHrungscode")
    String wHrungscode,
    @JsonProperty("zahlungsform")
    String zahlungsform,
    @JsonProperty("zielkostenstelleBelegBertr")
    String zielkostenstelleBelegBertr,
    @JsonProperty("zielmandantBelegBertr")
    String zielmandantBelegBertr,
    @JsonProperty("zlgBedingungscode")
    String zlgBedingungscode,
    @JsonProperty("zusatzkennung")
    String zusatzkennung,
    @JsonProperty("abwAdressnr")
    String abwAdressnr,
    @JsonProperty("aenderungsbenutzer")
    String aenderungsbenutzer
) {
    
}
