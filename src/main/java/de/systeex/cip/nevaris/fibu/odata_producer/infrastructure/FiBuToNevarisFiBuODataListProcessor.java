package de.systeex.cip.nevaris.fibu.odata_producer.infrastructure;

import de.systeex.cip.nevaris.fibu.odata_producer.domain.NevarisFiBuODataRecord;
import de.systeex.cip.types.FiBuRecord;
import de.systeex.cip.types.FiBuRecordLine;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class FiBuToNevarisFiBuODataListProcessor implements Processor {

    private final DateTimeFormatter nevarisDateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    private final DecimalFormat nevarisDecimalFormatter = new DecimalFormat("###0.##", new DecimalFormatSymbols(Locale.GERMAN));

    @Override
    public void process(Exchange exchange) throws Exception {
        FiBuRecord fibu = exchange.getIn().getBody(FiBuRecord.class);

        List<NevarisFiBuODataRecord> nevarisFiBuODataRecordList = new ArrayList<>();
        boolean isAnzahlungsvorgang = fibu.anzahlungsvorgang() != null;

        for(FiBuRecordLine fiBuRecordLine: fibu.debitorBuchungen()) {
            NevarisFiBuODataRecord nevarisFiBuODataRecord = new NevarisFiBuODataRecord(
                    "",
                    "",
                    "",
                    "",
                    Objects.toString(exchange.getIn().getHeader("NEVARIS_ANZAHLUNGSVORGANG"), ""),
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.belegart(), ""),
                    isAnzahlungsvorgang ? "Anforderung" : "",
                    this.formatDate(fiBuRecordLine.belegdatum()),
                    Objects.toString(fiBuRecordLine.belegnummer(), ""),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.beschreibung(), ""),
                    this.formatDate(fiBuRecordLine.buchungsdatum()),
                    "",
                    isAnzahlungsvorgang ? "DEBITOR" : "",
                    Objects.toString(fiBuRecordLine.dokumentId(), ""),
                    Objects.toString(fiBuRecordLine.externeBelegnummer(), ""),
                    "",
                    this.formatDate(fiBuRecordLine.faelligkeitsdatum()),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.geschaeftsbuchungsgruppe(), ""),
                    formatDecimal(fiBuRecordLine.habenBetrag()),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.kontoart(), ""),
                    Objects.toString(fiBuRecordLine.kontonummer(), ""),
                    Objects.toString(fiBuRecordLine.kostenart(), ""),
                    Objects.toString(fiBuRecordLine.kostenstelle(), "").replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)),
                    "",
                    "",
                    "",
                    formatDecimal(fiBuRecordLine.mwStBetrag()),
                    Objects.toString(fiBuRecordLine.mwStGruppe(), ""),
                    Objects.toString(fiBuRecordLine.mwStSatz(), ""),
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.produktbuchungsgruppe(), ""),
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    formatDecimal(fiBuRecordLine.skontoProzent()),
                    formatDecimal(fiBuRecordLine.skontoProzent2()),
                    this.formatDate(fiBuRecordLine.skontodatum()),
                    this.formatDate(fiBuRecordLine.skontodatum2()),
                    formatDecimal(fiBuRecordLine.sollBetrag()),
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    null
            );

            nevarisFiBuODataRecordList.add(
                    nevarisFiBuODataRecord
            );
        }

        for(FiBuRecordLine fiBuRecordLine: fibu.umsatzBuchungen()) {
            NevarisFiBuODataRecord nevarisFiBuODataRecord = new NevarisFiBuODataRecord(
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.belegart(), ""),
                    "",
                    this.formatDate(fiBuRecordLine.belegdatum()),
                    Objects.toString(fiBuRecordLine.belegnummer(), ""),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.beschreibung(), ""),
                    this.formatDate(fiBuRecordLine.buchungsdatum()),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.dokumentId(), ""),
                    Objects.toString(fiBuRecordLine.externeBelegnummer(), ""),
                    "",
                    this.formatDate(fiBuRecordLine.faelligkeitsdatum()),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.geschaeftsbuchungsgruppe(), ""),
                    formatDecimal(fiBuRecordLine.habenBetrag()),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.kontoart(), ""),
                    Objects.toString(fiBuRecordLine.kontonummer(), ""),
                    Objects.toString(fiBuRecordLine.kostenart(), ""),
                    Objects.toString(fiBuRecordLine.kostenstelle(), "").replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)),
                    "",
                    "",
                    "",
                    formatDecimal(fiBuRecordLine.mwStBetrag()),
                    Objects.toString(fiBuRecordLine.mwStGruppe(), ""),
                    Objects.toString(fiBuRecordLine.mwStSatz(), ""),
                    "",
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.menge(), ""),
                    Objects.toString(fiBuRecordLine.mengenEinheit(), ""),
                    "",
                    "",
                    Objects.toString(fiBuRecordLine.produktbuchungsgruppe(), ""),
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    formatDecimal(fiBuRecordLine.sollBetrag()),
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    null
            );

            nevarisFiBuODataRecordList.add(
                    nevarisFiBuODataRecord
            );
        }
        exchange.getIn().setBody(nevarisFiBuODataRecordList);
    }

    private String formatDate(LocalDate date) {
        if (date == null) {
            return null;
        } else {
            return date.format(nevarisDateTimeFormatter);
        }
    }

    private String formatDecimal(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return "";
        }
        return nevarisDecimalFormatter.format(bigDecimal);
    }
}
