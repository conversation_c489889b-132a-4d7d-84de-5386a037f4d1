package de.systeex.cip.nevaris.fibu.csv_file_producer.infrastructure;

import de.systeex.cip.nevaris.fibu.csv_file_producer.domain.NevarisFiBuCSVAnzahlungsvorgangRecord;
import de.systeex.cip.types.FiBuRecord;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

public class FiBuToNevarisCSVAnzahlungsvorgangProcessor implements Processor {
    @Override
    public void process(Exchange exchange) throws Exception {
        FiBuRecord fiBuRecord = exchange.getIn().getBody(FiBuRecord.class);
        assert fiBuRecord.anzahlungsvorgang() != null;
        NevarisFiBuCSVAnzahlungsvorgangRecord anzahlungsvorgang = new NevarisFiBuCSVAnzahlungsvorgangRecord(
                fiBuRecord.anzahlungsvorgang().anzahlungsvorgang().replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)),
                fiBuRecord.anzahlungsvorgang().kontonummer(),
                fiBuRecord.anzahlungsvorgang().kostenstelle().replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)),
                fiBuRecord.anzahlungsvorgang().betragseingabe(),
                fiBuRecord.anzahlungsvorgang().buchungsgruppeFordAusAnz(),
                fiBuRecord.anzahlungsvorgang().buchungsgruppeAnz(),
                fiBuRecord.anzahlungsvorgang().geschaeftsbuchungsgruppe(),
                fiBuRecord.anzahlungsvorgang().produktbuchungsgruppe(),
                fiBuRecord.anzahlungsvorgang().mwstGeschaeftsbuchungsgruppe(),
                fiBuRecord.anzahlungsvorgang().mwstProduktbuchungsgruppe(),
                fiBuRecord.anzahlungsvorgang().art()
        );

        exchange.getIn().setHeader("NEVARIS_ANZAHLUNGSVORGANG", anzahlungsvorgang.getAnzahlungsvorgang());
        exchange.getIn().setBody(anzahlungsvorgang);
    }
}
