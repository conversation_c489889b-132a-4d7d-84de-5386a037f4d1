package de.systeex.cip.nevaris.fibu.csv_file_producer.infrastructure;

import de.systeex.cip.nevaris.fibu.csv_file_producer.domain.Steuerschluessel;
import de.systeex.cip.nevaris.fibu.csv_file_producer.domain.NevarisFiBuCSVRecord;
import de.systeex.cip.types.FiBuRecord;
import de.systeex.cip.types.FiBuRecordLine;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class FibuToNevarisFibuListProcessor implements Processor {

    private static final String SOLL_HABEN_SOLL = "S";
    private static final String SOLL_HABEN_HABEN = "H";
    private static final Map<String, Steuerschluessel> STEUERSCHLUESSEL_MAP = new HashMap<>();
    static {
        Steuerschluessel steuerschluessel = new Steuerschluessel();
        steuerschluessel.setMwstSatz("19");
        steuerschluessel.setBuchungsgruppe("DE");
        steuerschluessel.setGeschaeftsbuchungsgruppe("DE");
        steuerschluessel.setProduktbuchungsgruppe("LOH");
        steuerschluessel.setMwstGruppe("19");
        STEUERSCHLUESSEL_MAP.put("M19" + SOLL_HABEN_HABEN, steuerschluessel);
        steuerschluessel = new Steuerschluessel();
        steuerschluessel.setMwstSatz("7");
        steuerschluessel.setBuchungsgruppe("DE");
        steuerschluessel.setGeschaeftsbuchungsgruppe("DE");
        steuerschluessel.setProduktbuchungsgruppe("LOH");
        steuerschluessel.setMwstGruppe("7");
        STEUERSCHLUESSEL_MAP.put("M7" + SOLL_HABEN_HABEN, steuerschluessel);
        steuerschluessel = new Steuerschluessel();
        steuerschluessel.setMwstSatz("0");
        steuerschluessel.setBuchungsgruppe("DE");
        steuerschluessel.setGeschaeftsbuchungsgruppe("DE");
        steuerschluessel.setProduktbuchungsgruppe("LOH");
        steuerschluessel.setMwstGruppe("0");
        STEUERSCHLUESSEL_MAP.put("M0" + SOLL_HABEN_HABEN, steuerschluessel);

        steuerschluessel = new Steuerschluessel();
        steuerschluessel.setMwstSatz("19");
        steuerschluessel.setBuchungsgruppe("DE");
        steuerschluessel.setGeschaeftsbuchungsgruppe("DE");
        steuerschluessel.setProduktbuchungsgruppe("LOH");
        steuerschluessel.setMwstGruppe("19");
        STEUERSCHLUESSEL_MAP.put("V19" + SOLL_HABEN_SOLL, steuerschluessel);
        steuerschluessel = new Steuerschluessel();
        steuerschluessel.setMwstSatz("7");
        steuerschluessel.setBuchungsgruppe("DE");
        steuerschluessel.setGeschaeftsbuchungsgruppe("DE");
        steuerschluessel.setProduktbuchungsgruppe("LOH");
        steuerschluessel.setMwstGruppe("7");
        STEUERSCHLUESSEL_MAP.put("V7" + SOLL_HABEN_SOLL, steuerschluessel);
        steuerschluessel = new Steuerschluessel();
        steuerschluessel.setMwstSatz("0");
        steuerschluessel.setBuchungsgruppe("DE");
        steuerschluessel.setGeschaeftsbuchungsgruppe("DE");
        steuerschluessel.setProduktbuchungsgruppe("LOH");
        steuerschluessel.setMwstGruppe("0");
        STEUERSCHLUESSEL_MAP.put("V0" + SOLL_HABEN_SOLL, steuerschluessel);
    }
    private static final Map<String, String> GEGENBUCHUNG_MAP = new HashMap<>();
    static {
        GEGENBUCHUNG_MAP.put(SOLL_HABEN_SOLL, SOLL_HABEN_HABEN);
        GEGENBUCHUNG_MAP.put(SOLL_HABEN_HABEN, SOLL_HABEN_SOLL);
    }

    private final DecimalFormat nevarisDecimalFormatter = new DecimalFormat("###0.##", new DecimalFormatSymbols(Locale.GERMAN));
    @Override
    public void process(Exchange exchange) throws Exception {
        FiBuRecord fibu = exchange.getIn().getBody(FiBuRecord.class);
        List<NevarisFiBuCSVRecord> nevarisFiBuRecordList = new ArrayList<>();
        boolean isAnzahlungsvorgang = fibu.anzahlungsvorgang() != null;

        for(FiBuRecordLine fiBuRecordLine: fibu.debitorBuchungen()) {
            NevarisFiBuCSVRecord nevarisFiBuCSVRecord = new NevarisFiBuCSVRecord();
            nevarisFiBuCSVRecord.setBelegart(fiBuRecordLine.belegart());
            nevarisFiBuCSVRecord.setKontoart(fiBuRecordLine.kontoart());
            nevarisFiBuCSVRecord.setKontonr(fiBuRecordLine.kontonummer());
            nevarisFiBuCSVRecord.setKostenart(fiBuRecordLine.kostenart());
            nevarisFiBuCSVRecord.setBuchungsdatum(fiBuRecordLine.buchungsdatum());
            nevarisFiBuCSVRecord.setBelegnr(fiBuRecordLine.belegnummer());
            nevarisFiBuCSVRecord.setExterneBelegnr(fiBuRecordLine.externeBelegnummer());
            nevarisFiBuCSVRecord.setDokumentId(fiBuRecordLine.dokumentId());
            nevarisFiBuCSVRecord.setBelegdatum(fiBuRecordLine.belegdatum());
            nevarisFiBuCSVRecord.setBeschreibung(fiBuRecordLine.beschreibung());
            nevarisFiBuCSVRecord.setSollbetrag(formatDecimal(fiBuRecordLine.sollBetrag()));
            nevarisFiBuCSVRecord.setHabenbetrag(formatDecimal(fiBuRecordLine.habenBetrag()));
            nevarisFiBuCSVRecord.setMwstBetrag(formatDecimal(fiBuRecordLine.mwStBetrag()));
            nevarisFiBuCSVRecord.setSkontierfaehigerBetrag("");
            nevarisFiBuCSVRecord.setKostenstelle(Objects.toString(fiBuRecordLine.kostenstelle(), "").replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)));
            nevarisFiBuCSVRecord.setGeschaeftsbuchungsgruppe(fiBuRecordLine.geschaeftsbuchungsgruppe());
            nevarisFiBuCSVRecord.setProduktbuchungsgruppe(fiBuRecordLine.produktbuchungsgruppe());
            nevarisFiBuCSVRecord.setMwstGruppe(fiBuRecordLine.mwStGruppe());
            nevarisFiBuCSVRecord.setMwstSatz(fiBuRecordLine.mwStSatz());
            nevarisFiBuCSVRecord.setZlgBedingungscode("");
            nevarisFiBuCSVRecord.setSkontodatum(fiBuRecordLine.skontodatum());
            nevarisFiBuCSVRecord.setSkontoDatumII(fiBuRecordLine.skontodatum2());
            nevarisFiBuCSVRecord.setFaelligkeitsdatum(fiBuRecordLine.faelligkeitsdatum());
            nevarisFiBuCSVRecord.setSkontoProzent(formatDecimal(fiBuRecordLine.skontoProzent()));
            nevarisFiBuCSVRecord.setSkontoProzentII(formatDecimal(fiBuRecordLine.skontoProzent2()));
            nevarisFiBuCSVRecord.setDebitorKreditorAnzVorgang(isAnzahlungsvorgang ? "DEBITOR" : "");
            nevarisFiBuCSVRecord.setAnzVorgang(Objects.toString(exchange.getIn().getHeader("NEVARIS_ANZAHLUNGSVORGANG"), ""));
            nevarisFiBuCSVRecord.setBelegartAnzVorgang(isAnzahlungsvorgang ? "Anforderung" : "");

            nevarisFiBuRecordList.add(
                    nevarisFiBuCSVRecord
            );
        }

        for(FiBuRecordLine fiBuRecordLine: fibu.umsatzBuchungen()) {
            NevarisFiBuCSVRecord nevarisFiBuCSVRecord = new NevarisFiBuCSVRecord();
            nevarisFiBuCSVRecord.setBelegart(fiBuRecordLine.belegart());
            nevarisFiBuCSVRecord.setKontoart(fiBuRecordLine.kontoart());
            nevarisFiBuCSVRecord.setKontonr(fiBuRecordLine.kontonummer());
            nevarisFiBuCSVRecord.setKostenart(fiBuRecordLine.kostenart());
            nevarisFiBuCSVRecord.setBuchungsdatum(fiBuRecordLine.buchungsdatum());
            nevarisFiBuCSVRecord.setBelegnr(fiBuRecordLine.belegnummer());
            nevarisFiBuCSVRecord.setExterneBelegnr(fiBuRecordLine.externeBelegnummer());
            nevarisFiBuCSVRecord.setDokumentId(fiBuRecordLine.dokumentId());
            nevarisFiBuCSVRecord.setBelegdatum(fiBuRecordLine.belegdatum());
            nevarisFiBuCSVRecord.setBeschreibung(fiBuRecordLine.beschreibung());
            nevarisFiBuCSVRecord.setSollbetrag(formatDecimal(fiBuRecordLine.sollBetrag()));
            nevarisFiBuCSVRecord.setHabenbetrag(formatDecimal(fiBuRecordLine.habenBetrag()));
            nevarisFiBuCSVRecord.setMwstBetrag(formatDecimal(fiBuRecordLine.mwStBetrag()));
            nevarisFiBuCSVRecord.setSkontierfaehigerBetrag("");
            nevarisFiBuCSVRecord.setKostenstelle(Objects.toString(fiBuRecordLine.kostenstelle(), "").replace("{{MANDANT}}", exchange.getIn().getHeader("MANDANT_NR", String.class)));
            nevarisFiBuCSVRecord.setGeschaeftsbuchungsgruppe(fiBuRecordLine.geschaeftsbuchungsgruppe());
            nevarisFiBuCSVRecord.setProduktbuchungsgruppe(fiBuRecordLine.produktbuchungsgruppe());
            nevarisFiBuCSVRecord.setMwstGruppe(fiBuRecordLine.mwStGruppe());
            nevarisFiBuCSVRecord.setMwstSatz(fiBuRecordLine.mwStSatz());
            nevarisFiBuCSVRecord.setZlgBedingungscode("");
            nevarisFiBuCSVRecord.setMenge(fiBuRecordLine.menge());
            nevarisFiBuCSVRecord.setMengeneinheit(fiBuRecordLine.mengenEinheit());
            nevarisFiBuCSVRecord.setFaelligkeitsdatum(fiBuRecordLine.faelligkeitsdatum());

            nevarisFiBuRecordList.add(
                    nevarisFiBuCSVRecord
            );
        }

        exchange.getIn().setBody(nevarisFiBuRecordList);
    }

    private String formatDecimal(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return null;
        }
        return nevarisDecimalFormatter.format(bigDecimal);
    }
}
