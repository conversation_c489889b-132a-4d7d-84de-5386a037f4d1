package de.systeex.cip.nevaris.fibu.csv_file_producer.domain;

import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

import java.time.LocalDate;

@CsvRecord(separator = "|", generateHeaderColumns = false)
public class NevarisFiBuCSVRecord {
    @DataField(pos = 1, columnName = "Kontoart")
    private String kontoart;
    @DataField(pos = 2, columnName = "Kontonr.")
    private String kontonr;
    @DataField(pos = 3, columnName = "Buchungsdatum", pattern = "dd.MM.yyyy")
    private LocalDate buchungsdatum;
    @DataField(pos = 4, columnName = "Belegart")
    private String belegart;
    @DataField(pos = 5, columnName = "Belegnr.")
    private String belegnr;
    @DataField(pos = 6, columnName = "Externe Belegnr.")
    private String externeBelegnr;
    @DataField(pos = 7, columnName = "Belegdatum", pattern = "dd.MM.yyyy")
    private LocalDate belegdatum;
    @DataField(pos = 8, columnName = "Beschreibung")
    private String beschreibung;
    @DataField(pos = 9, columnName = "Zlg.-Bedingungscode")
    private String zlgBedingungscode;
    @DataField(pos = 10, columnName = "Fälligkeitsdatum", pattern = "dd.MM.yyyy")
    private LocalDate faelligkeitsdatum;
    @DataField(pos = 11, columnName = "Skontodatum", pattern = "dd.MM.yyyy")
    private LocalDate skontodatum;
    @DataField(pos = 12, columnName = "Skonto %")
    private String skontoProzent;
    @DataField(pos = 13, columnName = "Skontierfähiger Betrag")
    private String skontierfaehigerBetrag;
    @DataField(pos = 14, columnName = "Währungscode")
    private String waehrungscode;
    @DataField(pos = 15, columnName = "Sollbetrag")
    private String sollbetrag;
    @DataField(pos = 16, columnName = "Habenbetrag")
    private String habenbetrag;
    @DataField(pos = 17, columnName = "MWSt-Betrag")
    private String mwstBetrag;
    @DataField(pos = 18, columnName = "MWSt-Satz")
    private String mwstSatz;
    @DataField(pos = 19, columnName = "Buchungsgruppe")
    private String buchungsgruppe;
    @DataField(pos = 20, columnName = "Geschäftsbuchungsgruppe")
    private String geschaeftsbuchungsgruppe;
    @DataField(pos = 21, columnName = "Produktbuchungsgruppe")
    private String produktbuchungsgruppe;
    @DataField(pos = 22, columnName = "Menge")
    private String menge;
    @DataField(pos = 23, columnName = "Mengeneinheit")
    private String mengeneinheit;
    @DataField(pos = 24, columnName = "Kostenstelle")
    private String kostenstelle;
    @DataField(pos = 25, columnName = "Kostenträger")
    private String kostentraeger;
    @DataField(pos = 26, columnName = "Kostenart")
    private String kostenart;
    @DataField(pos = 27, columnName = "Gerät")
    private String geraet;
    @DataField(pos = 28, columnName = "Artikel")
    private String artikel;
    @DataField(pos = 29, columnName = "BAS")
    private String bas;
    @DataField(pos = 30, columnName = "LV-Position")
    private String lvPosition;
    @DataField(pos = 31, columnName = "Positionsnr.")
    private String positionsnr;
    @DataField(pos = 32, columnName = "Dokument-ID")
    private String dokumentId;
    @DataField(pos = 33, columnName = "Versicherungsgesellschaft")
    private String versicherungsgesellschaft;
    @DataField(pos = 34, columnName = "Risikonr.")
    private String risikonr;
    @DataField(pos = 35, columnName = "Verk.-/Einkäufercode")
    private String verkEinkaeufercode;
    @DataField(pos = 36, columnName = "Niederlassung")
    private String niederlassung;
    @DataField(pos = 37, columnName = "Projektnr.")
    private String projektnr;
    @DataField(pos = 38, columnName = "Abwarten")
    private String abwarten;
    @DataField(pos = 39, columnName = "Abweichender Mandant Kurzbez.")
    private String abweichenderMandantKurzbez;
    @DataField(pos = 40, columnName = "Belegnr2")
    private String belegnr2;
    @DataField(pos = 41, columnName = "Zusatzkennung")
    private String zusatzkennung;
    @DataField(pos = 42, columnName = "Abgrenzung")
    private String abgrenzung;
    @DataField(pos = 43, columnName = "Mahnstufe")
    private Integer mahnstufe;
    @DataField(pos = 44, columnName = "Mahnstopp")
    private String mahnstopp;
    @DataField(pos = 45, columnName = "Ausgleich mit Belegart")
    private String ausgleichMitBelegart;
    @DataField(pos = 46, columnName = "Ausgleich mit Belegnr.")
    private String ausgleichMitBelegnr;
    @DataField(pos = 47, columnName = "Zahlungsform")
    private String zahlungsform;
    @DataField(pos = 48, columnName = "Bankkonto für Zahlungen")
    private String bankkontoFuerZahlungen;
    @DataField(pos = 49, columnName = "Herkunftscode")
    private String herkunftscode;
    @DataField(pos = 50, columnName = "MWSt-Gruppe")
    private String mwstGruppe;
    @DataField(pos = 51, columnName = "Zielmandant Belegübertr.")
    private String zielmandantBeleguebertr;
    @DataField(pos = 52, columnName = "Zielkostenstelle Belegübertr.")
    private String zielkostenstelleBeleguebertr;
    @DataField(pos = 53, columnName = "Adressnr.")
    private String adressnr;
    @DataField(pos = 54, columnName = "Debitor/Kreditor Anz. Vorgang")
    private String debitorKreditorAnzVorgang;
    @DataField(pos = 55, columnName = "Anz. Vorgang")
    private String anzVorgang;
    @DataField(pos = 56, columnName = "Belegart Anz. Vorgang")
    private String belegartAnzVorgang;
    @DataField(pos = 57, columnName = "Mandant")
    private String mandant;
    @DataField(pos = 58, columnName = "abw. Adressnr.")
    private String abwAdressnr;
    @DataField(pos = 59, columnName = "Segment")
    private String segment;
    @DataField(pos = 60, columnName = "Skontodatum II", pattern = "dd.MM.yyyy")
    private LocalDate skontoDatumII;
    @DataField(pos= 61, columnName = "Skonto % II")
    private String skontoProzentII;
    @DataField(pos= 62, columnName = "Infofeld")
    private String infofeld;
    @DataField(pos= 63, columnName = "Leistungsdatum", pattern = "dd.MM.yyyy")
    private LocalDate leistungsdatum;


    public NevarisFiBuCSVRecord() {
    }

    public String getKontoart() {
        return kontoart;
    }

    public void setKontoart(String kontoart) {
        this.kontoart = kontoart;
    }

    public String getKontonr() {
        return kontonr;
    }

    public void setKontonr(String kontonr) {
        this.kontonr = kontonr;
    }

    public LocalDate getBuchungsdatum() {
        return buchungsdatum;
    }

    public void setBuchungsdatum(LocalDate buchungsdatum) {
        this.buchungsdatum = buchungsdatum;
    }

    public String getBelegart() {
        return belegart;
    }

    public void setBelegart(String belegart) {
        this.belegart = belegart;
    }

    public String getBelegnr() {
        return belegnr;
    }

    public void setBelegnr(String belegnr) {
        this.belegnr = belegnr;
    }

    public String getExterneBelegnr() {
        return externeBelegnr;
    }

    public void setExterneBelegnr(String externeBelegnr) {
        this.externeBelegnr = externeBelegnr;
    }

    public LocalDate getBelegdatum() {
        return belegdatum;
    }

    public void setBelegdatum(LocalDate belegdatum) {
        this.belegdatum = belegdatum;
    }

    public String getBeschreibung() {
        return beschreibung;
    }

    public void setBeschreibung(String beschreibung) {
        this.beschreibung = beschreibung;
    }

    public String getZlgBedingungscode() {
        return zlgBedingungscode;
    }

    public void setZlgBedingungscode(String zlgBedingungscode) {
        this.zlgBedingungscode = zlgBedingungscode;
    }

    public LocalDate getFaelligkeitsdatum() {
        return faelligkeitsdatum;
    }

    public void setFaelligkeitsdatum(LocalDate faelligkeitsdatum) {
        this.faelligkeitsdatum = faelligkeitsdatum;
    }

    public LocalDate getSkontodatum() {
        return skontodatum;
    }

    public void setSkontodatum(LocalDate skontodatum) {
        this.skontodatum = skontodatum;
    }

    public String getSkontoProzent() {
        return skontoProzent;
    }

    public void setSkontoProzent(String skontoProzent) {
        this.skontoProzent = skontoProzent;
    }

    public String getSkontierfaehigerBetrag() {
        return skontierfaehigerBetrag;
    }

    public void setSkontierfaehigerBetrag(String skontierfaehigerBetrag) {
        this.skontierfaehigerBetrag = skontierfaehigerBetrag;
    }

    public String getWaehrungscode() {
        return waehrungscode;
    }

    public void setWaehrungscode(String waehrungscode) {
        this.waehrungscode = waehrungscode;
    }

    public String getSollbetrag() {
        return sollbetrag;
    }

    public void setSollbetrag(String sollbetrag) {
        this.sollbetrag = sollbetrag;
    }

    public String getHabenbetrag() {
        return habenbetrag;
    }

    public void setHabenbetrag(String habenbetrag) {
        this.habenbetrag = habenbetrag;
    }

    public String getMwstBetrag() {
        return mwstBetrag;
    }

    public void setMwstBetrag(String mwstBetrag) {
        this.mwstBetrag = mwstBetrag;
    }

    public String getMwstSatz() {
        return mwstSatz;
    }

    public void setMwstSatz(String mwstSatz) {
        this.mwstSatz = mwstSatz;
    }

    public String getBuchungsgruppe() {
        return buchungsgruppe;
    }

    public void setBuchungsgruppe(String buchungsgruppe) {
        this.buchungsgruppe = buchungsgruppe;
    }

    public String getGeschaeftsbuchungsgruppe() {
        return geschaeftsbuchungsgruppe;
    }

    public void setGeschaeftsbuchungsgruppe(String geschaeftsbuchungsgruppe) {
        this.geschaeftsbuchungsgruppe = geschaeftsbuchungsgruppe;
    }

    public String getProduktbuchungsgruppe() {
        return produktbuchungsgruppe;
    }

    public void setProduktbuchungsgruppe(String produktbuchungsgruppe) {
        this.produktbuchungsgruppe = produktbuchungsgruppe;
    }

    public String getMenge() {
        return menge;
    }

    public void setMenge(String menge) {
        this.menge = menge;
    }

    public String getMengeneinheit() {
        return mengeneinheit;
    }

    public void setMengeneinheit(String mengeneinheit) {
        this.mengeneinheit = mengeneinheit;
    }

    public String getKostenstelle() {
        return kostenstelle;
    }

    public void setKostenstelle(String kostenstelle) {
        this.kostenstelle = kostenstelle;
    }

    public String getKostentraeger() {
        return kostentraeger;
    }

    public void setKostentraeger(String kostentraeger) {
        this.kostentraeger = kostentraeger;
    }

    public String getKostenart() {
        return kostenart;
    }

    public void setKostenart(String kostenart) {
        this.kostenart = kostenart;
    }

    public String getGeraet() {
        return geraet;
    }

    public void setGeraet(String geraet) {
        this.geraet = geraet;
    }

    public String getArtikel() {
        return artikel;
    }

    public void setArtikel(String artikel) {
        this.artikel = artikel;
    }

    public String getBas() {
        return bas;
    }

    public void setBas(String bas) {
        this.bas = bas;
    }

    public String getLvPosition() {
        return lvPosition;
    }

    public void setLvPosition(String lvPosition) {
        this.lvPosition = lvPosition;
    }

    public String getPositionsnr() {
        return positionsnr;
    }

    public void setPositionsnr(String positionsnr) {
        this.positionsnr = positionsnr;
    }

    public String getDokumentId() {
        return dokumentId;
    }

    public void setDokumentId(String dokumentId) {
        this.dokumentId = dokumentId;
    }

    public String getVersicherungsgesellschaft() {
        return versicherungsgesellschaft;
    }

    public void setVersicherungsgesellschaft(String versicherungsgesellschaft) {
        this.versicherungsgesellschaft = versicherungsgesellschaft;
    }

    public String getRisikonr() {
        return risikonr;
    }

    public void setRisikonr(String risikonr) {
        this.risikonr = risikonr;
    }

    public String getVerkEinkaeufercode() {
        return verkEinkaeufercode;
    }

    public void setVerkEinkaeufercode(String verkEinkaeufercode) {
        this.verkEinkaeufercode = verkEinkaeufercode;
    }

    public String getNiederlassung() {
        return niederlassung;
    }

    public void setNiederlassung(String niederlassung) {
        this.niederlassung = niederlassung;
    }

    public String getProjektnr() {
        return projektnr;
    }

    public void setProjektnr(String projektnr) {
        this.projektnr = projektnr;
    }

    public String getAbwarten() {
        return abwarten;
    }

    public void setAbwarten(String abwarten) {
        this.abwarten = abwarten;
    }

    public String getAbweichenderMandantKurzbez() {
        return abweichenderMandantKurzbez;
    }

    public void setAbweichenderMandantKurzbez(String abweichenderMandantKurzbez) {
        this.abweichenderMandantKurzbez = abweichenderMandantKurzbez;
    }

    public String getBelegnr2() {
        return belegnr2;
    }

    public void setBelegnr2(String belegnr2) {
        this.belegnr2 = belegnr2;
    }

    public String getZusatzkennung() {
        return zusatzkennung;
    }

    public void setZusatzkennung(String zusatzkennung) {
        this.zusatzkennung = zusatzkennung;
    }

    public String getAbgrenzung() {
        return abgrenzung;
    }

    public void setAbgrenzung(String abgrenzung) {
        this.abgrenzung = abgrenzung;
    }

    public Integer getMahnstufe() {
        return mahnstufe;
    }

    public void setMahnstufe(Integer mahnstufe) {
        this.mahnstufe = mahnstufe;
    }

    public String getMahnstopp() {
        return mahnstopp;
    }

    public void setMahnstopp(String mahnstopp) {
        this.mahnstopp = mahnstopp;
    }

    public String getAusgleichMitBelegart() {
        return ausgleichMitBelegart;
    }

    public void setAusgleichMitBelegart(String ausgleichMitBelegart) {
        this.ausgleichMitBelegart = ausgleichMitBelegart;
    }

    public String getAusgleichMitBelegnr() {
        return ausgleichMitBelegnr;
    }

    public void setAusgleichMitBelegnr(String ausgleichMitBelegnr) {
        this.ausgleichMitBelegnr = ausgleichMitBelegnr;
    }

    public String getZahlungsform() {
        return zahlungsform;
    }

    public void setZahlungsform(String zahlungsform) {
        this.zahlungsform = zahlungsform;
    }

    public String getBankkontoFuerZahlungen() {
        return bankkontoFuerZahlungen;
    }

    public void setBankkontoFuerZahlungen(String bankkontoFuerZahlungen) {
        this.bankkontoFuerZahlungen = bankkontoFuerZahlungen;
    }

    public String getHerkunftscode() {
        return herkunftscode;
    }

    public void setHerkunftscode(String herkunftscode) {
        this.herkunftscode = herkunftscode;
    }

    public String getMwstGruppe() {
        return mwstGruppe;
    }

    public void setMwstGruppe(String mwstGruppe) {
        this.mwstGruppe = mwstGruppe;
    }

    public String getZielmandantBeleguebertr() {
        return zielmandantBeleguebertr;
    }

    public void setZielmandantBeleguebertr(String zielmandantBeleguebertr) {
        this.zielmandantBeleguebertr = zielmandantBeleguebertr;
    }

    public String getZielkostenstelleBeleguebertr() {
        return zielkostenstelleBeleguebertr;
    }

    public void setZielkostenstelleBeleguebertr(String zielkostenstelleBeleguebertr) {
        this.zielkostenstelleBeleguebertr = zielkostenstelleBeleguebertr;
    }

    public String getAdressnr() {
        return adressnr;
    }

    public void setAdressnr(String adressnr) {
        this.adressnr = adressnr;
    }

    public String getDebitorKreditorAnzVorgang() {
        return debitorKreditorAnzVorgang;
    }

    public void setDebitorKreditorAnzVorgang(String debitorKreditorAnzVorgang) {
        this.debitorKreditorAnzVorgang = debitorKreditorAnzVorgang;
    }

    public String getAnzVorgang() {
        return anzVorgang;
    }

    public void setAnzVorgang(String anzVorgang) {
        this.anzVorgang = anzVorgang;
    }

    public String getBelegartAnzVorgang() {
        return belegartAnzVorgang;
    }

    public void setBelegartAnzVorgang(String belegartAnzVorgang) {
        this.belegartAnzVorgang = belegartAnzVorgang;
    }

    public String getMandant() {
        return mandant;
    }

    public void setMandant(String mandant) {
        this.mandant = mandant;
    }

    public String getAbwAdressnr() {
        return abwAdressnr;
    }

    public void setAbwAdressnr(String abwAdressnr) {
        this.abwAdressnr = abwAdressnr;
    }

    public String getSegment() {
        return segment;
    }

    public void setSegment(String segment) {
        this.segment = segment;
    }

    public LocalDate getSkontoDatumII() {
        return skontoDatumII;
    }

    public void setSkontoDatumII(LocalDate skontoDatumII) {
        this.skontoDatumII = skontoDatumII;
    }

    public String getSkontoProzentII() {
        return skontoProzentII;
    }

    public void setSkontoProzentII(String skontoProzentII) {
        this.skontoProzentII = skontoProzentII;
    }

    public String getInfofeld() {
        return infofeld;
    }

    public void setInfofeld(String infofeld) {
        this.infofeld = infofeld;
    }

    public LocalDate getLeistungsdatum() {
        return leistungsdatum;
    }

    public void setLeistungsdatum(LocalDate leistungsdatum) {
        this.leistungsdatum = leistungsdatum;
    }
}
