package de.systeex.cip.nevaris.routes;

import de.systeex.cip.nevaris.infrastructure.NevarisGetLastTimestampFromDatabaseErrorMessageTransformer;
import org.apache.camel.ExchangePattern;
import org.apache.camel.LoggingLevel;
import org.apache.camel.builder.PredicateBuilder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import static org.apache.camel.component.sql.SqlConstants.SQL_ROW_COUNT;

@Component
public class GetLastTimestampFromDatabaseRoute extends ErrorResistantRouteBuilder {
    @Override
    public void configure() throws Exception {
        super.configure();
        from("direct:getLastTimestampFromDatabase")
                .routeId("getLastTimestampFromDatabase")
                .log(LoggingLevel.INFO,"getLastTimestampFromDatabase")
                .toD("sql:SELECT timestamp, last_key FROM consumer_timestamps WHERE Entity = '${headers.NEVARIS_ENTITY}' AND mandant='${headers.MANDANT}'")
                .choice()
                    .when(PredicateBuilder.isEqualTo(header(SQL_ROW_COUNT), constant(0)))
                        .toD("sql:INSERT INTO consumer_timestamps (entity, nevaris_mandant, updated, timestamp, last_key, mandant) VALUES ('${headers.NEVARIS_ENTITY}', '${headers.NEVARIS_COMPANY_KEY}', '1900-01-01 00:00:00.000', '1900-01-01 00:00:000', '', '${headers.MANDANT}')")
                        .process(exchange -> {
                            Map<String, Object> map = new HashMap<>();
                            map.put("timestamp", "1900-01-01 00:00:00.000");
                            map.put("last_key", "");
                            exchange.getIn().setBody(map);
                    })
                    .endChoice()
                    .otherwise()
                    .process(exchange -> {
                        List<Map<String, Object>> list = exchange.getIn().getBody(ArrayList.class);
                        Map<String, Object> map = list.get(0);
                        exchange.getIn().setBody(map);
                    })
                .end()
                .setExchangePattern(ExchangePattern.InOnly);
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisGetLastTimestampFromDatabaseErrorMessageTransformer("InternalDatabase", "NEVARIS", "getLastTimestampFromDatabase", Map.of());
    }
}
