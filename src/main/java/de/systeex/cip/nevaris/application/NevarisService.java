package de.systeex.cip.nevaris.application;

import de.systeex.cip.nevaris.adressen.json_producer.NevarisAdressuebersicht;
import de.systeex.cip.nevaris.domain.odata.NevarisPLZCodeList;
import de.systeex.cip.nevaris.domain.odata.NevarisCostCenterList;

public interface NevarisService {

    NevarisPLZCodeList getPLZCodes(String mandant, String queryString);
    NevarisCostCenterList getKostenstellen(String mandant, String queryString);
    NevarisAdressuebersicht getAdressuebersichtByAdressnr(String mandant, String unique);
}
