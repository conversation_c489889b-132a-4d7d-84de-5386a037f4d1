package de.systeex.cip.nevaris.adressat.json_producer.routing;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatCache;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersichtListe;
import de.systeex.cip.nevaris.adressat.json_producer.infrastructure.MitarbeiterToNevarisAdressatenuebersichtProcessor;
import de.systeex.cip.nevaris.adressat.json_producer.infrastructure.NevarisAdressatErrorMessageTransformer;
import de.systeex.cip.nevaris.adressat.json_producer.infrastructure.WeitereMitarbeiterToNevarisAdressatenuebersichtProcessor;
import de.systeex.cip.types.Mitarbeiter;
import org.apache.camel.Exchange;
import org.apache.camel.Predicate;
import org.apache.camel.builder.PredicateBuilder;
import org.apache.camel.component.http.HttpConstants;
import org.apache.camel.component.jackson.JacksonDataFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import systeex.cip.error_handler.ErrorMessageTransformer;
import systeex.cip.error_handler.ErrorResistantRouteBuilder;

import java.util.ArrayList;
import java.util.Map;

@Component
public class NevarisAdressatImportJsonProducerRoute extends ErrorResistantRouteBuilder {

    @Value("${nevaris.host}")
    private String nevarisHost;

    @Value("${nevaris.default.basepath}")
    private String basePathDefault;

    @Value("#{${mandant.mapping}}")
    private Map<String, String> mandantMapToNevaris;

    private final String KONZERNADRESSE_PREFIX = "70000";

    private final Predicate createAdressat = PredicateBuilder.isNull(header("If-Match"));
    private final Predicate httpStatusOk = PredicateBuilder.and(PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final Predicate httpStatusNOk = PredicateBuilder.or(PredicateBuilder.isLessThan(header(Exchange.HTTP_RESPONSE_CODE), constant(200)), PredicateBuilder.isGreaterThanOrEqualTo(header(Exchange.HTTP_RESPONSE_CODE), constant(300)));
    private final String originQueueName = "nevaris-%s.adressat";

    @Override
    public void configure() throws Exception {
        super.configure();
        ObjectMapper mapper = JsonMapper.builder()
                .findAndAddModules()
                .build();
        JacksonDataFormat jacksonDataFormatMitarbeiter = new JacksonDataFormat(mapper, Mitarbeiter.class);
        JacksonDataFormat jacksonDataFormatNevarisAdressat = new JacksonDataFormat(mapper, NevarisAdressatCache.class);
        JacksonDataFormat jacksonDataFormatNevarisAdressatenuebersicht = new JacksonDataFormat(mapper, NevarisAdressatenuebersicht.class);
        JacksonDataFormat jacksonDataFormatNevarisAdressatenuebersichtListe = new JacksonDataFormat(mapper, NevarisAdressatenuebersichtListe.class);

        mandantMapToNevaris.keySet().forEach(mandant -> {
            String queueName = String.format(originQueueName, mandantMapToNevaris.get(mandant));

            from("rabbitmq:mitarbeiterdaten-anfragen?exchangeType=topic&routingKey=" + mandant + "&queue=" + queueName + "&declare=true&autoDelete=false&prefetchEnabled=true&prefetchCount=1&prefetchGlobal=false&prefetchSize=0&autoAck=false&deadLetterExchange=nevaris-dlx&deadLetterExchangeType=topic&deadLetterQueue=nevaris-" + mandantMapToNevaris.get(mandant) + ".adressat.dlq&deadLetterRoutingKey=nevaris-" + mandant + ".adressat&connectionFactory=#rabbitConnectionFactoryConfig")
                    .routeId("fetchAdressatFromBroker" + mandant)
                    .unmarshal(jacksonDataFormatMitarbeiter)
                    .process(exchange -> {
                        Mitarbeiter mitarbeiter = exchange.getIn().getBody(Mitarbeiter.class);
                        exchange.getIn().setHeader("MANDANT", mandant);
                        exchange.getIn().setHeader("NEVARIS_MANDANT", mandantMapToNevaris.get(mandant));
                        exchange.getIn().setHeader("ADRESSAT", mitarbeiter.personalnr());
                    })
                    .toD("sql:SELECT adress_nummer FROM mapping.adressnummern WHERE mandant = '${headers.MANDANT}'?outputType=SelectOne&OutputHeader=NEVARIS_ADRESS_NR")
                    .toD("sql:DELETE FROM mapping.adressaten WHERE adressnr = '${headers.NEVARIS_ADRESS_NR}' AND personalnr = '${body.personalnr}' AND mandant = '${headers.MANDANT}'")
                    .toD("sql:INSERT INTO mapping.adressaten(adressnr,personalnr,mandant) VALUES ('${headers.NEVARIS_ADRESS_NR}', '${body.personalnr}', '${headers.MANDANT}')")
                    .enrich("direct:updateStandardAdressat", (oldExchange, newExchange) -> oldExchange)
                    .enrich("direct:updateWeitereAdressaten", (oldExchange, newExchange) -> oldExchange)
                    .end();
        });

        from("direct:updateStandardAdressat")
                .routeId("updateStandardAdressat")
                .enrich("direct:adressatImportEnrichNevarisAdressat", (oldExchange, newExchange) -> {
                    NevarisAdressatenuebersicht nevarisAdressatenuebersicht = newExchange.getIn().getBody(NevarisAdressatenuebersicht.class);
                    if (nevarisAdressatenuebersicht != null) {
                        oldExchange.getIn().setHeader("If-Match", nevarisAdressatenuebersicht.odataEtag());
                    }
                    return oldExchange;
                })
                .choice()
                    .when(createAdressat)
                        .process(new MitarbeiterToNevarisAdressatenuebersichtProcessor())
                        .marshal(jacksonDataFormatNevarisAdressat)
                        .convertBodyTo(String.class, "UTF-8")
                        .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                        .toD("http://" + nevarisHost + basePathDefault + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=POST")
                        .convertBodyTo(String.class, "UTF-8")
                        .to("log:createAdressat?showHeaders=true")
                        .choice()
                            .when(httpStatusNOk)
                                .throwException(Exception.class, "Adressat anlegen nicht möglich: Adressnr: ${headers.NEVARIS_ADRESS_NR}, Adressat: ${headers.ADRESSAT},  Body: ${body}, Headers: ${headers} ")
                            .endChoice()
                    .endChoice()
                    .otherwise()
                        .process(new MitarbeiterToNevarisAdressatenuebersichtProcessor())
                        .marshal(jacksonDataFormatNevarisAdressatenuebersicht)
                        .convertBodyTo(String.class, "UTF-8")
                        .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                        .toD("http://" + nevarisHost + basePathDefault + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.NEVARIS_ADRESS_NR}','${headers.ADRESSAT}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=PATCH")
                        .convertBodyTo(String.class, "UTF-8")
                        .to("log:notCreateAdressat?showHeaders=true")
                        .choice()
                            .when(httpStatusNOk)
                                .throwException(Exception.class, "Adressat aktualisieren nicht möglich: Adressnr: ${headers.NEVARIS_ADRESS_NR}, Adressat: ${headers.ADRESSAT}, Body: ${body}, Headers: ${headers}")
                .endChoice()
                .end();

        from("direct:updateWeitereAdressaten")
                .routeId("updateWeitereAdressaten")
                .enrich("direct:getAlleAdressatenByAdressatFromNevaris", (oldExchange, newExchange) -> {
                    Mitarbeiter mitarbeiter = oldExchange.getIn().getBody(Mitarbeiter.class);
                    NevarisAdressatenuebersichtListe nevarisAdressatenuebersichtListe = newExchange.getIn().getBody(NevarisAdressatenuebersichtListe.class);
                    if (nevarisAdressatenuebersichtListe != null && mitarbeiter != null) {
                        oldExchange.getIn().setBody(nevarisAdressatenuebersichtListe.values().stream().filter(nevarisAdressatenuebersicht -> !(oldExchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class) != null && oldExchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class).startsWith(KONZERNADRESSE_PREFIX))).toList());
                        oldExchange.getIn().setHeader("MITARBEITER", mitarbeiter);
                    } else {
                        oldExchange.getIn().setBody(new ArrayList<NevarisAdressatenuebersicht>());
                    }
                    return oldExchange;
                })
                .split(body())
                    .setHeader("If-Match", simple("${body.odataEtag}"))
                    .process(new WeitereMitarbeiterToNevarisAdressatenuebersichtProcessor())
                    .marshal(jacksonDataFormatNevarisAdressatenuebersicht)
                    .convertBodyTo(String.class, "UTF-8")
                    .to("log:mylogger?showHeaders=true")
                    .removeHeaders("*", "NEVARIS_MANDANT|NEVARIS_ADRESS_NR|ADRESSAT|If-Match|MESSAGE_ID|DELIVERY_MODE")
                    .setHeader(HttpConstants.CONTENT_TYPE, constant("application/json;charset=utf-8"))
                    .toD("http://" + nevarisHost + basePathDefault + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.NEVARIS_ADRESS_NR}','${headers.ADRESSAT}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=PATCH")
                    .convertBodyTo(String.class, "UTF-8")
                    .to("log:updateWeitereAdressaten?showHeaders=true")
                    .choice()
                        .when(httpStatusNOk)
                            .throwException(Exception.class, "Adressat aktualisieren nicht möglich: Adressnr: ${headers.NEVARIS_ADRESS_NR}, Adressat: ${headers.ADRESSAT}, Body: ${body}, Headers: ${headers}")
                .end();

        from("direct:adressatImportEnrichNevarisAdressat")
                .routeId("adressatImportEnrichNevarisAdressat")
                .toD("http://" + nevarisHost + basePathDefault + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht('${headers.NEVARIS_ADRESS_NR}','${headers.ADRESSAT}')?clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:adressatImportEnrichNevarisAdressat?showHeaders=true")
                .choice()
                    .when(httpStatusOk)
                        .unmarshal(jacksonDataFormatNevarisAdressatenuebersicht)
                .end();

        from("direct:getAlleAdressatenByAdressatFromNevaris")
                .routeId("getAlleAdressatenByAdressatFromNevaris")
                .removeHeaders("Camel*","MESSAGE_ID|DELIVERY_MODE")
                .to("log:getAlleAdressatenByAdressatFromNevaris?showHeaders=true")
                .setHeader("PERSONALNR", simple("${body.personalnr}"))
                .toD("http://" + nevarisHost + basePathDefault + "/Company('${headers.NEVARIS_MANDANT}')/adressatenuebersicht?$filter=Adressat eq '${body.personalnr}'&clientBuilder=#nevarisODataHttpClientBuilder&throwExceptionOnFailure=false&httpMethod=GET")
                .streamCaching()
                .to("log:getAlleAdressatenByAdressatFromNevaris?showHeaders=true")
                .choice()
                    .when(httpStatusOk)
                        .unmarshal(jacksonDataFormatNevarisAdressatenuebersichtListe)
                    .otherwise()
                        .throwException(Exception.class, "Adressaten nicht gefunden: Personalnr: ${headers.PERSONALNR}, Body: ${body}, Headers: ${headers}")
                .endChoice()
                .end();
    }

    @Override
    protected ErrorMessageTransformer createErrorMessageTransformerInstance() {
        return new NevarisAdressatErrorMessageTransformer("Adressat", "NEVARIS", originQueueName, mandantMapToNevaris);
    }
}
