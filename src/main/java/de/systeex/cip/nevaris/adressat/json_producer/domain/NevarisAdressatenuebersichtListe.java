package de.systeex.cip.nevaris.adressat.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisAdressatenuebersichtListe(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("value")
        List<NevarisAdressatenuebersicht> values
) {
}
