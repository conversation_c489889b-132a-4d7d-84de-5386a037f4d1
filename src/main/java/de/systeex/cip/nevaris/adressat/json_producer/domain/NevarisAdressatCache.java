package de.systeex.cip.nevaris.adressat.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

public record NevarisAdressatCache(
        @JsonProperty("@odata.etag")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String odataEtag,
        @JsonProperty("Lfd_Nr")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        Integer lfdNr,
        @JsonProperty("Importiert")
        Boolean Importiert,
        @JsonProperty("CompanyName")
        String CompanyName,
        @JsonProperty("AdressNr")
        String AdressNr,
        @JsonProperty("Adressat")
        String Adressat,
        @JsonProperty("Titel")
        String Titel,
        @JsonProperty("Titel_im_Anschreiben")
        String TitelImAnschreiben,
        @JsonProperty("Anrede")
        String Anrede,
        @JsonProperty("Vorname")
        String Vorname,
        @JsonProperty("Name")
        String Name,
        @JsonProperty("Durchwahl")
        String Durchwahl,
        @JsonProperty("Fax")
        String Fax,
        @JsonProperty("Mobiltelefon")
        String Mobiltelefon,
        @JsonProperty("Funktion")
        String Funktion,
        @JsonProperty("E_Mail")
        String EMail,
        @JsonProperty("WWW_Adresse_URL")
        String WWWAdresseURL,
        @JsonProperty("Adressnr_privat")
        String AdressnrPrivat,
        @JsonProperty("Geburtsdatum")
        String Geburtsdatum,
        @JsonProperty("Raum")
        String Raum,
        @JsonProperty("Bundesland_Privatadresse")
        String BundeslandPrivatadresse,
        @JsonProperty("Abteilung")
        String Abteilung,
        @JsonProperty("Externe_Adressnr")
        String ExterneAdressnr,
        @JsonProperty("Telefonnr_kompl")
        String TelefonnrKompl,
        @JsonProperty("Faxnr_kompl")
        String FaxnrKompl,
        @JsonProperty("Infofeld")
        String Infofeld,
        @JsonProperty("Inaktiv")
        Boolean Inaktiv,
        @JsonProperty("Austrittsdatum")
        String Austrittsdatum,
        @JsonProperty("Sprache")
        String Sprache,
        @JsonProperty("SystemId")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String systemId
) {
    public static final String NULL_DATE = "0001-01-01";
}
