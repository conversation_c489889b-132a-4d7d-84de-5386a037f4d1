package de.systeex.cip.nevaris.adressat.json_producer.infrastructure;

import de.systeex.cip.nevaris.adressat.json_producer.domain.NevarisAdressatenuebersicht;
import de.systeex.cip.types.Mitarbeiter;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class MitarbeiterToNevarisAdressatenuebersichtProcessor implements Processor {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public void process(Exchange exchange) throws Exception {
        Mitarbeiter mitarbeiter = exchange.getIn().getBody(Mitarbeiter.class);

        String adressNummer = exchange.getIn().getHeader("NEVARIS_ADRESS_NR", String.class);
        if (adressNummer == null){
            throw new Exception("kein Mapping für die Adressnummer gefunden. Mandant: '" + exchange.getIn().getHeader("MANDANT", String.class) + "'");
        }

        NevarisAdressatenuebersicht nevarisAdressatenuebersicht = new NevarisAdressatenuebersicht(
                null,
                null,
                truncate(adressNummer, 10),
                truncate(mitarbeiter.personalnr(), 10),
                truncate("", 70),
                truncate(mitarbeiter.anrede(), 10),
                truncate(mitarbeiter.vorname(), 70),
                truncate(mitarbeiter.name(), 70),
                truncate(mitarbeiter.titel(), 20),
                truncate(mitarbeiter.titelImAnschreiben(), 50),
                truncate(mitarbeiter.durchwahl(), 20),
                truncate(mitarbeiter.fax(), 20),
                truncate(mitarbeiter.telefonnrKompl(), 100),
                truncate(mitarbeiter.faxnrKompl(), 100),
                truncate(mitarbeiter.mobiltelefon(), 20),
                truncate(mitarbeiter.zielFunktion(), 10),
                truncate(mitarbeiter.abteilung(), 10),
                this.truncate(mitarbeiter.mail(), 50),
                truncate(mitarbeiter.wwwAdresse(), 250),
                truncate(mitarbeiter.adressnrPrivat(), 10),
                this.parseDate(mitarbeiter.geburtsdatum()),
                truncate(mitarbeiter.raum(), 100),
                truncate(mitarbeiter.externeAdressnr(), 20),
                mitarbeiter.inaktiv(),
                this.parseDate(mitarbeiter.austrittsdatum()),
                truncate(mitarbeiter.sprache(), 10),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
                );

        exchange.getIn().setHeader("NEVARIS_ADRESS_NR", nevarisAdressatenuebersicht.adressnr());
        exchange.getIn().setHeader("ADRESSAT", nevarisAdressatenuebersicht.adressat());
        exchange.getIn().setHeader("ADRESSATEN_UEBERSICHT_ADRESSNR", nevarisAdressatenuebersicht.adressnr());
        exchange.getIn().setHeader("ADRESSATEN_UEBERSICHT_ADRESSAT", nevarisAdressatenuebersicht.adressat());
        exchange.getIn().setBody(nevarisAdressatenuebersicht);
    }

    private String truncate(String value, int length) {
        if (value == null)
            return null;

        return value.substring(0, Math.min(value.length(), length));
    }

    private boolean getInaktiv(LocalDate vertragsende) {
        return vertragsende != null && !vertragsende.isAfter(LocalDate.now());
    }

    private String parseDate(LocalDate datum) {
        if (datum == null) {
            return NevarisAdressatenuebersicht.NULL_DATE;
        } else {
            return datum.format(DATE_FORMATTER);
        }
    }
}
