package de.systeex.cip.nevaris.adressat.json_producer.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisAdressatenuebersicht(
        @JsonProperty("@odata.context")
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String odataEtag,
        @JsonProperty("Adressnr")
        String adressnr,// MaxLength="10">
        @JsonProperty("Adressat")
        String adressat, // MaxLength="10
        @JsonProperty("Adressbezeichnung_lang")
        @JsonIgnore
        String adressbezeichnungLang, // MaxLength="70">
        @JsonProperty("Anrede")
        String anrede, // MaxLength="10">
        @JsonProperty("Vorname")
        String vorname, // MaxLength="70">

        @JsonProperty("Name")
        String name, //MaxLength="70">
        @JsonProperty("Titel")
        String titel, // MaxLength="20">
        @JsonProperty("Titel_im_Anschreiben")
        String titelImAnschreiben, // MaxLength="50">
        @JsonProperty("Durchwahl")
        String durchwahl, // MaxLength="20">
        @JsonProperty("Fax")
        String fax, // MaxLength="20">
        @JsonProperty("Telefonnr_kompl")
        String telefonnrKompl, // MaxLength="100">
        @JsonProperty("Faxnr_kompl")
        String faxnrKompl, // MaxLength="100">
        @JsonProperty("Mobiltelefon")
        String mobiltelefon, // MaxLength="20">
        @JsonProperty("Funktion")
        String funktion, // MaxLength="10">
        @JsonProperty("Abteilung")
        String abteilung, // MaxLength="10">
        @JsonProperty("E_Mail")
        String eMail, // MaxLength="50">
        @JsonProperty("WWW_Adresse_URL")
        String wwwAdresseURL, //MaxLength="250">
        @JsonProperty("Adressnr_privat")
        String adressnrPrivat, // MaxLength="10">
        @JsonProperty("Geburtsdatum")
        String geburtsdatum,
        @JsonProperty("Raum")
        String raum, // MaxLength="100">
        @JsonProperty("Externe_Adressnr")
        String externeAdressnr, // MaxLength="20">
        @JsonProperty("Inaktiv")
        Boolean inaktiv,
        @JsonProperty("Austrittsdatum")
        String austrittsdatum,
        @JsonProperty("Sprache")
        String sprache, // MaxLength="10">
        @JsonProperty("Neuanlagesystem")
        @JsonIgnore
        String neuanlagesystem, // MaxLength="10">
        @JsonProperty("Neuanlagebenutzer")
        @JsonIgnore
        String neuanlagebenutzer, // MaxLength="50">
        @JsonProperty("Neuanlagedatum")
        @JsonIgnore
        String neuanlagedatum,
        @JsonProperty("Neuanlagezeit")
        @JsonIgnore
        String neuanlagezeit,
        @JsonProperty("Änderungssystem")
        String aenderungssystem, // MaxLength="10">
        @JsonProperty("Änderungsbenutzer")
        @JsonIgnore
        String aenderungsbenutzer, // MaxLength="50">
        @JsonProperty("Änderungsdatum")
        @JsonIgnore
        String aenderungsdatum,
        @JsonProperty("Änderungszeit")
        @JsonIgnore
        String aenderungszeit
) {
    public static final String NULL_DATE = "0001-01-01";
}
