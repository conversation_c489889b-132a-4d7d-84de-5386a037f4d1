package de.systeex.cip.nevaris.adressat.json_producer.infrastructure;
import systeex.cip.error_handler.ErrorMessageTransformer;
import de.systeex.cip.types.ErrSrcType;

import java.util.Map;

public class NevarisAdressatErrorMessageTransformer extends ErrorMessageTransformer {

    public NevarisAdressatErrorMessageTransformer(String module, String systemName, String originQueue, Map<String, String> systemMandantMapping) {
        super(module, systemName, originQueue, systemMandantMapping);
    }

    @Override
    protected ErrSrcType getSrcType() {
        return ErrSrcType.QUEUE;
    }
}
