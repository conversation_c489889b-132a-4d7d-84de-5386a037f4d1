package de.systeex.cip.nevaris.debitoren.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record NevarisDebitorenuebersicht(
//        <EntityType Name="debitorenuebersicht">
//        <Key>
//        <PropertyRef Name="No"/>
//        </Key>
        @JsonProperty("@odata.context")
        @Nullable
        @JsonIgnore
        String odataContext,
        @JsonProperty("@odata.etag")
        @Nullable
        @JsonIgnore
        String odataEtag,
        @JsonProperty("No")
        @Nonnull
        String no, // Type="Edm.String" Nullable="false" MaxLength="20">
        @JsonProperty("Markierung")
        @Nullable
        Boolean markierung, // Type="Edm.Boolean">
        @JsonProperty("Adressnr_des_Debitors")
        @Nullable
        String adressnrDesDebitors, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Name")
        @Nullable
        @JsonIgnore
        String name, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Search_Name")
        @Nullable
        String searchName, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Country_Region_Code")
        @Nullable
        String countryRegionCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("City")
        @Nullable
        String city, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Phone_No")
        @Nullable
        String phoneNo, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Fax_No")
        @Nullable
        String faxNo, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Contact")
        @Nullable
        String contact, // Type="Edm.String" MaxLength="100">
        @JsonProperty("VAT_Registration_No")
        @Nullable
        String vatRegistrationNo, // Type="Edm.String" MaxLength="20">
        @JsonProperty("USt_IdNr_Betriebsstätte")
        @Nullable
        String ustIdNrBetriebsstaette, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Saldo_II")
        @Nullable
        @JsonIgnore
        String saldoII, // Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("Currency_Code")
        @Nullable
        String currencyCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Payment_Terms_Code")
        @Nullable
        String paymentTermsCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Fälliger_Saldo_II")
        @Nullable
        @JsonIgnore
        String faelligerSaldoII, // Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("Reminder_Terms_Code")
        @Nullable
        String reminderTermsCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Kundengruppe")
        @Nullable
        String kundengruppe, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Leistungsempfänger")
        @Nullable
        Boolean leistungsempfaenger, // Type="Edm.Boolean">
        @JsonProperty("Bauabzugssteuer_Adressnr")
        @Nullable
        @JsonIgnore
        String bauabzugssteuerAdressnr, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Freistellung_LE_von")
        @Nullable
        @JsonIgnore
        String freistellungLeVon, // Type="Edm.Date">
        @JsonProperty("Freistellung_LE_bis")
        @Nullable
        @JsonIgnore
        String freistellungLeBis, // Type="Edm.Date">
        @JsonProperty("Customer_Posting_Group")
        @Nullable
        String customerPostingGroup, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Gen_Bus_Posting_Group")
        @Nullable
        String genBusPostingGroup, // Type="Edm.String" MaxLength="20">
        @JsonProperty("VAT_Bus_Posting_Group")
        @Nullable
        String vatBusPostingGroup, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Payment_Method_Code")
        @Nullable
        String paymentMethodCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Aktuelles_Bankkonto")
        @Nullable
        String aktuellesBankkonto, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Steuernummer")
        @Nullable
        String steuernummer, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Zielmandant_Belegübertragung")
        @Nullable
        String zielmandantBeleguebertragung, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Zielkreditor_Belegübertragung")
        @Nullable
        String zielkreditorBeleguebertragung, // Type="Edm.String" MaxLength="20">
        @JsonProperty("LeitwegID")
        @Nullable
        String leitwegId, // Type="Edm.String" MaxLength="46">
        @JsonProperty("Document_Sending_Profile")
        @Nullable
        String documentSendingProfile, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Datensatz_Gesperrt")
        @Nullable
        String datensatzGesperrt, // Type="Edm.String">
        @JsonProperty("Sperrhinweis")
        @Nullable
        String sperrhinweis, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Gültig_bis")
        @Nullable
        String gueltigBis, // Type="Edm.Date">
        @JsonProperty("Änderungsbenutzer")
        @Nullable
        @JsonIgnore
        String aenderungsbenutzer, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Änderungsdatum")
        @Nullable
        @JsonIgnore
        String aenderungsdatum, // Type="Edm.Date">
        @JsonProperty("Versicherung")
        @Nullable
        String versicherung, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Risikonr")
        @Nullable
        String risikonr, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Trab_Debitor")
        @Nullable
        Boolean trabDebitor, // Type="Edm.Boolean">
        @JsonProperty("Ist_Niederlassung")
        @Nullable
        @JsonIgnore
        Boolean istNiederlassung, // Type="Edm.Boolean">
        @JsonProperty("Konto_pro_Diverse")
        @Nullable
        @JsonIgnore
        Boolean kontoProDiverse, // Type="Edm.Boolean">
        @JsonProperty("Steuernummer_IT")
        @Nullable
        String steuernummerIT, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Address")
        @Nullable
        String address, // Type="Edm.String" MaxLength="100">
        @JsonProperty("Address_2")
        @Nullable
        String address2, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Post_Code")
        @Nullable
        String postCode, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Salesperson_Code")
        @Nullable
        String salespersonCode, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Home_Page")
        @Nullable
        String homePage, // Type="Edm.String" MaxLength="80">
        @JsonProperty("E_Mail")
        @Nullable
        String eMail, // Type="Edm.String" MaxLength="80">
        @JsonProperty("Our_Account_No")
        @Nullable
        String ourAccountNo, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Bill_to_Customer_No")
        @Nullable
        String billToCustomer_No, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Rech_an_abw_Adressnr")
        @Nullable
        String rechAnAbwAdressnr, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Öffentlicher_Auftraggeber")
        @Nullable
        Boolean oeffentlicherAuftraggeber, // Type="Edm.Boolean">
        @JsonProperty("Global_Dimension_1_Code")
        @Nullable
        String globalDimension1Code, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Global_Dimension_2_Code")
        @Nullable
        String globalDimension2Code, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Bankkonto")
        @Nullable
        String bankkonto, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Credit_Limit_LCY")
        @Nullable
        BigDecimal creditLimitLCY, // Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("Location_Code")
        @Nullable
        String locationCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Customer_Price_Group")
        @Nullable
        String customerPriceGroup, // Type="Edm.String" MaxLength="10">
        @JsonProperty("DR_Prozent")
        @Nullable
        BigDecimal drProzent, // Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("DR_Fälligkeitsformel")
        @Nullable
        String drFaelligkeitsformel, // Type="Edm.String">
        @JsonProperty("Fin_Charge_Terms_Code")
        @Nullable
        String finChargeTermsCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Language_Code")
        @Nullable
        String languageCode, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Combine_Shipments")
        @Nullable
        Boolean combineShipments, // Type="Edm.Boolean">
        @JsonProperty("Gültig_ab")
        @Nullable
        String gueltigab, // Type="Edm.Date">
        @JsonProperty("Notleidend")
        @Nullable
        Boolean notleidend, // Type="Edm.Boolean">
        @JsonProperty("Debitorenzuschlagsgruppe")
        @Nullable
        String debitorenzuschlagsgruppe, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Saldo_MW_II")
        @Nullable
        @JsonIgnore
        String saldoMwII, // Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("Fälliger_Saldo_MW_II")
        @JsonIgnore
        BigDecimal faelligerSaldoMwII, // Type="Edm.Decimal" Scale="Variable">
        @JsonProperty("Bankname")
        @Nullable
        String bankname, // Type="Edm.String" MaxLength="100">
        @JsonProperty("BLZ")
        @Nullable
        String bLZ, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Bankkontonr")
        @Nullable
        String bankkontonr, // Type="Edm.String" MaxLength="30">
        @JsonProperty("Versicherbarkeit")
        @Nullable
        String versicherbarkeit, // Type="Edm.String">
        @JsonProperty("Adressnr_Avise")
        @Nullable
        String adressnrAvise, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Adressnr_Mahnungen")
        @Nullable
        String adressnrMahnungen, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Dtaus_Fülldefinition")
        @Nullable
        String dtAusFuelldefinition, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Provisionsberechtigter")
        @Nullable
        String provisionsberechtigter, // Type="Edm.String" MaxLength="20">
        @JsonProperty("SWIFT_Code")
        @Nullable
        @JsonIgnore
        String swiftCode, // Type="Edm.String" MaxLength="20">
        @JsonProperty("IBAN")
        @Nullable
        @JsonIgnore
        String iban, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Anzahl_Posten_pro_Zahlsatz")
        @Nullable
        Integer anzahlPostenproZahlsatz, // Type="Edm.Int32">
        @JsonProperty("max_Anzahl_Zahlsätze")
        @Nullable
        String maxAnzahlZahlsaetze, // Type="Edm.String" MaxLength="3">
        @JsonProperty("Verwendungszweck_SEPA")
        @Nullable
        @JsonIgnore
        String verwendungszweckSEPA, // Type="Edm.String" MaxLength="140">
        @JsonProperty("Mandatreferenz")
        @Nullable
        @JsonIgnore
        String mandatreferenz, // Type="Edm.String" MaxLength="35">
        @JsonProperty("Überarbeiten")
        @Nullable
        Boolean ueberarbeiten, // Type="Edm.Boolean">
        @JsonProperty("ESR_System")
        @Nullable
        String esrSystem, // Type="Edm.String">
        @JsonProperty("ZUGFeRD_Rechnungen")
        @Nullable
        Boolean zUGFeRDRechnungen, // Type="Edm.Boolean">
        @JsonProperty("Name_lang")
        @Nullable
        @JsonIgnore
        String nameLang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse_lang")
        @Nullable
        @JsonIgnore
        String adresseLang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Adresse_2_lang")
        @Nullable
        String adresse2lang, // Type="Edm.String" MaxLength="70">
        @JsonProperty("Neuanlagesystem")
        @Nullable
        @JsonIgnore
        String neuanlagesystem, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Neuanlagebenutzer")
        @Nullable
        @JsonIgnore
        String neuanlagebenutzer, // Type="Edm.String" MaxLength="50">
        @JsonProperty("Neuanlagedatum")
        @Nullable
        @JsonIgnore
        String neuanlagedatum, // Type="Edm.Date">
        @JsonProperty("Neuanlagezeit")
        @Nullable
        @JsonIgnore
        String neuanlagezeit, // Type="Edm.String">
        @JsonProperty("Änderungssystem")
        @Nullable
        @JsonIgnore
        String aenderungssystem, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Änderungszeit")
        @Nullable
        @JsonIgnore
        String aenderungszeit, // Type="Edm.String">
        @JsonProperty("Global_Dimension_1_Filter")
        @Nullable
        String globalDimension1Filter, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Global_Dimension_2_Filter")
        @Nullable
        String globalDimension2Filter, // Type="Edm.String" MaxLength="20">
        @JsonProperty("Currency_Filter")
        @Nullable
        String currencyFilter, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Statistischfilter")
        @Nullable
        String statistischfilter, // Type="Edm.String">
        @JsonProperty("Niederlassungsfilter")
        @Nullable
        String niederlassungsfilter, // Type="Edm.String" MaxLength="10">
        @JsonProperty("DebitorenbuchungsgruppenFilter")
        @Nullable
        String debitorenbuchungsgruppenFilter, // Type="Edm.String" MaxLength="20">
        @JsonProperty("EinbehaltsartFilter")
        @Nullable
        String einbehaltsartFilter, // Type="Edm.String">
        @JsonProperty("Bauleiterfilter")
        @Nullable
        String bauleiterfilter, // Type="Edm.String" MaxLength="10">
        @JsonProperty("Date_Filter")
        @Nullable
        String dateFilter, // Type="Edm.String">
        @JsonProperty("Umsatzfilter")
        @Nullable
        @JsonIgnore
        String umsatzfilter // Type="Edm.String">

//        <NavigationProperty Name="Adressnr_des_Debitors_Link" Type="Collection(NAV.adressuebersicht)" ContainsTarget="true">
//                <ReferentialConstraint Property="Adressnr_des_Debitors" ReferencedProperty="Adressnr"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Country_Region_Code_Link" Type="Collection(NAV.Laender)" ContainsTarget="true">
//                <ReferentialConstraint Property="Country_Region_Code" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="City_Link" Type="Collection(NAV.PLZCodes)" ContainsTarget="true">
//                <ReferentialConstraint Property="City" ReferencedProperty="City"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Post_Code_Link" Type="Collection(NAV.PLZCodes)" ContainsTarget="true">
//                <ReferentialConstraint Property="Post_Code" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Rech_an_abw_Adressnr_Link" Type="Collection(NAV.adressuebersicht)" ContainsTarget="true">
//                <ReferentialConstraint Property="Rech_an_abw_Adressnr" ReferencedProperty="Adressnr"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Language_Code_Link" Type="Collection(NAV.Sprachen)" ContainsTarget="true">
//                <ReferentialConstraint Property="Language_Code" ReferencedProperty="Code"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Adressnr_Avise_Link" Type="Collection(NAV.adressuebersicht)" ContainsTarget="true">
//                <ReferentialConstraint Property="Adressnr_Avise" ReferencedProperty="Adressnr"/>
//                </NavigationProperty>
//                <NavigationProperty Name="Adressnr_Mahnungen_Link" Type="Collection(NAV.adressuebersicht)" ContainsTarget="true">
//                <ReferentialConstraint Property="Adressnr_Mahnungen" ReferencedProperty="Adressnr"/>
//                </NavigationProperty>
) {
        public static final String NULL_DATE = "0001-01-01";
}
